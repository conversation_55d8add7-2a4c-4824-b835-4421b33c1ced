<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuración Local - Control de Gastos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding: 2rem 0;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        .step {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
        }
        .step:last-child {
            border-bottom: none;
        }
        .step-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header text-center">
                        <h1 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Configuración Local XAMPP
                        </h1>
                        <p class="mb-0 mt-2">Control de Gastos - Configuración para Desarrollo</p>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        $step = $_GET['step'] ?? 1;
                        $action = $_POST['action'] ?? '';
                        
                        // Verificar conexión a MySQL
                        function checkMySQLConnection() {
                            try {
                                $pdo = new PDO("mysql:host=localhost", "root", "");
                                return true;
                            } catch (Exception $e) {
                                return false;
                            }
                        }
                        
                        // Crear base de datos
                        function createDatabase() {
                            try {
                                $pdo = new PDO("mysql:host=localhost", "root", "");
                                $pdo->exec("CREATE DATABASE IF NOT EXISTS control_gastos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                                return true;
                            } catch (Exception $e) {
                                return $e->getMessage();
                            }
                        }
                        
                        // Ejecutar migraciones
                        function runMigrations() {
                            try {
                                $pdo = new PDO("mysql:host=localhost;dbname=control_gastos", "root", "");
                                
                                // Leer archivos de migración
                                $migration_files = glob('database/migrations/*.sql');
                                sort($migration_files);
                                
                                $results = [];
                                foreach ($migration_files as $file) {
                                    $filename = basename($file);
                                    $sql = file_get_contents($file);
                                    
                                    // Dividir por statements
                                    $statements = explode(';', $sql);
                                    
                                    foreach ($statements as $statement) {
                                        $statement = trim($statement);
                                        if (!empty($statement)) {
                                            $pdo->exec($statement);
                                        }
                                    }
                                    
                                    $results[] = $filename;
                                }
                                
                                return $results;
                            } catch (Exception $e) {
                                return false;
                            }
                        }
                        
                        // Procesar acciones
                        if ($action === 'create_db') {
                            $result = createDatabase();
                            if ($result === true) {
                                echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>Base de datos creada exitosamente</div>";
                                $step = 3;
                            } else {
                                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>Error: $result</div>";
                            }
                        }
                        
                        if ($action === 'run_migrations') {
                            $result = runMigrations();
                            if ($result !== false) {
                                echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>Migraciones ejecutadas: " . implode(', ', $result) . "</div>";
                                $step = 4;
                            } else {
                                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>Error ejecutando migraciones</div>";
                            }
                        }
                        ?>
                        
                        <!-- Paso 1: Verificar XAMPP -->
                        <div class="step">
                            <div class="d-flex align-items-center mb-3">
                                <div class="step-number">1</div>
                                <h4 class="mb-0">Verificar XAMPP</h4>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Apache:</strong>
                                    <?php if (isset($_SERVER['SERVER_SOFTWARE'])): ?>
                                        <span class="status-ok"><i class="fas fa-check"></i> Funcionando</span>
                                    <?php else: ?>
                                        <span class="status-error"><i class="fas fa-times"></i> No detectado</span>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>MySQL:</strong>
                                    <?php if (checkMySQLConnection()): ?>
                                        <span class="status-ok"><i class="fas fa-check"></i> Conectado</span>
                                    <?php else: ?>
                                        <span class="status-error"><i class="fas fa-times"></i> No conectado</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <strong>PHP Version:</strong> <?= PHP_VERSION ?>
                                <?php if (version_compare(PHP_VERSION, '8.0.0', '>=')): ?>
                                    <span class="status-ok"><i class="fas fa-check"></i> Compatible</span>
                                <?php else: ?>
                                    <span class="status-error"><i class="fas fa-times"></i> Requiere PHP 8.0+</span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (!checkMySQLConnection()): ?>
                                <div class="alert alert-warning mt-3">
                                    <strong>⚠️ MySQL no está funcionando</strong><br>
                                    Asegúrate de que XAMPP esté iniciado y MySQL esté corriendo.
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info mt-3">
                                    <strong>💡 ¿Problemas con las tablas?</strong><br>
                                    Si tienes errores de columnas faltantes, usa el reparador de base de datos:
                                    <div class="mt-2">
                                        <a href="fix-database.php" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-tools me-1"></i>Reparar Base de Datos
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Paso 2: Crear Base de Datos -->
                        <div class="step">
                            <div class="d-flex align-items-center mb-3">
                                <div class="step-number">2</div>
                                <h4 class="mb-0">Crear Base de Datos</h4>
                            </div>
                            
                            <?php if (checkMySQLConnection()): ?>
                                <p>Se creará la base de datos <code>control_gastos</code> en MySQL.</p>
                                
                                <form method="POST">
                                    <input type="hidden" name="action" value="create_db">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-database me-2"></i>
                                        Crear Base de Datos
                                    </button>
                                </form>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    Primero debes iniciar MySQL en XAMPP
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Paso 3: Ejecutar Migraciones -->
                        <?php if ($step >= 3): ?>
                        <div class="step">
                            <div class="d-flex align-items-center mb-3">
                                <div class="step-number">3</div>
                                <h4 class="mb-0">Ejecutar Migraciones</h4>
                            </div>
                            
                            <p>Se ejecutarán las migraciones para crear las tablas necesarias.</p>
                            
                            <form method="POST">
                                <input type="hidden" name="action" value="run_migrations">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-play me-2"></i>
                                    Ejecutar Migraciones
                                </button>
                            </form>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Paso 4: Crear Usuario Admin -->
                        <?php if ($step >= 4): ?>
                        <div class="step">
                            <div class="d-flex align-items-center mb-3">
                                <div class="step-number">4</div>
                                <h4 class="mb-0">Crear Usuario Administrador</h4>
                            </div>
                            
                            <div class="alert alert-info">
                                <strong>¡Configuración completada!</strong><br>
                                Ahora puedes crear tu usuario administrador.
                            </div>
                            
                            <a href="create-admin-local.php" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>
                                Crear Usuario Admin
                            </a>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Paso 5: Acceder a la Aplicación -->
                        <?php if ($step >= 4): ?>
                        <div class="step">
                            <div class="d-flex align-items-center mb-3">
                                <div class="step-number">5</div>
                                <h4 class="mb-0">Acceder a la Aplicación</h4>
                            </div>
                            
                            <p>Una vez creado el usuario administrador, puedes acceder a la aplicación:</p>
                            
                            <div class="d-grid gap-2 d-md-flex">
                                <a href="public/" class="btn btn-success btn-lg">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    Ir a Control de Gastos
                                </a>
                                <a href="public/index.php?route=auth/login" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Página de Login
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                    </div>
                </div>
                
                <!-- Información adicional -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            URLs de Acceso Local
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Aplicación Principal:</strong><br>
                                <a href="public/" target="_blank">http://localhost/controlGastos/public/</a>
                            </div>
                            <div class="col-md-6">
                                <strong>phpMyAdmin:</strong><br>
                                <a href="http://localhost/phpmyadmin" target="_blank">http://localhost/phpmyadmin</a>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Panel XAMPP:</strong><br>
                                <a href="http://localhost/xampp" target="_blank">http://localhost/xampp</a>
                            </div>
                            <div class="col-md-6">
                                <strong>Logs de la App:</strong><br>
                                <code>logs/app.log</code>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Comandos útiles -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            Comandos Útiles para Desarrollo
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="code-block">
                            <strong>Reiniciar configuración:</strong><br>
                            <code>php setup-local.php</code>
                        </div>
                        
                        <div class="code-block">
                            <strong>Ver logs en tiempo real:</strong><br>
                            <code>tail -f logs/app.log</code>
                        </div>
                        
                        <div class="code-block">
                            <strong>Limpiar cache:</strong><br>
                            <code>rm -rf storage/cache/*</code>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</body>
</html>
