<?php
// La autenticación ya se verifica en el controlador
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-plus me-2"></i>Nueva Cuenta Bancaria
                    </h1>
                    <p class="text-muted">Registra una nueva cuenta de ahorros, corriente o empresarial</p>
                </div>
                <div>
                    <a href="/controlGastos/public/?route=bank-accounts" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Formulario -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-university me-2"></i>Información de la Cuenta
                    </h5>
                </div>
                <div class="card-body">
                    <form id="bankAccountForm" method="POST" action="/controlGastos/public/?route=bank-accounts/store">
                        <!-- Información Básica -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="account_name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Nombre de la Cuenta *
                                </label>
                                <input type="text" class="form-control" id="account_name" name="account_name" 
                                       placeholder="Ej: Cuenta Ahorros Principal" required maxlength="100">
                                <div class="form-text">Nombre personalizado para identificar tu cuenta</div>
                            </div>
                            <div class="col-md-6">
                                <label for="bank_name" class="form-label">
                                    <i class="fas fa-university me-1"></i>Banco o Entidad *
                                </label>
                                <select class="form-select" id="bank_id" name="bank_id" required>
                                    <option value="">Seleccionar banco...</option>
                                    <?php if (!empty($banks)): ?>
                                        <?php foreach ($banks as $bank): ?>
                                            <option value="<?= $bank['id'] ?>" data-color="<?= htmlspecialchars($bank['color'] ?? '') ?>">
                                                <?= htmlspecialchars($bank['bank_name']) ?>
                                                <?php if (!empty($bank['bank_code'])): ?>
                                                    (<?= htmlspecialchars($bank['bank_code']) ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <option value="" disabled>No hay bancos disponibles</option>
                                    <?php endif; ?>
                                </select>
                                <div class="form-text">
                                    Selecciona tu banco.
                                    <a href="?route=banks&action=create" target="_blank" class="text-primary">
                                        <i class="fas fa-plus"></i> Agregar nuevo banco
                                    </a>
                                </div>
                            </div>
                        </div>


                        <!-- Información de la Cuenta -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="account_type" class="form-label">
                                    <i class="fas fa-list me-1"></i>Tipo de Cuenta *
                                </label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">Seleccionar tipo...</option>
                                    <option value="savings">Cuenta de Ahorros</option>
                                    <option value="checking">Cuenta Corriente</option>
                                    <option value="business">Cuenta Empresarial</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="account_number" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Número de Cuenta
                                </label>
                                <input type="text" class="form-control" id="account_number" name="account_number" 
                                       placeholder="**********" maxlength="50">
                                <div class="form-text">Opcional, para identificación</div>
                            </div>
                            <div class="col-md-4">
                                <label for="debit_card_number" class="form-label">
                                    <i class="fas fa-credit-card me-1"></i>Últimos 4 Dígitos Débito
                                </label>
                                <input type="text" class="form-control" id="debit_card_number" name="debit_card_number" 
                                       placeholder="1234" maxlength="4" pattern="[0-9]{4}">
                                <div class="form-text">De la tarjeta débito</div>
                            </div>
                        </div>

                        <!-- Saldo Inicial -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="initial_balance" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>Saldo Inicial
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="initial_balance" name="initial_balance"
                                           placeholder="100000" min="0" step="0.01" max="999999999">
                                </div>
                                <div class="form-text">Saldo actual que tiene la cuenta (opcional)</div>
                            </div>
                            <div class="col-md-6">
                                <label for="description" class="form-label">
                                    <i class="fas fa-comment me-1"></i>Descripción
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="Descripción adicional de la cuenta..." maxlength="500"></textarea>
                            </div>
                        </div>

                        <!-- Botones -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Crear Cuenta
                                    </button>
                                    <a href="/controlGastos/public/?route=bank-accounts" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Cancelar
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Vista Previa -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Vista Previa
                    </h5>
                </div>
                <div class="card-body">
                    <div class="card bg-gradient text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h6 class="mb-0" id="preview_account_name">Nombre de la Cuenta</h6>
                                    <small id="preview_bank_name">Banco</small>
                                </div>
                                <div class="text-end">
                                    <i class="fas fa-university fa-2x opacity-75"></i>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-end">
                                <div>
                                    <small class="opacity-75">Saldo</small>
                                    <div id="preview_initial_balance">$0</div>
                                </div>
                                <div class="text-center">
                                    <small class="opacity-75">Tipo</small>
                                    <div id="preview_account_type">-</div>
                                </div>
                                <div class="text-end">
                                    <small class="opacity-75">Cuenta</small>
                                    <div id="preview_account_number">****</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Información adicional -->
                    <div class="mt-3">
                        <h6>Características:</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-2"></i>Movimientos ilimitados</li>
                            <li><i class="fas fa-check text-success me-2"></i>Transferencias entre cuentas</li>
                            <li><i class="fas fa-check text-success me-2"></i>Historial completo</li>
                            <li><i class="fas fa-check text-success me-2"></i>Reportes automáticos</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('bankAccountForm');
    const previewElements = {
        account_name: document.getElementById('preview_account_name'),
        bank_name: document.getElementById('preview_bank_name'),
        initial_balance: document.getElementById('preview_initial_balance'),
        account_type: document.getElementById('preview_account_type'),
        account_number: document.getElementById('preview_account_number')
    };

    // Gestión de banco seleccionado
    form.bank_id.addEventListener('change', function() {
        updatePreview();
    });

    // Actualizar vista previa en tiempo real
    function updatePreview() {
        const accountName = form.account_name.value || 'Nombre de la Cuenta';

        // Obtener nombre del banco seleccionado
        let bankName = 'Banco';
        const bankSelect = form.bank_id;
        if (bankSelect.value && bankSelect.selectedOptions.length > 0) {
            bankName = bankSelect.selectedOptions[0].textContent.trim();
        }

        const initialBalance = form.initial_balance.value ? '$' + parseInt(form.initial_balance.value).toLocaleString() : '$0';
        const accountType = form.account_type.value ?
            {'savings': 'Ahorros', 'checking': 'Corriente', 'business': 'Empresarial'}[form.account_type.value] : '-';
        const accountNumber = form.account_number.value ?
            '****' + form.account_number.value.slice(-4) : '****';

        previewElements.account_name.textContent = accountName;
        previewElements.bank_name.textContent = bankName;
        previewElements.initial_balance.textContent = initialBalance;
        previewElements.account_type.textContent = accountType;
        previewElements.account_number.textContent = accountNumber;
    }

    // Event listeners para vista previa
    ['account_name', 'bank_id', 'initial_balance', 'account_type', 'account_number'].forEach(field => {
        form[field].addEventListener('input', updatePreview);
        form[field].addEventListener('change', updatePreview);
    });

    // Formatear número de cuenta
    form.account_number.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '');
    });

    // Formatear número de tarjeta débito
    form.debit_card_number.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').slice(0, 4);
        
        if (this.value.length > 0 && this.value.length < 4) {
            this.setCustomValidity('Debe ingresar exactamente 4 dígitos');
        } else {
            this.setCustomValidity('');
        }
    });

    // Validación antes de enviar
    form.addEventListener('submit', function(e) {
        if (!form.bank_id.value) {
            e.preventDefault();
            alert('Por favor seleccione un banco');
            form.bank_id.focus();
            return;
        }
    });

    // Inicializar vista previa
    updatePreview();
});
</script>
