<?php

declare(strict_types=1);

namespace ControlGastos\Commands;

use ControlGastos\Core\Container;
use ControlGastos\Core\Logger;
use ControlGastos\Services\EmailService;

/**
 * Comando para procesar la cola de emails
 * Se ejecuta periódicamente para enviar emails pendientes
 */
class ProcessEmailQueueCommand
{
    private EmailService $emailService;
    private Logger $logger;
    private array $config;

    public function __construct(Container $container)
    {
        $this->emailService = $container->get('emailService');
        $this->logger = $container->get('logger');
        $this->config = [
            'batch_size' => (int) ($_ENV['EMAIL_BATCH_SIZE'] ?? 50),
            'max_execution_time' => (int) ($_ENV['EMAIL_MAX_EXECUTION_TIME'] ?? 300), // 5 minutos
            'memory_limit' => $_ENV['EMAIL_MEMORY_LIMIT'] ?? '256M'
        ];
    }

    /**
     * Ejecutar el comando
     */
    public function execute(array $args = []): int
    {
        $startTime = time();
        $this->logger->info('Iniciando procesamiento de cola de emails');

        try {
            // Configurar límites de ejecución
            $this->configureExecution();

            // Obtener parámetros
            $batchSize = (int) ($args['batch-size'] ?? $this->config['batch_size']);
            $maxBatches = (int) ($args['max-batches'] ?? 10);
            $dryRun = isset($args['dry-run']);

            if ($dryRun) {
                $this->logger->info('Modo dry-run activado - no se enviarán emails');
            }

            $totalProcessed = 0;
            $totalFailed = 0;
            $batchCount = 0;

            while ($batchCount < $maxBatches) {
                // Verificar tiempo de ejecución
                if (time() - $startTime > $this->config['max_execution_time']) {
                    $this->logger->warning('Tiempo máximo de ejecución alcanzado');
                    break;
                }

                // Verificar memoria
                if ($this->isMemoryLimitReached()) {
                    $this->logger->warning('Límite de memoria alcanzado');
                    break;
                }

                if ($dryRun) {
                    $result = $this->simulateProcessing($batchSize);
                } else {
                    $result = $this->emailService->processEmailQueue($batchSize);
                }

                $processed = $result['processed'] ?? 0;
                $failed = $result['failed'] ?? 0;

                if ($processed === 0 && $failed === 0) {
                    $this->logger->info('No hay más emails en la cola');
                    break;
                }

                $totalProcessed += $processed;
                $totalFailed += $failed;
                $batchCount++;

                $this->logger->info("Lote {$batchCount}: {$processed} procesados, {$failed} fallidos");

                // Pausa entre lotes para no sobrecargar el servidor SMTP
                if ($batchCount < $maxBatches && $processed > 0) {
                    sleep(2);
                }
            }

            $executionTime = time() - $startTime;
            $this->logger->info("Procesamiento completado: {$totalProcessed} emails enviados, {$totalFailed} fallidos en {$executionTime}s");

            // Limpiar datos antiguos si es necesario
            if (!$dryRun && $batchCount > 0) {
                $this->cleanupOldData();
            }

            return 0; // Éxito

        } catch (\Exception $e) {
            $this->logger->error('Error procesando cola de emails: ' . $e->getMessage(), [
                'exception' => $e,
                'execution_time' => time() - $startTime
            ]);
            return 1; // Error
        }
    }

    /**
     * Configurar límites de ejecución
     */
    private function configureExecution(): void
    {
        // Configurar límite de tiempo
        set_time_limit($this->config['max_execution_time']);

        // Configurar límite de memoria
        ini_set('memory_limit', $this->config['memory_limit']);

        // Configurar manejo de errores
        set_error_handler(function($severity, $message, $file, $line) {
            if (error_reporting() & $severity) {
                $this->logger->error("PHP Error: {$message} in {$file}:{$line}");
            }
        });
    }

    /**
     * Verificar si se alcanzó el límite de memoria
     */
    private function isMemoryLimitReached(): bool
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit($this->config['memory_limit']);
        
        return $memoryUsage > ($memoryLimit * 0.9); // 90% del límite
    }

    /**
     * Parsear límite de memoria
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $unit = strtolower(substr($limit, -1));
        $value = (int) substr($limit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $limit;
        }
    }

    /**
     * Simular procesamiento para dry-run
     */
    private function simulateProcessing(int $batchSize): array
    {
        // Simular obtención de emails pendientes
        $pendingEmails = $this->getPendingEmailsCount();
        
        if ($pendingEmails === 0) {
            return ['processed' => 0, 'failed' => 0];
        }

        $toProcess = min($batchSize, $pendingEmails);
        
        $this->logger->info("Simulando procesamiento de {$toProcess} emails");
        
        // Simular tiempo de procesamiento
        usleep(100000); // 0.1 segundos
        
        return [
            'processed' => $toProcess,
            'failed' => 0
        ];
    }

    /**
     * Obtener cantidad de emails pendientes
     */
    private function getPendingEmailsCount(): int
    {
        try {
            $query = "
                SELECT COUNT(*) as count 
                FROM email_queue 
                WHERE status = 'pending' 
                AND scheduled_at <= NOW()
            ";
            
            $result = $this->emailService->getDatabase()->selectOne($query);
            return (int) ($result['count'] ?? 0);

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo cantidad de emails pendientes: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Limpiar datos antiguos
     */
    private function cleanupOldData(): void
    {
        try {
            $this->logger->info('Iniciando limpieza de datos antiguos');
            
            $cleaned = $this->emailService->cleanupOldLogs(90);
            
            if ($cleaned > 0) {
                $this->logger->info("Limpieza completada: {$cleaned} registros eliminados");
            }

        } catch (\Exception $e) {
            $this->logger->error('Error en limpieza de datos: ' . $e->getMessage());
        }
    }

    /**
     * Mostrar estadísticas de la cola
     */
    public function showStats(): void
    {
        try {
            $stats = $this->emailService->getEmailStats(30);
            
            echo "=== Estadísticas de Email (últimos 30 días) ===\n";
            echo "Total enviados: " . ($stats['total_sent'] ?? 0) . "\n";
            echo "Total fallidos: " . ($stats['total_failed'] ?? 0) . "\n";
            
            if (!empty($stats['daily_stats'])) {
                echo "\n=== Estadísticas Diarias ===\n";
                foreach (array_slice($stats['daily_stats'], 0, 7) as $day) {
                    echo "{$day['date']}: {$day['count']} emails ({$day['status']})\n";
                }
            }

            // Mostrar emails pendientes
            $pending = $this->getPendingEmailsCount();
            echo "\nEmails pendientes: {$pending}\n";

        } catch (\Exception $e) {
            echo "Error obteniendo estadísticas: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Mostrar ayuda del comando
     */
    public static function showHelp(): void
    {
        echo "Comando: ProcessEmailQueueCommand\n";
        echo "Descripción: Procesa la cola de emails pendientes\n\n";
        echo "Uso:\n";
        echo "  php console.php email:process [opciones]\n\n";
        echo "Opciones:\n";
        echo "  --batch-size=N     Número de emails por lote (default: 50)\n";
        echo "  --max-batches=N    Número máximo de lotes (default: 10)\n";
        echo "  --dry-run          Simular procesamiento sin enviar emails\n";
        echo "  --stats            Mostrar estadísticas de la cola\n";
        echo "  --help             Mostrar esta ayuda\n\n";
        echo "Ejemplos:\n";
        echo "  php console.php email:process\n";
        echo "  php console.php email:process --batch-size=25 --max-batches=5\n";
        echo "  php console.php email:process --dry-run\n";
        echo "  php console.php email:process --stats\n";
    }
}

/**
 * Script de consola para ejecutar el comando
 */
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    require_once __DIR__ . '/../../vendor/autoload.php';
    require_once __DIR__ . '/../bootstrap.php';

    try {
        $container = new Container();
        $command = new ProcessEmailQueueCommand($container);

        // Parsear argumentos
        $args = [];
        for ($i = 1; $i < $argc; $i++) {
            $arg = $argv[$i];
            
            if ($arg === '--help') {
                ProcessEmailQueueCommand::showHelp();
                exit(0);
            } elseif ($arg === '--stats') {
                $command->showStats();
                exit(0);
            } elseif ($arg === '--dry-run') {
                $args['dry-run'] = true;
            } elseif (strpos($arg, '--batch-size=') === 0) {
                $args['batch-size'] = (int) substr($arg, 13);
            } elseif (strpos($arg, '--max-batches=') === 0) {
                $args['max-batches'] = (int) substr($arg, 14);
            }
        }

        $exitCode = $command->execute($args);
        exit($exitCode);

    } catch (\Exception $e) {
        echo "Error fatal: " . $e->getMessage() . "\n";
        exit(1);
    }
}
