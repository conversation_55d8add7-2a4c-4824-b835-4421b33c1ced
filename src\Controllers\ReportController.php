<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Session;
use ControlGastos\Core\SimpleDatabase;
use PDO;

/**
 * Controlador de reportes simplificado
 */
class ReportController
{
    private PDO $db;
    private Session $session;

    public function __construct(SimpleDatabase $database, Session $session)
    {
        $this->db = $database->getConnection();
        $this->session = $session;
    }

    /**
     * Dashboard principal de reportes
     */
    public function index(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        $this->render('reports/index', [
            'title' => 'Reportes',
            'financial_summary' => null,
            'category_report' => null,
            'account_report' => null,
            'trend_report' => null,
            'filters' => []
        ]);
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): void
    {
        extract($data);
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            // Vista de fallback para reportes
            echo "<div class='container mt-4'>";
            echo "<div class='alert alert-info'>";
            echo "<h4><span class='icon-emoji'>📊</span> Reportes</h4>";
            echo "<p>Este módulo está en desarrollo y estará disponible próximamente.</p>";
            echo "<p><strong>Estado del Desarrollo:</strong></p>";
            echo "<div class='alert alert-warning'>";
            echo "<p>Actualmente puedes probar el <strong>sistema de autenticación</strong> y el <strong>dashboard básico</strong>. Los demás módulos se implementarán gradualmente.</p>";
            echo "</div>";
            echo "<p><strong>Módulos Disponibles para Prueba:</strong></p>";
            echo "<div class='row'>";
            echo "<div class='col-md-6'>";
            echo "<div class='card border-success'>";
            echo "<div class='card-body'>";
            echo "<h5 class='card-title text-success'><span class='icon-emoji'>✅</span> Autenticación</h5>";
            echo "<p class='card-text'>Login, Registro, Logout</p>";
            echo "<a href='?route=auth/login' class='btn btn-success btn-sm'>Probar</a>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "<div class='col-md-6'>";
            echo "<div class='card border-success'>";
            echo "<div class='card-body'>";
            echo "<h5 class='card-title text-success'><span class='icon-emoji'>✅</span> Dashboard</h5>";
            echo "<p class='card-text'>Resumen básico de finanzas</p>";
            echo "<a href='?route=dashboard' class='btn btn-success btn-sm'>Probar</a>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "<div class='mt-3'>";
            echo "<a href='?route=dashboard' class='btn btn-primary me-2'>";
            echo "<span class='icon-emoji'>🏠</span> Volver al Dashboard";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
    }
}
