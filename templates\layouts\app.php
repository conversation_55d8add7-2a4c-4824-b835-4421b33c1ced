<!DOCTYPE html>
<html lang="es" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title ?? 'Control de Gastos') ?></title>
    
    <!-- Font Awesome 6 - Carga prioritaria -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">

    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Verificación y fallback de FontAwesome -->
    <script>
        // Verificar si FontAwesome se cargó correctamente
        function checkFontAwesome() {
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-home';
            testIcon.style.position = 'absolute';
            testIcon.style.left = '-9999px';
            testIcon.style.fontSize = '16px';
            document.body.appendChild(testIcon);

            const computedStyle = window.getComputedStyle(testIcon, ':before');
            const fontFamily = computedStyle.fontFamily;
            const content = computedStyle.content;

            document.body.removeChild(testIcon);

            // Verificar si FontAwesome está cargado
            const isLoaded = fontFamily && (
                fontFamily.includes('Font Awesome') ||
                fontFamily.includes('FontAwesome') ||
                (content && content !== 'none' && content !== '""')
            );

            if (!isLoaded) {
                console.warn('FontAwesome no detectado, forzando recarga...');
                // Crear un nuevo link para FontAwesome
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css';
                link.crossOrigin = 'anonymous';
                link.integrity = 'sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==';
                document.head.appendChild(link);

                // Verificar nuevamente después de un breve delay
                setTimeout(checkFontAwesome, 500);
            } else {
                console.log('FontAwesome cargado correctamente');
            }
        }

        // Ejecutar verificación cuando el DOM esté listo
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', checkFontAwesome);
        } else {
            checkFontAwesome();
        }

        // También verificar cuando la ventana termine de cargar
        window.addEventListener('load', function() {
            setTimeout(checkFontAwesome, 100);
        });
    </script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --sidebar-width: 280px;
            --header-height: 70px;
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        /* Header Styles */
        .main-header {
            height: var(--header-height);
            background: var(--primary-gradient);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1030;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            z-index: 1040;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar-brand {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-left: 4px solid #3498db;
        }

        .sidebar-nav .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
        }

        .sidebar-submenu {
            background-color: rgba(0,0,0,0.2);
        }

        .sidebar-submenu .nav-link {
            padding-left: 3rem;
            font-size: 0.9rem;
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            margin-top: var(--header-height);
            min-height: calc(100vh - var(--header-height));
            transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-collapsed {
            margin-left: 0;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-gradient-primary {
            background: var(--primary-gradient);
            color: white;
        }

        .card-gradient-success {
            background: var(--success-gradient);
            color: white;
        }

        .card-gradient-danger {
            background: var(--danger-gradient);
            color: white;
        }

        .card-gradient-warning {
            background: var(--warning-gradient);
            color: white;
        }

        .card-gradient-info {
            background: var(--info-gradient);
            color: white;
        }

        /* Buttons */
        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-gradient-primary {
            background: var(--primary-gradient);
            border: none;
            color: white;
        }

        .btn-gradient-success {
            background: var(--success-gradient);
            border: none;
            color: white;
        }

        /* Tables */
        .table {
            border-radius: 15px;
            overflow: hidden;
        }

        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        /* Financial specific styles */
        .amount-positive {
            color: #28a745;
            font-weight: 600;
        }

        .amount-negative {
            color: #dc3545;
            font-weight: 600;
        }

        .amount-neutral {
            color: #6c757d;
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }

        /* Loading spinner */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Custom scrollbar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        /* Sistema de iconos simplificado - Solo emojis para evitar duplicación */
        .icon-emoji {
            display: inline-block;
            width: 1.25em;
            text-align: center;
            font-size: 1em;
            margin-right: 0.5rem;
        }

        /* Ocultar FontAwesome para evitar duplicación */
        .fas {
            font-size: 0;
            width: 0;
            height: 0;
            overflow: hidden;
            display: none;
        }
    </style>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/img/favicon.ico">
</head>
<body>
    <!-- Loading Spinner -->
    <div id="loading-overlay" class="loading-overlay d-none">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden">Cargando...</span>
        </div>
    </div>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <!-- Brand -->
        <div class="sidebar-brand text-center">
            <h4 class="text-white mb-0">
                <span class="icon-emoji">💰</span>
                Control Gastos
            </h4>
            <small class="text-white-50">Gestión Financiera Personal</small>
        </div>

        <!-- Navigation -->
        <div class="sidebar-nav">
            <ul class="nav flex-column">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard">
                        <span class="icon-emoji">📊</span>
                        Dashboard
                    </a>
                </li>

                <!-- Transacciones -->
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="collapse" href="#transactionsMenu" role="button">
                        <span class="icon-emoji">💱</span>
                        Transacciones
                        <span class="ms-auto">⌄</span>
                    </a>
                    <div class="collapse sidebar-submenu" id="transactionsMenu">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="/transactions">
                                    <span class="icon-emoji">📋</span>Ver Todas
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/transactions/create">
                                    <span class="icon-emoji">➕</span>Nueva Transacción
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/transactions/import">
                                    <span class="icon-emoji">📤</span>Importar
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Gestión Financiera -->
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="collapse" href="#accountsMenu" role="button">
                        <span class="icon-emoji">🏛️</span>
                        Gestión Financiera
                        <span class="ms-auto">⌄</span>
                    </a>
                    <div class="collapse sidebar-submenu" id="accountsMenu">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="?route=accounts">
                                    <span class="icon-emoji">📈</span>Control Integrado
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="?route=banks">
                                    <span class="icon-emoji">🏛️</span>Bancos
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="?route=bank-accounts">
                                    <span class="icon-emoji">🐷</span>Cuentas Bancarias
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="?route=credit-cards">
                                    <span class="icon-emoji">💳</span>Tarjetas de Crédito
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Categorías -->
                <li class="nav-item">
                    <a class="nav-link" href="/categories">
                        <span class="icon-emoji">🏷️</span>
                        Categorías
                    </a>
                </li>

                <!-- Recordatorios -->
                <li class="nav-item">
                    <a class="nav-link" href="/reminders">
                        <span class="icon-emoji">🔔</span>
                        Recordatorios
                        <span class="badge bg-warning text-dark ms-auto">2</span>
                    </a>
                </li>

                <!-- Reportes -->
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="collapse" href="#reportsMenu" role="button">
                        <span class="icon-emoji">📊</span>
                        Reportes
                        <span class="ms-auto">⌄</span>
                    </a>
                    <div class="collapse sidebar-submenu" id="reportsMenu">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="?route=reports">
                                    <span class="icon-emoji">🥧</span>Resumen General
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="?route=reports&action=cash-flow">
                                    <span class="icon-emoji">💱</span>Flujo de Efectivo
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="?route=reports&action=category-expenses">
                                    <span class="icon-emoji">🏷️</span>Por Categorías
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="?route=reports&action=trends">
                                    <span class="icon-emoji">📈</span>Tendencias
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Administración (solo para admins) -->
                <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                <li class="nav-item mt-3">
                    <div class="px-3 py-2">
                        <small class="text-white-50 text-uppercase fw-bold">Administración</small>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="?route=backup">
                        <span class="icon-emoji">🛡️</span>
                        Backup & Seguridad
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/optimization">
                        <span class="icon-emoji">⚡</span>
                        Optimización
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </nav>

    <!-- Header -->
    <header class="main-header navbar navbar-expand-lg fixed-top">
        <div class="container-fluid">
            <!-- Sidebar toggle -->
            <button class="btn btn-link text-white d-md-none" type="button" id="sidebarToggle">
                <span class="icon-emoji">☰</span>
            </button>

            <!-- Page title -->
            <div class="navbar-brand text-white fw-bold d-none d-md-block">
                <?= htmlspecialchars($title ?? 'Dashboard') ?>
            </div>

            <!-- Header actions -->
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <!-- Quick Add Transaction -->
                <div class="nav-item me-3">
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#quickTransactionModal">
                        <span class="icon-emoji">➕</span>
                        <span class="d-none d-lg-inline ms-1">Nueva Transacción</span>
                    </button>
                </div>

                <!-- Notifications -->
                <div class="nav-item dropdown me-3">
                    <a class="nav-link text-white position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <span class="icon-emoji">🔔</span>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notification-count">
                            3
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow" style="width: 320px;">
                        <li><h6 class="dropdown-header">Notificaciones Recientes</h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item d-flex align-items-start py-3" href="#">
                                <span class="icon-emoji text-warning me-3 mt-1">⚠️</span>
                                <div>
                                    <div class="fw-semibold">Recordatorio vencido</div>
                                    <small class="text-muted">Pago de tarjeta de crédito</small>
                                    <div class="text-muted small">Hace 2 horas</div>
                                </div>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item d-flex align-items-start py-3" href="#">
                                <span class="icon-emoji text-info me-3 mt-1">📈</span>
                                <div>
                                    <div class="fw-semibold">Meta alcanzada</div>
                                    <small class="text-muted">Ahorro mensual completado</small>
                                    <div class="text-muted small">Ayer</div>
                                </div>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center py-2" href="/notifications">Ver todas las notificaciones</a></li>
                    </ul>
                </div>

                <!-- User menu -->
                <div class="nav-item dropdown">
                    <a class="nav-link text-white dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <span class="icon-emoji me-2" style="font-size: 1.5rem;">👤</span>
                        <span class="d-none d-lg-inline"><?= htmlspecialchars($_SESSION['user_name'] ?? 'Usuario') ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow">
                        <li><h6 class="dropdown-header">Mi Cuenta</h6></li>
                        <li><a class="dropdown-item" href="/profile"><span class="icon-emoji me-2">👤</span>Perfil</a></li>
                        <li><a class="dropdown-item" href="/settings"><span class="icon-emoji me-2">⚙️</span>Configuración</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/backup"><span class="icon-emoji me-2">🛡️</span>Seguridad</a></li>
                        <li><a class="dropdown-item" href="/optimization"><span class="icon-emoji me-2">⚡</span>Rendimiento</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/logout"><span class="icon-emoji me-2">🚪</span>Cerrar Sesión</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <div class="container-fluid p-4">
            <!-- Breadcrumb -->
            <?php if (isset($breadcrumb) && is_array($breadcrumb)): ?>
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard"><i class="fas fa-home"></i> Inicio</a></li>
                    <?php foreach ($breadcrumb as $item): ?>
                        <?php if (isset($item['url'])): ?>
                            <li class="breadcrumb-item"><a href="<?= $item['url'] ?>"><?= htmlspecialchars($item['title']) ?></a></li>
                        <?php else: ?>
                            <li class="breadcrumb-item active"><?= htmlspecialchars($item['title']) ?></li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
            <?php endif; ?>

            <!-- Flash Messages -->
            <?php if (isset($_SESSION['flash_success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($_SESSION['flash_success']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['flash_success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['flash_error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($_SESSION['flash_error']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['flash_error']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['flash_warning'])): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?= htmlspecialchars($_SESSION['flash_warning']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['flash_warning']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['flash_info'])): ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <?= htmlspecialchars($_SESSION['flash_info']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['flash_info']); ?>
            <?php endif; ?>

            <!-- Page Content -->
            <div class="fade-in-up">
                <?= $content ?>
            </div>
        </div>
    </main>

    <!-- Quick Transaction Modal -->
    <div class="modal fade" id="quickTransactionModal" tabindex="-1" aria-labelledby="quickTransactionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="quickTransactionModalLabel">
                        <i class="fas fa-plus me-2"></i>Nueva Transacción Rápida
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quickTransactionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quickType" class="form-label">Tipo</label>
                                    <select class="form-select" id="quickType" name="type" required>
                                        <option value="">Seleccionar tipo</option>
                                        <option value="income">Ingreso</option>
                                        <option value="expense">Egreso</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quickAmount" class="form-label">Monto</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="quickAmount" name="amount" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quickAccount" class="form-label">Cuenta</label>
                                    <select class="form-select" id="quickAccount" name="account_id" required>
                                        <option value="">Seleccionar cuenta</option>
                                        <!-- Se cargarán dinámicamente -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quickCategory" class="form-label">Categoría</label>
                                    <select class="form-select" id="quickCategory" name="category_id" required>
                                        <option value="">Seleccionar categoría</option>
                                        <!-- Se cargarán dinámicamente -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="quickDescription" class="form-label">Descripción</label>
                            <input type="text" class="form-control" id="quickDescription" name="description" placeholder="Descripción de la transacción">
                        </div>
                        <div class="mb-3">
                            <label for="quickDate" class="form-label">Fecha</label>
                            <input type="date" class="form-control" id="quickDate" name="transaction_date" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="submitQuickTransaction()">
                        <i class="fas fa-save me-1"></i>Guardar Transacción
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmationModalLabel">Confirmar Acción</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="confirmationModalBody">
                    ¿Está seguro de que desea realizar esta acción?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="confirmationModalConfirm">Confirmar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer">
        <!-- Los toasts se insertarán aquí dinámicamente -->
    </div>

    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Global variables
        let currentUser = <?= json_encode([
            'id' => $_SESSION['user_id'] ?? null,
            'name' => $_SESSION['user_name'] ?? null,
            'email' => $_SESSION['user_email'] ?? null,
            'is_admin' => $_SESSION['is_admin'] ?? false
        ]) ?>;

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            // Initialize sidebar
            initializeSidebar();

            // Initialize quick transaction modal
            initializeQuickTransaction();

            // Initialize tooltips
            initializeTooltips();

            // Set current date in quick transaction form
            document.getElementById('quickDate').value = new Date().toISOString().split('T')[0];

            // Load accounts and categories for quick transaction
            loadQuickTransactionData();

            // Initialize active nav link
            setActiveNavLink();
        }

        function initializeSidebar() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768) {
                    if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                }
            });
        }

        function initializeQuickTransaction() {
            const typeSelect = document.getElementById('quickType');
            const categorySelect = document.getElementById('quickCategory');

            if (typeSelect) {
                typeSelect.addEventListener('change', function() {
                    loadCategoriesByType(this.value);
                });
            }
        }

        function initializeTooltips() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        function setActiveNavLink() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && currentPath.startsWith(href) && href !== '/') {
                    link.classList.add('active');

                    // Expand parent menu if it's a submenu item
                    const parentCollapse = link.closest('.collapse');
                    if (parentCollapse) {
                        parentCollapse.classList.add('show');
                    }
                }
            });
        }

        async function loadQuickTransactionData() {
            try {
                // Load accounts
                const accountsResponse = await fetch('/api/accounts');
                const accountsData = await accountsResponse.json();

                if (accountsData.success) {
                    const accountSelect = document.getElementById('quickAccount');
                    accountSelect.innerHTML = '<option value="">Seleccionar cuenta</option>';

                    accountsData.accounts.forEach(account => {
                        const option = document.createElement('option');
                        option.value = account.id;
                        option.textContent = `${account.name} (${account.type_label})`;
                        accountSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading quick transaction data:', error);
            }
        }

        async function loadCategoriesByType(type) {
            if (!type) {
                document.getElementById('quickCategory').innerHTML = '<option value="">Seleccionar categoría</option>';
                return;
            }

            try {
                const response = await fetch(`/api/categories?type=${type}`);
                const data = await response.json();

                if (data.success) {
                    const categorySelect = document.getElementById('quickCategory');
                    categorySelect.innerHTML = '<option value="">Seleccionar categoría</option>';

                    data.categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        categorySelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        async function submitQuickTransaction() {
            const form = document.getElementById('quickTransactionForm');
            const formData = new FormData(form);

            // Show loading
            showLoading(true);

            try {
                const response = await fetch('/api/transactions', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('quickTransactionModal'));
                    modal.hide();

                    // Reset form
                    form.reset();
                    document.getElementById('quickDate').value = new Date().toISOString().split('T')[0];

                    // Show success message
                    showToast('Transacción creada exitosamente', 'success');

                    // Reload page if we're on transactions or dashboard
                    if (window.location.pathname === '/transactions' || window.location.pathname === '/dashboard') {
                        setTimeout(() => window.location.reload(), 1000);
                    }
                } else {
                    showToast(data.message || 'Error al crear la transacción', 'error');
                }
            } catch (error) {
                console.error('Error submitting transaction:', error);
                showToast('Error al crear la transacción', 'error');
            } finally {
                showLoading(false);
            }
        }

        function showLoading(show) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('d-none');
            } else {
                overlay.classList.add('d-none');
            }
        }

        function showToast(message, type = 'info', duration = 5000) {
            const toastContainer = document.getElementById('toastContainer');
            const toastId = 'toast-' + Date.now();

            const bgClass = {
                'success': 'bg-success',
                'error': 'bg-danger',
                'warning': 'bg-warning',
                'info': 'bg-info'
            }[type] || 'bg-info';

            const icon = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-triangle',
                'warning': 'fas fa-exclamation-circle',
                'info': 'fas fa-info-circle'
            }[type] || 'fas fa-info-circle';

            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="${icon} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: duration });
            toast.show();

            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        function showConfirmation(message, onConfirm, title = 'Confirmar Acción') {
            const modal = document.getElementById('confirmationModal');
            const modalTitle = document.getElementById('confirmationModalLabel');
            const modalBody = document.getElementById('confirmationModalBody');
            const confirmButton = document.getElementById('confirmationModalConfirm');

            modalTitle.textContent = title;
            modalBody.textContent = message;

            // Remove previous event listeners
            const newConfirmButton = confirmButton.cloneNode(true);
            confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

            // Add new event listener
            newConfirmButton.addEventListener('click', function() {
                onConfirm();
                bootstrap.Modal.getInstance(modal).hide();
            });

            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }

        // Utility functions
        function formatCurrency(amount) {
            return new Intl.NumberFormat('es-AR', {
                style: 'currency',
                currency: 'ARS'
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('es-AR');
        }

        function formatDateTime(dateString) {
            return new Date(dateString).toLocaleString('es-AR');
        }

        // CSRF Token handling
        function getCSRFToken() {
            const meta = document.querySelector('meta[name="csrf-token"]');
            return meta ? meta.getAttribute('content') : '';
        }

        // Add CSRF token to all AJAX requests
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (options.method && options.method.toUpperCase() !== 'GET') {
                options.headers = options.headers || {};
                options.headers['X-CSRF-Token'] = getCSRFToken();
            }
            return originalFetch(url, options);
        };
    </script>

    <!-- Page specific scripts -->
    <?php if (isset($scripts)): ?>
        <?php foreach ($scripts as $script): ?>
            <script src="<?= $script ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline scripts -->
    <?php if (isset($inlineScripts)): ?>
        <script>
            <?= $inlineScripts ?>
        </script>
    <?php endif; ?>
</body>
</html>
