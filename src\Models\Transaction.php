<?php

declare(strict_types=1);

namespace ControlGastos\Models;

/**
 * Modelo de Transacción
 * Representa una transacción financiera (ingreso o egreso)
 */
class Transaction
{
    private ?int $id = null;
    private int $userId;
    private int $accountId;
    private int $categoryId;
    private ?int $subcategoryId = null;
    private string $type;
    private float $amount;
    private string $description;
    private ?\DateTime $transactionDate = null;
    private ?string $reference = null;
    private ?string $notes = null;
    private array $tags = [];
    private bool $isRecurring = false;
    private ?string $recurringType = null;
    private ?int $recurringInterval = null;
    private ?\DateTime $recurringEndDate = null;
    private \DateTime $createdAt;
    private \DateTime $updatedAt;

    // Tipos de transacción
    public const TYPES = [
        'income' => 'Ingreso',
        'expense' => 'Egreso'
    ];

    // Tipos de recurrencia
    public const RECURRING_TYPES = [
        'daily' => 'Diario',
        'weekly' => 'Semanal',
        'monthly' => 'Mensual',
        'quarterly' => 'Trimestral',
        'yearly' => 'Anual'
    ];

    public function __construct(array $data = [])
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        $this->transactionDate = new \DateTime();
        
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    /**
     * Llenar modelo con datos
     */
    public function fill(array $data): void
    {
        if (isset($data['id'])) {
            $this->id = (int) $data['id'];
        }
        
        if (isset($data['user_id'])) {
            $this->userId = (int) $data['user_id'];
        }
        
        if (isset($data['account_id'])) {
            $this->accountId = (int) $data['account_id'];
        }
        
        if (isset($data['category_id'])) {
            $this->categoryId = (int) $data['category_id'];
        }
        
        if (isset($data['subcategory_id'])) {
            $this->subcategoryId = $data['subcategory_id'] ? (int) $data['subcategory_id'] : null;
        }
        
        if (isset($data['type'])) {
            $this->type = $data['type'];
        }
        
        if (isset($data['amount'])) {
            $this->amount = (float) $data['amount'];
        }
        
        if (isset($data['description'])) {
            $this->description = trim($data['description']);
        }
        
        if (isset($data['transaction_date'])) {
            $this->transactionDate = new \DateTime($data['transaction_date']);
        }
        
        if (isset($data['reference'])) {
            $this->reference = $data['reference'] ? trim($data['reference']) : null;
        }
        
        if (isset($data['notes'])) {
            $this->notes = $data['notes'] ? trim($data['notes']) : null;
        }
        
        if (isset($data['tags'])) {
            $this->tags = is_string($data['tags']) ? 
                array_filter(array_map('trim', explode(',', $data['tags']))) : 
                (array) $data['tags'];
        }
        
        if (isset($data['is_recurring'])) {
            $this->isRecurring = (bool) $data['is_recurring'];
        }
        
        if (isset($data['recurring_type'])) {
            $this->recurringType = $data['recurring_type'];
        }
        
        if (isset($data['recurring_interval'])) {
            $this->recurringInterval = $data['recurring_interval'] ? (int) $data['recurring_interval'] : null;
        }
        
        if (isset($data['recurring_end_date'])) {
            $this->recurringEndDate = $data['recurring_end_date'] ? new \DateTime($data['recurring_end_date']) : null;
        }
        
        if (isset($data['created_at'])) {
            $this->createdAt = new \DateTime($data['created_at']);
        }
        
        if (isset($data['updated_at'])) {
            $this->updatedAt = new \DateTime($data['updated_at']);
        }
    }

    /**
     * Convertir a array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->userId,
            'account_id' => $this->accountId,
            'category_id' => $this->categoryId,
            'subcategory_id' => $this->subcategoryId,
            'type' => $this->type,
            'amount' => $this->amount,
            'description' => $this->description,
            'transaction_date' => $this->transactionDate?->format('Y-m-d H:i:s'),
            'reference' => $this->reference,
            'notes' => $this->notes,
            'tags' => implode(',', $this->tags),
            'is_recurring' => $this->isRecurring,
            'recurring_type' => $this->recurringType,
            'recurring_interval' => $this->recurringInterval,
            'recurring_end_date' => $this->recurringEndDate?->format('Y-m-d H:i:s'),
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Convertir a array con información adicional
     */
    public function toDetailedArray(): array
    {
        return array_merge($this->toArray(), [
            'type_label' => self::TYPES[$this->type] ?? $this->type,
            'recurring_type_label' => $this->recurringType ? (self::RECURRING_TYPES[$this->recurringType] ?? $this->recurringType) : null,
            'amount_formatted' => $this->getFormattedAmount(),
            'transaction_date_formatted' => $this->transactionDate?->format('d/m/Y H:i'),
            'tags_array' => $this->tags,
            'is_income' => $this->isIncome(),
            'is_expense' => $this->isExpense()
        ]);
    }

    /**
     * Validar datos de la transacción
     */
    public function validate(): array
    {
        $errors = [];

        // Validar user_id
        if (empty($this->userId)) {
            $errors['user_id'] = 'El ID de usuario es requerido';
        }

        // Validar account_id
        if (empty($this->accountId)) {
            $errors['account_id'] = 'La cuenta es requerida';
        }

        // Validar category_id
        if (empty($this->categoryId)) {
            $errors['category_id'] = 'La categoría es requerida';
        }

        // Validar tipo
        if (empty($this->type)) {
            $errors['type'] = 'El tipo de transacción es requerido';
        } elseif (!array_key_exists($this->type, self::TYPES)) {
            $errors['type'] = 'Tipo de transacción inválido';
        }

        // Validar monto
        if (!isset($this->amount)) {
            $errors['amount'] = 'El monto es requerido';
        } elseif (!is_numeric($this->amount)) {
            $errors['amount'] = 'El monto debe ser un número válido';
        } elseif ($this->amount <= 0) {
            $errors['amount'] = 'El monto debe ser mayor a cero';
        }

        // Validar descripción
        if (empty($this->description)) {
            $errors['description'] = 'La descripción es requerida';
        } elseif (strlen($this->description) < 3) {
            $errors['description'] = 'La descripción debe tener al menos 3 caracteres';
        } elseif (strlen($this->description) > 255) {
            $errors['description'] = 'La descripción no puede exceder 255 caracteres';
        }

        // Validar fecha de transacción
        if (!$this->transactionDate) {
            $errors['transaction_date'] = 'La fecha de transacción es requerida';
        }

        // Validar referencia
        if ($this->reference && strlen($this->reference) > 100) {
            $errors['reference'] = 'La referencia no puede exceder 100 caracteres';
        }

        // Validar notas
        if ($this->notes && strlen($this->notes) > 1000) {
            $errors['notes'] = 'Las notas no pueden exceder 1000 caracteres';
        }

        // Validar recurrencia
        if ($this->isRecurring) {
            if (empty($this->recurringType)) {
                $errors['recurring_type'] = 'El tipo de recurrencia es requerido';
            } elseif (!array_key_exists($this->recurringType, self::RECURRING_TYPES)) {
                $errors['recurring_type'] = 'Tipo de recurrencia inválido';
            }

            if ($this->recurringInterval && $this->recurringInterval < 1) {
                $errors['recurring_interval'] = 'El intervalo de recurrencia debe ser mayor a cero';
            }
        }

        return $errors;
    }

    /**
     * Verificar si la transacción es válida
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }

    /**
     * Obtener monto formateado
     */
    public function getFormattedAmount(): string
    {
        $prefix = $this->isIncome() ? '+' : '-';
        return $prefix . '$ ' . number_format($this->amount, 2, '.', ',');
    }

    /**
     * Verificar si es ingreso
     */
    public function isIncome(): bool
    {
        return $this->type === 'income';
    }

    /**
     * Verificar si es egreso
     */
    public function isExpense(): bool
    {
        return $this->type === 'expense';
    }

    /**
     * Obtener monto con signo para cálculos
     */
    public function getSignedAmount(): float
    {
        return $this->isIncome() ? $this->amount : -$this->amount;
    }

    /**
     * Agregar tag
     */
    public function addTag(string $tag): void
    {
        $tag = trim($tag);
        if (!empty($tag) && !in_array($tag, $this->tags)) {
            $this->tags[] = $tag;
            $this->updatedAt = new \DateTime();
        }
    }

    /**
     * Remover tag
     */
    public function removeTag(string $tag): void
    {
        $index = array_search($tag, $this->tags);
        if ($index !== false) {
            unset($this->tags[$index]);
            $this->tags = array_values($this->tags);
            $this->updatedAt = new \DateTime();
        }
    }

    /**
     * Verificar si tiene tag
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags);
    }

    /**
     * Calcular próxima fecha de recurrencia
     */
    public function getNextRecurringDate(): ?\DateTime
    {
        if (!$this->isRecurring || !$this->recurringType || !$this->transactionDate) {
            return null;
        }

        $interval = $this->recurringInterval ?: 1;
        $nextDate = clone $this->transactionDate;

        switch ($this->recurringType) {
            case 'daily':
                $nextDate->add(new \DateInterval("P{$interval}D"));
                break;
            case 'weekly':
                $nextDate->add(new \DateInterval("P" . ($interval * 7) . "D"));
                break;
            case 'monthly':
                $nextDate->add(new \DateInterval("P{$interval}M"));
                break;
            case 'quarterly':
                $nextDate->add(new \DateInterval("P" . ($interval * 3) . "M"));
                break;
            case 'yearly':
                $nextDate->add(new \DateInterval("P{$interval}Y"));
                break;
            default:
                return null;
        }

        // Verificar si excede la fecha de fin
        if ($this->recurringEndDate && $nextDate > $this->recurringEndDate) {
            return null;
        }

        return $nextDate;
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getUserId(): int { return $this->userId; }
    public function getAccountId(): int { return $this->accountId; }
    public function getCategoryId(): int { return $this->categoryId; }
    public function getSubcategoryId(): ?int { return $this->subcategoryId; }
    public function getType(): string { return $this->type; }
    public function getAmount(): float { return $this->amount; }
    public function getDescription(): string { return $this->description; }
    public function getTransactionDate(): ?\DateTime { return $this->transactionDate; }
    public function getReference(): ?string { return $this->reference; }
    public function getNotes(): ?string { return $this->notes; }
    public function getTags(): array { return $this->tags; }
    public function isRecurring(): bool { return $this->isRecurring; }
    public function getRecurringType(): ?string { return $this->recurringType; }
    public function getRecurringInterval(): ?int { return $this->recurringInterval; }
    public function getRecurringEndDate(): ?\DateTime { return $this->recurringEndDate; }
    public function getCreatedAt(): \DateTime { return $this->createdAt; }
    public function getUpdatedAt(): \DateTime { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setUserId(int $userId): void { $this->userId = $userId; }
    public function setAccountId(int $accountId): void { $this->accountId = $accountId; }
    public function setCategoryId(int $categoryId): void { $this->categoryId = $categoryId; }
    public function setSubcategoryId(?int $subcategoryId): void { $this->subcategoryId = $subcategoryId; }
    public function setType(string $type): void { $this->type = $type; $this->updatedAt = new \DateTime(); }
    public function setAmount(float $amount): void { $this->amount = $amount; $this->updatedAt = new \DateTime(); }
    public function setDescription(string $description): void { $this->description = trim($description); $this->updatedAt = new \DateTime(); }
    public function setTransactionDate(\DateTime $transactionDate): void { $this->transactionDate = $transactionDate; $this->updatedAt = new \DateTime(); }
    public function setReference(?string $reference): void { $this->reference = $reference ? trim($reference) : null; $this->updatedAt = new \DateTime(); }
    public function setNotes(?string $notes): void { $this->notes = $notes ? trim($notes) : null; $this->updatedAt = new \DateTime(); }
    public function setTags(array $tags): void { $this->tags = array_filter(array_map('trim', $tags)); $this->updatedAt = new \DateTime(); }
    public function setIsRecurring(bool $isRecurring): void { $this->isRecurring = $isRecurring; $this->updatedAt = new \DateTime(); }
    public function setRecurringType(?string $recurringType): void { $this->recurringType = $recurringType; $this->updatedAt = new \DateTime(); }
    public function setRecurringInterval(?int $recurringInterval): void { $this->recurringInterval = $recurringInterval; $this->updatedAt = new \DateTime(); }
    public function setRecurringEndDate(?\DateTime $recurringEndDate): void { $this->recurringEndDate = $recurringEndDate; $this->updatedAt = new \DateTime(); }
    public function setUpdatedAt(\DateTime $updatedAt): void { $this->updatedAt = $updatedAt; }
}
