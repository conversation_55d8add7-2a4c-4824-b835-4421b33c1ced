<?php
/**
 * Test de Autenticación para Tarjetas de Crédito
 * Verifica el estado de autenticación y sesión
 */

echo "🔐 TESTING DE AUTENTICACIÓN\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// Iniciar sesión
session_start();

// Verificar estado actual de la sesión
echo "📊 ESTADO ACTUAL DE LA SESIÓN:\n";
echo "Session ID: " . session_id() . "\n";
echo "User ID: " . ($_SESSION['user_id'] ?? 'NO DEFINIDO') . "\n";
echo "Datos de sesión: " . json_encode($_SESSION) . "\n\n";

// Si no hay usuario, crear uno de prueba
if (!isset($_SESSION['user_id'])) {
    echo "⚠️ No hay usuario autenticado. Creando sesión de prueba...\n";
    
    // Configurar sesión de prueba
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Admin';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['is_authenticated'] = true;
    
    echo "✅ Sesión de prueba creada:\n";
    echo "   User ID: " . $_SESSION['user_id'] . "\n";
    echo "   Nombre: " . $_SESSION['user_name'] . "\n";
    echo "   Email: " . $_SESSION['user_email'] . "\n\n";
} else {
    echo "✅ Usuario ya autenticado con ID: " . $_SESSION['user_id'] . "\n\n";
}

// Verificar conexión a base de datos
echo "🗄️ VERIFICANDO BASE DE DATOS:\n";

$config = [
    'host' => 'localhost',
    'dbname' => 'control_gastos',
    'username' => 'root',
    'password' => '',
];

try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4";
    $db = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✅ Conexión a BD exitosa\n";
    
    // Verificar usuario en BD
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "✅ Usuario encontrado en BD: " . $user['name'] . " (" . $user['email'] . ")\n";
    } else {
        echo "⚠️ Usuario no encontrado en BD. Creando usuario de prueba...\n";
        
        // Crear usuario de prueba
        $stmt = $db->prepare("INSERT INTO users (name, email, password, created_at) VALUES (?, ?, ?, NOW())");
        $stmt->execute(['Admin Test', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT)]);
        
        echo "✅ Usuario de prueba creado\n";
    }
    
    // Verificar tarjetas del usuario
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM credit_cards WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $cardCount = $stmt->fetch()['count'];
    
    echo "📊 Tarjetas del usuario: {$cardCount}\n";
    
} catch (Exception $e) {
    echo "❌ Error de BD: " . $e->getMessage() . "\n";
}

echo "\n🌐 PROBANDO ACCESO A PÁGINAS:\n";

// Función para hacer petición con cookies de sesión
function makeRequestWithSession($url) {
    $sessionName = session_name();
    $sessionId = session_id();
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => "Cookie: {$sessionName}={$sessionId}\r\n"
        ]
    ]);
    
    return @file_get_contents($url, false, $context);
}

$baseUrl = 'http://localhost/controlGastos/public/';
$testUrls = [
    '' => 'Dashboard principal',
    '?route=credit-cards' => 'Dashboard tarjetas',
    '?route=credit-cards/create' => 'Crear tarjeta'
];

foreach ($testUrls as $route => $description) {
    $url = $baseUrl . $route;
    echo "🔍 Probando: {$description}\n";
    echo "   URL: {$url}\n";
    
    $content = makeRequestWithSession($url);
    
    if ($content !== false) {
        if (strpos($content, 'Fatal error') === false && 
            strpos($content, 'Parse error') === false) {
            
            // Verificar si es redirección a login
            if (strpos($content, 'login') !== false && strpos($content, 'password') !== false) {
                echo "   ⚠️ Redirige a login (problema de autenticación)\n";
            } else {
                echo "   ✅ Carga correctamente\n";
            }
        } else {
            echo "   ❌ Contiene errores PHP\n";
        }
    } else {
        echo "   ❌ No responde\n";
    }
    echo "\n";
}

echo "🎯 RECOMENDACIONES:\n";
echo "1. Asegúrate de estar autenticado en el navegador\n";
echo "2. Ve a: http://localhost/controlGastos/public/ y haz login\n";
echo "3. Luego prueba: http://localhost/controlGastos/public/?route=credit-cards\n\n";

echo "🔑 DATOS DE LOGIN DE PRUEBA:\n";
echo "Email: <EMAIL>\n";
echo "Password: admin123\n\n";

echo "🎉 TESTING DE AUTENTICACIÓN COMPLETADO\n";
