<?php

declare(strict_types=1);

namespace ControlGastos\Core;

/**
 * Manejo de sesiones de usuario
 * Proporciona una interfaz segura para el manejo de sesiones
 */
class Session
{
    private array $config;
    private bool $started = false;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * Iniciar sesión
     */
    public function start(): void
    {
        if ($this->started) {
            return;
        }

        // Configurar parámetros de sesión
        $this->configureSession();

        // Iniciar sesión
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $this->started = true;

        // Regenerar ID de sesión periódicamente
        $this->regenerateIdIfNeeded();

        // Validar sesión
        $this->validateSession();
    }

    /**
     * Configurar parámetros de sesión
     */
    private function configureSession(): void
    {
        $security = $this->config['security'] ?? [];

        // Nombre de sesión
        if (isset($security['session_name'])) {
            session_name($security['session_name']);
        }

        // Configurar cookies de sesión
        session_set_cookie_params([
            'lifetime' => $security['session_expire'] ?? 86400,
            'path' => '/',
            'domain' => '',
            'secure' => isset($_SERVER['HTTPS']),
            'httponly' => true,
            'samesite' => 'Lax'
        ]);

        // Configurar garbage collection
        ini_set('session.gc_maxlifetime', (string) ($security['session_expire'] ?? 86400));
        ini_set('session.gc_probability', '1');
        ini_set('session.gc_divisor', '100');
    }

    /**
     * Regenerar ID de sesión si es necesario
     */
    private function regenerateIdIfNeeded(): void
    {
        $lastRegeneration = $this->get('_last_regeneration', 0);
        $now = time();

        // Regenerar cada 30 minutos
        if ($now - $lastRegeneration > 1800) {
            session_regenerate_id(true);
            $this->set('_last_regeneration', $now);
        }
    }

    /**
     * Validar sesión
     */
    private function validateSession(): void
    {
        // Validar IP (opcional, puede causar problemas con proxies)
        $currentIp = $_SERVER['REMOTE_ADDR'] ?? '';
        $sessionIp = $this->get('_ip_address');

        if ($sessionIp === null) {
            $this->set('_ip_address', $currentIp);
        }

        // Validar User Agent
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $sessionUserAgent = $this->get('_user_agent');

        if ($sessionUserAgent === null) {
            $this->set('_user_agent', $currentUserAgent);
        } elseif ($sessionUserAgent !== $currentUserAgent) {
            $this->destroy();
            throw new \Exception('Sesión inválida: User Agent no coincide');
        }

        // Validar tiempo de vida
        $lastActivity = $this->get('_last_activity', time());
        $sessionExpire = $this->config['security']['session_expire'] ?? 86400;

        if (time() - $lastActivity > $sessionExpire) {
            $this->destroy();
            throw new \Exception('Sesión expirada');
        }

        $this->set('_last_activity', time());
    }

    /**
     * Obtener valor de sesión
     */
    public function get(string $key, $default = null)
    {
        $this->ensureStarted();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Establecer valor de sesión
     */
    public function set(string $key, $value): void
    {
        $this->ensureStarted();
        $_SESSION[$key] = $value;
    }

    /**
     * Verificar si existe una clave en sesión
     */
    public function has(string $key): bool
    {
        $this->ensureStarted();
        return isset($_SESSION[$key]);
    }

    /**
     * Eliminar valor de sesión
     */
    public function remove(string $key): void
    {
        $this->ensureStarted();
        unset($_SESSION[$key]);
    }

    /**
     * Obtener todos los datos de sesión
     */
    public function all(): array
    {
        $this->ensureStarted();
        return $_SESSION;
    }

    /**
     * Limpiar todos los datos de sesión
     */
    public function clear(): void
    {
        $this->ensureStarted();
        $_SESSION = [];
    }

    /**
     * Destruir sesión completamente
     */
    public function destroy(): void
    {
        $this->ensureStarted();
        
        // Limpiar datos de sesión
        $_SESSION = [];

        // Eliminar cookie de sesión
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params['path'],
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
        }

        // Destruir sesión
        session_destroy();
        $this->started = false;
    }

    /**
     * Regenerar ID de sesión
     */
    public function regenerateId(bool $deleteOldSession = true): void
    {
        $this->ensureStarted();
        session_regenerate_id($deleteOldSession);
        $this->set('_last_regeneration', time());
    }

    /**
     * Obtener ID de sesión
     */
    public function getId(): string
    {
        $this->ensureStarted();
        return session_id();
    }

    /**
     * Flash messages - Establecer mensaje temporal
     */
    public function flash(string $key, $value): void
    {
        $this->set('_flash_' . $key, $value);
    }

    /**
     * Obtener mensaje flash
     */
    public function getFlash(string $key, $default = null)
    {
        $flashKey = '_flash_' . $key;
        $value = $this->get($flashKey, $default);
        $this->remove($flashKey);
        return $value;
    }

    /**
     * Verificar si existe mensaje flash
     */
    public function hasFlash(string $key): bool
    {
        return $this->has('_flash_' . $key);
    }

    /**
     * Obtener token CSRF
     */
    public function getCsrfToken(): string
    {
        $token = $this->get('_csrf_token');
        
        if (!$token) {
            $token = bin2hex(random_bytes(32));
            $this->set('_csrf_token', $token);
        }
        
        return $token;
    }

    /**
     * Validar token CSRF
     */
    public function validateCsrfToken(string $token): bool
    {
        $sessionToken = $this->get('_csrf_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Asegurar que la sesión esté iniciada
     */
    private function ensureStarted(): void
    {
        if (!$this->started) {
            $this->start();
        }
    }

    /**
     * Verificar si la sesión está iniciada
     */
    public function isStarted(): bool
    {
        return $this->started;
    }
}
