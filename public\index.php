<?php
/**
 * Punto de entrada principal de la aplicación
 * Control de Gastos Personales - Versión Local XAMPP
 */

declare(strict_types=1);

// Configuración de sesión segura
ini_set('session.cookie_httponly', '1');
ini_set('session.use_only_cookies', '1');
ini_set('session.cookie_secure', '0'); // Para desarrollo local
ini_set('session.cookie_samesite', 'Lax');

// Cargar bootstrap de la aplicación
require_once __DIR__ . '/../src/bootstrap.php';

// Router simple para desarrollo
$route = $_GET['route'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];

try {
    // Rutas básicas para pruebas
    switch ($route) {
        case '':
        case 'dashboard':
            // Verificar si hay usuario logueado
            $session = $container->get('session');
            if (!$session->get('user_id')) {
                header('Location: ?route=auth/login');
                exit;
            }

            // Mostrar dashboard
            $dashboardController = new \ControlGastos\Controllers\SimpleDashboardController($container);
            echo $dashboardController->index();
            break;

        case 'auth/login':
            if ($method === 'POST') {
                // Procesar login
                $authController = new \ControlGastos\Controllers\SimpleAuthController($container);
                echo $authController->login();
            } else {
                // Mostrar formulario de login
                $authController = new \ControlGastos\Controllers\SimpleAuthController($container);
                echo $authController->showLogin();
            }
            break;

        case 'auth/register':
            if ($method === 'POST') {
                // Procesar registro
                $authController = new \ControlGastos\Controllers\SimpleAuthController($container);
                echo $authController->register();
            } else {
                // Mostrar formulario de registro
                $authController = new \ControlGastos\Controllers\SimpleAuthController($container);
                echo $authController->showRegister();
            }
            break;

        case 'auth/logout':
            $authController = new \ControlGastos\Controllers\SimpleAuthController($container);
            $authController->logout();
            break;

        case 'transactions':
            $transactionController = new \ControlGastos\Controllers\TransactionController(
                $container->get('database'),
                $container->get('session')
            );
            $transactionController->index();
            break;

        // Rutas de bancos
        case 'banks':
        case 'banks/create':
        case 'banks/store':
        case 'banks/edit':
        case 'banks/update':
        case 'banks/delete':
        case 'banks/toggle-status':

        // Rutas de tarjetas de crédito
        case 'credit-cards':
        case 'credit-cards/create':
        case 'credit-cards/store':
        case 'credit-cards/show':
        case 'credit-cards/edit':
        case 'credit-cards/update':
        case 'credit-cards/delete':
        case 'credit-cards/add-transaction':
        case 'credit-cards/add-payment':
        case 'credit-cards/reports':
        case 'credit-cards/change-status':
        case 'credit-cards/check-expired':

        // Rutas de cuentas bancarias
        case 'bank-accounts':
        case 'bank-accounts/create':
        case 'bank-accounts/store':
        case 'bank-accounts/show':
        case 'bank-accounts/edit':
        case 'bank-accounts/update':
        case 'bank-accounts/add-movement':
            $session = $container->get('session');
            if (!$session->get('user_id')) {
                header('Location: ?route=auth/login');
                exit;
            }

            // Determinar qué controlador usar
            if (strpos($route, 'banks') === 0) {
                $bankController = new \ControlGastos\Controllers\BankController(
                    $container->get('database'),
                    $container->get('session')
                );

                $action = $_GET['action'] ?? 'index';

                switch ($action) {
                    case 'index':
                    default:
                        echo $bankController->index();
                        break;
                    case 'create':
                        echo $bankController->create();
                        break;
                    case 'store':
                        $bankController->store();
                        break;
                    case 'edit':
                        echo $bankController->edit();
                        break;
                    case 'update':
                        $bankController->update();
                        break;
                    case 'delete':
                        $bankController->delete();
                        break;
                    case 'toggle-status':
                        $bankController->toggleStatus();
                        break;
                }
            } elseif (strpos($route, 'credit-cards') === 0) {
                $creditCardController = new \ControlGastos\Controllers\CreditCardController(
                    $container->get('database'),
                    $container->get('session')
                );

                switch ($route) {
                    case 'credit-cards':
                        $creditCardController->index();
                        break;
                    case 'credit-cards/create':
                        $creditCardController->create();
                        break;
                    case 'credit-cards/store':
                        $creditCardController->store();
                        break;
                    case 'credit-cards/show':
                        $creditCardController->show();
                        break;
                    case 'credit-cards/edit':
                        $creditCardController->edit();
                        break;
                    case 'credit-cards/update':
                        $creditCardController->update();
                        break;
                    case 'credit-cards/delete':
                        $creditCardController->delete();
                        break;
                    case 'credit-cards/add-transaction':
                        $creditCardController->addTransaction();
                        break;
                    case 'credit-cards/add-payment':
                        $creditCardController->addPayment();
                        break;
                    case 'credit-cards/change-status':
                        $creditCardController->changeStatus();
                        break;
                    case 'credit-cards/check-expired':
                        $creditCardController->checkExpired();
                        break;
                    case 'credit-cards/reports':
                        $creditCardController->reports();
                        break;
                }
            } elseif (strpos($route, 'bank-accounts') === 0) {
                $bankAccountController = new \ControlGastos\Controllers\BankAccountController(
                    $container->get('database'),
                    $container->get('session')
                );

                switch ($route) {
                    case 'bank-accounts':
                        $bankAccountController->index();
                        break;
                    case 'bank-accounts/create':
                        $bankAccountController->create();
                        break;
                    case 'bank-accounts/store':
                        $bankAccountController->store();
                        break;
                    case 'bank-accounts/show':
                        $bankAccountController->show();
                        break;
                    case 'bank-accounts/edit':
                        $bankAccountController->edit();
                        break;
                    case 'bank-accounts/update':
                        $bankAccountController->update();
                        break;
                    case 'bank-accounts/add-movement':
                        $bankAccountController->addMovement();
                        break;
                }
            }

            break;

        case 'accounts':
        case 'accounts/store':
            $session = $container->get('session');
            if (!$session->get('user_id')) {
                header('Location: ?route=auth/login');
                exit;
            }

            $integratedController = new \ControlGastos\Controllers\IntegratedTransactionController(
                $container->get('database'),
                $container->get('session')
            );

            switch ($route) {
                case 'accounts':
                    $integratedController->index();
                    break;
                case 'accounts/store':
                    $integratedController->store();
                    break;
            }
            break;

        case 'reports':
            $reportController = new \ControlGastos\Controllers\ReportController(
                $container->get('database'),
                $container->get('session')
            );
            $reportController->index();
            break;

        case 'categories':
            $categoryController = new \ControlGastos\Controllers\CategoryController(
                $container->get('database'),
                $container->get('session')
            );

            $action = $_GET['action'] ?? $_POST['action'] ?? 'index';

            switch ($action) {
                case 'store':
                    $categoryController->store();
                    break;
                case 'update':
                    $categoryController->update();
                    break;
                case 'delete':
                    $categoryController->delete();
                    break;
                case 'toggle':
                    $categoryController->toggle();
                    break;
                default:
                    $categoryController->index();
                    break;
            }
            break;

        default:
            // Página de inicio o 404
            if (empty($route)) {
                // Redirigir al dashboard si está logueado, sino al login
                $session = $container->get('session');
                if ($session->get('user_id')) {
                    header('Location: ?route=dashboard');
                } else {
                    header('Location: ?route=auth/login');
                }
                exit;
            } else {
                // 404
                http_response_code(404);
                include __DIR__ . '/../templates/errors/404.php';
            }
            break;
    }

} catch (Exception $e) {
    // Manejo de errores
    $config = $container->get('config');

    if ($config['app']['debug']) {
        echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem; border-radius: 5px;">';
        echo '<h3>Error de Desarrollo</h3>';
        echo '<p><strong>Mensaje:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>Archivo:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Línea:</strong> ' . $e->getLine() . '</p>';
        echo '<details><summary>Stack Trace</summary><pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre></details>';
        echo '</div>';
    } else {
        http_response_code(500);
        include __DIR__ . '/../templates/errors/500.php';
    }

    // Log del error
    if (isset($container)) {
        $logger = $container->get('logger');
        $logger->error('Error de aplicación: ' . $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}

/**
 * Renderizar página "Coming Soon" para módulos no implementados
 */
function renderComingSoon(string $module): string
{
    $moduleNames = [
        'transactions' => 'Transacciones',
        'accounts' => 'Cuentas',
        'categories' => 'Categorías',
        'reports' => 'Reportes'
    ];

    $moduleName = $moduleNames[$module] ?? ucfirst($module);

    ob_start();
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?= $moduleName ?> - Control de Gastos</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; }
            .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        </style>
    </head>
    <body>
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="?route=dashboard">
                    <i class="fas fa-chart-line me-2"></i>Control de Gastos
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="?route=auth/logout">
                        <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-tools fa-4x text-primary mb-4"></i>
                            <h2 class="mb-3"><?= $moduleName ?></h2>
                            <p class="lead text-muted mb-4">
                                Este módulo está en desarrollo y estará disponible próximamente.
                            </p>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Estado del Desarrollo</h6>
                                <p class="mb-0">
                                    Actualmente puedes probar el <strong>sistema de autenticación</strong> y el <strong>dashboard básico</strong>.
                                    Los demás módulos se implementarán gradualmente.
                                </p>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="?route=dashboard" class="btn btn-primary">
                                    <i class="fas fa-home me-2"></i>Volver al Dashboard
                                </a>
                                <a href="?route=auth/logout" class="btn btn-outline-secondary">
                                    <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                                </a>
                            </div>

                            <hr class="my-4">

                            <h6>Módulos Disponibles para Pruebas:</h6>
                            <div class="row g-3 mt-2">
                                <div class="col-md-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <strong>Autenticación</strong><br>
                                            <small>Login, Registro, Logout</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <strong>Dashboard</strong><br>
                                            <small>Resumen básico de finanzas</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}
