<?php

declare(strict_types=1);

namespace ControlGastos\Core;

use ControlGastos\Core\Router;
use ControlGastos\Core\Database;
use ControlGastos\Core\Session;
use ControlGastos\Core\Logger;
use ControlGastos\Core\Container;
use Exception;

/**
 * Clase principal de la aplicación
 * Maneja la inicialización y ejecución de la aplicación
 */
class Application
{
    private array $config;
    private Database $database;
    private Session $session;
    private Logger $logger;
    private Container $container;

    public function __construct(
        array $config,
        Database $database,
        Session $session,
        Logger $logger
    ) {
        $this->config = $config;
        $this->database = $database;
        $this->session = $session;
        $this->logger = $logger;
        $this->container = new Container();
        
        $this->registerServices();
    }

    /**
     * Registrar servicios en el contenedor
     */
    private function registerServices(): void
    {
        $this->container->bind('config', $this->config);
        $this->container->bind('database', $this->database);
        $this->container->bind('session', $this->session);
        $this->container->bind('logger', $this->logger);
        
        // Registrar repositorios
        $this->container->bind('userRepository', function() {
            return new \ControlGastos\Repositories\UserRepository($this->database);
        });
        
        $this->container->bind('accountRepository', function() {
            return new \ControlGastos\Repositories\AccountRepository($this->database);
        });
        
        $this->container->bind('categoryRepository', function() {
            return new \ControlGastos\Repositories\CategoryRepository($this->database);
        });
        
        $this->container->bind('transactionRepository', function() {
            return new \ControlGastos\Repositories\TransactionRepository($this->database);
        });
        
        $this->container->bind('commitmentRepository', function() {
            return new \ControlGastos\Repositories\CommitmentRepository($this->database);
        });
        
        // Registrar servicios
        $this->container->bind('authService', function() {
            return new \ControlGastos\Services\AuthService(
                $this->container->get('userRepository'),
                $this->session,
                $this->logger
            );
        });
        
        $this->container->bind('mailService', function() {
            return new \ControlGastos\Services\MailService($this->config, $this->logger);
        });
        
        $this->container->bind('notificationService', function() {
            return new \ControlGastos\Services\NotificationService(
                $this->container->get('mailService'),
                $this->database,
                $this->logger
            );
        });
        
        $this->container->bind('reportService', function() {
            return new \ControlGastos\Services\ReportService(
                $this->database,
                $this->config
            );
        });
        
        $this->container->bind('backupService', function() {
            return new \ControlGastos\Services\BackupService(
                $this->config,
                $this->database,
                $this->logger
            );
        });

        $this->container->bind('accountService', function() {
            return new \ControlGastos\Services\AccountService(
                $this->container->get('accountRepository'),
                $this->logger
            );
        });

        $this->container->bind('subcategoryRepository', function() {
            return new \ControlGastos\Repositories\SubcategoryRepository($this->database);
        });

        $this->container->bind('categoryService', function() {
            return new \ControlGastos\Services\CategoryService(
                $this->container->get('categoryRepository'),
                $this->container->get('subcategoryRepository'),
                $this->logger
            );
        });

        $this->container->bind('transactionRepository', function() {
            return new \ControlGastos\Repositories\TransactionRepository($this->database);
        });

        $this->container->bind('transactionService', function() {
            return new \ControlGastos\Services\TransactionService(
                $this->container->get('transactionRepository'),
                $this->container->get('accountRepository'),
                $this->container->get('categoryRepository'),
                $this->logger
            );
        });

        $this->container->bind('reminderRepository', function() {
            return new \ControlGastos\Repositories\ReminderRepository($this->database);
        });

        $this->container->bind('reminderService', function() {
            return new \ControlGastos\Services\ReminderService(
                $this->container->get('reminderRepository'),
                $this->container->get('categoryRepository'),
                $this->container->get('accountRepository'),
                $this->container->get('transactionService'),
                $this->logger
            );
        });

        $this->container->bind('reportService', function() {
            return new \ControlGastos\Services\ReportService(
                $this->container->get('transactionRepository'),
                $this->container->get('accountRepository'),
                $this->container->get('categoryRepository'),
                $this->container->get('reminderRepository'),
                $this->logger
            );
        });

        $this->container->bind('backupService', function() {
            return new \ControlGastos\Services\BackupService(
                $this->database,
                $this->logger,
                $this->config
            );
        });

        $this->container->bind('securityService', function() {
            return new \ControlGastos\Services\SecurityService(
                $this->database,
                $this->logger,
                $this->container->get('session')
            );
        });

        $this->container->bind('cacheService', function() {
            return new \ControlGastos\Services\CacheService(
                $this->logger,
                $this->config->get('cache.path', __DIR__ . '/../../storage/cache'),
                $this->config->get('cache.default_ttl', 3600)
            );
        });

        $this->container->bind('dbOptimizationService', function() {
            return new \ControlGastos\Services\DatabaseOptimizationService(
                $this->database,
                $this->logger,
                $this->container->get('cacheService')
            );
        });

        $this->container->bind('performanceMonitorService', function() {
            return new \ControlGastos\Services\PerformanceMonitorService(
                $this->logger,
                $this->container->get('cacheService')
            );
        });
    }

    /**
     * Ejecutar la aplicación
     */
    public function run(Router $router): void
    {
        try {
            // Obtener método y URI
            $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
            $uri = $this->getUri();
            
            // Log de la request
            $this->logger->info("Request: {$method} {$uri}", [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            // Ejecutar router
            $response = $router->dispatch($method, $uri, $this->container);
            
            // Enviar respuesta
            $this->sendResponse($response);
            
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Obtener URI limpia
     */
    private function getUri(): string
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        
        // Remover query string
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }
        
        // Limpiar URI
        $uri = rawurldecode($uri);
        $uri = filter_var($uri, FILTER_SANITIZE_URL);
        
        return $uri ?: '/';
    }

    /**
     * Enviar respuesta al cliente
     */
    private function sendResponse($response): void
    {
        if (is_array($response)) {
            // Respuesta JSON
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
        } elseif (is_string($response)) {
            // Respuesta HTML
            header('Content-Type: text/html; charset=utf-8');
            echo $response;
        } else {
            // Respuesta vacía
            http_response_code(204);
        }
    }

    /**
     * Manejar excepciones
     */
    private function handleException(Exception $e): void
    {
        $this->logger->error('Application error: ' . $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);

        // Determinar código de respuesta
        $statusCode = 500;
        if (method_exists($e, 'getStatusCode')) {
            $statusCode = $e->getStatusCode();
        }

        http_response_code($statusCode);

        // Respuesta según el tipo de request
        if ($this->isApiRequest()) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode([
                'error' => true,
                'message' => $this->config['app']['debug'] ? $e->getMessage() : 'Error interno del servidor',
                'code' => $statusCode
            ], JSON_UNESCAPED_UNICODE);
        } else {
            // Mostrar página de error
            $this->showErrorPage($statusCode, $e);
        }
    }

    /**
     * Verificar si es una request de API
     */
    private function isApiRequest(): bool
    {
        $uri = $this->getUri();
        return strpos($uri, '/api/') === 0;
    }

    /**
     * Mostrar página de error
     */
    private function showErrorPage(int $statusCode, Exception $e): void
    {
        $errorTemplate = __DIR__ . '/../../templates/errors/' . $statusCode . '.php';
        
        if (file_exists($errorTemplate)) {
            include $errorTemplate;
        } else {
            echo '<h1>Error ' . $statusCode . '</h1>';
            if ($this->config['app']['debug']) {
                echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
            } else {
                echo '<p>Ha ocurrido un error. Por favor, inténtelo más tarde.</p>';
            }
        }
    }

    /**
     * Obtener instancia del contenedor
     */
    public function getContainer(): Container
    {
        return $this->container;
    }

    /**
     * Obtener configuración
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}
