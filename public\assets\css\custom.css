/* Estilos personalizados para Control de Gastos */

/* Variables CSS */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --shadow: 0 5px 15px rgba(0,0,0,0.1);
    --shadow-hover: 0 8px 25px rgba(0,0,0,0.15);
    --border-radius: 15px;
}

/* Estilos generales */
body {
    background-color: var(--light-bg);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navbar */
.navbar {
    background: var(--primary-gradient) !important;
    box-shadow: var(--shadow);
    border: none;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.card-header {
    background: white;
    border-bottom: 1px solid #eee;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
}

/* Stat Cards */
.stat-card {
    background: var(--primary-gradient) !important;
    color: white !important;
    border: none;
}

.stat-card .card-body {
    padding: 2rem 1rem;
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.stat-card h5 {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.stat-card i {
    opacity: 0.8;
}

/* Botones */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Tablas */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
}

/* Dropdown */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow);
    border-radius: 8px;
}

.dropdown-item {
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

/* Iconos */
.fas, .far, .fab {
    margin-right: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .stat-card .card-body {
        padding: 1.5rem 1rem;
    }
    
    .stat-card h3 {
        font-size: 1.5rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* Animaciones */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* Estados de carga */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Utilidades */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--primary-gradient) !important;
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Mejoras de accesibilidad */
.btn:focus,
.form-control:focus {
    outline: none;
}

/* Estilos para elementos vacíos */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Transiciones suaves */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Fix para iconos FontAwesome si no cargan */
.fa-fallback::before {
    content: "●";
    font-family: Arial, sans-serif;
}
