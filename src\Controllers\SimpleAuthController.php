<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Core\SimpleDatabase;
use ControlGastos\Core\Session;

/**
 * Controlador de autenticación simplificado para pruebas
 */
class SimpleAuthController
{
    private SimpleDatabase $database;
    private Session $session;

    public function __construct(Container $container)
    {
        $this->database = $container->get('database');
        $this->session = $container->get('session');
    }

    /**
     * Mostrar formulario de login
     */
    public function showLogin(): string
    {
        // Si ya está autenticado, redirigir al dashboard
        if ($this->session->get('user_id')) {
            header('Location: ?route=dashboard');
            exit;
        }

        return $this->renderLogin();
    }

    /**
     * Procesar login
     */
    public function login(): string
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return $this->renderLogin();
        }

        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($email) || empty($password)) {
            return $this->renderLogin('Email y contraseña son requeridos');
        }

        try {
            // Buscar usuario por email
            $user = $this->database->selectOne(
                "SELECT * FROM users WHERE email = ? AND (status = 'active' OR email_verified = 1)",
                [$email]
            );

            if (!$user) {
                return $this->renderLogin('Credenciales incorrectas');
            }

            // Verificar contraseña
            $passwordField = isset($user['password']) ? 'password' : 'password_hash';
            $isValidPassword = password_verify($password, $user[$passwordField]);

            if (!$isValidPassword) {
                return $this->renderLogin('Credenciales incorrectas');
            }

            // Login exitoso
            $this->session->set('user_id', $user['id']);
            $this->session->set('user_email', $user['email']);
            $this->session->set('user_name', $user['first_name'] . ' ' . $user['last_name']);
            $this->session->regenerateId();

            header('Location: ?route=dashboard');
            exit;

        } catch (\Exception $e) {
            return $this->renderLogin('Error de conexión: ' . $e->getMessage());
        }
    }

    /**
     * Logout
     */
    public function logout(): void
    {
        $this->session->destroy();
        header('Location: ?route=auth/login');
        exit;
    }

    /**
     * Mostrar formulario de registro
     */
    public function showRegister(): string
    {
        return $this->renderRegister();
    }

    /**
     * Procesar registro
     */
    public function register(): string
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return $this->renderRegister();
        }

        $firstName = $_POST['first_name'] ?? '';
        $lastName = $_POST['last_name'] ?? '';
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        // Validaciones
        $errors = [];
        
        if (empty($firstName)) $errors[] = 'El nombre es requerido';
        if (empty($lastName)) $errors[] = 'El apellido es requerido';
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Email válido es requerido';
        if (empty($password) || strlen($password) < 8) $errors[] = 'La contraseña debe tener al menos 8 caracteres';
        if ($password !== $confirmPassword) $errors[] = 'Las contraseñas no coinciden';

        if (!empty($errors)) {
            return $this->renderRegister(implode('<br>', $errors));
        }

        try {
            // Verificar si el email ya existe
            $existingUser = $this->database->selectOne(
                "SELECT id FROM users WHERE email = ?",
                [$email]
            );

            if ($existingUser) {
                return $this->renderRegister('El email ya está registrado');
            }

            // Crear usuario
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            
            // Adaptar a la estructura de la tabla
            $insertData = [
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
                'password_hash' => $passwordHash,
                'status' => 'active',
                'email_verified' => 1
            ];

            $userId = $this->database->insert(
                "INSERT INTO users (first_name, last_name, email, password_hash, status, email_verified, created_at, updated_at) 
                 VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())",
                array_values($insertData)
            );

            if ($userId) {
                // Auto-login después del registro
                $this->session->set('user_id', $userId);
                $this->session->set('user_email', $email);
                $this->session->set('user_name', $firstName . ' ' . $lastName);
                $this->session->regenerateId();

                header('Location: ?route=dashboard');
                exit;
            } else {
                return $this->renderRegister('Error al crear la cuenta');
            }

        } catch (\Exception $e) {
            return $this->renderRegister('Error de conexión: ' . $e->getMessage());
        }
    }

    /**
     * Renderizar página de login
     */
    private function renderLogin(string $error = ''): string
    {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Iniciar Sesión - Control de Gastos</title>
            <?= includeBasicCSS() ?>
            <style>
                body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; }
                .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
                .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 col-lg-4">
                        <div class="card">
                            <div class="card-header text-center py-4">
                                <h3><i class="fas fa-sign-in-alt me-2"></i>Iniciar Sesión</h3>
                            </div>
                            <div class="card-body p-4">
                                <?php if ($error): ?>
                                    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                                <?php endif; ?>
                                
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email" name="email" required 
                                               value="<?= htmlspecialchars($_POST['email'] ?? 'admin@localhost') ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Contraseña</label>
                                        <input type="password" class="form-control" id="password" name="password" required
                                               placeholder="admin123">
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-sign-in-alt me-2"></i>Iniciar Sesión
                                        </button>
                                    </div>
                                </form>
                                
                                <hr>
                                <div class="text-center">
                                    <a href="?route=auth/register" class="btn btn-outline-secondary">
                                        <i class="fas fa-user-plus me-2"></i>Crear Cuenta
                                    </a>
                                </div>
                                
                                <div class="mt-3 text-center">
                                    <small class="text-muted">
                                        <strong>Cuenta de prueba:</strong><br>
                                        Email: admin@localhost<br>
                                        Contraseña: admin123
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Renderizar página de registro
     */
    private function renderRegister(string $error = ''): string
    {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Registrarse - Control de Gastos</title>
            <?= includeBasicCSS() ?>
            <style>
                body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; }
                .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
                .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-6">
                        <div class="card">
                            <div class="card-header text-center py-4">
                                <h3><i class="fas fa-user-plus me-2"></i>Crear Cuenta</h3>
                            </div>
                            <div class="card-body p-4">
                                <?php if ($error): ?>
                                    <div class="alert alert-danger"><?= $error ?></div>
                                <?php endif; ?>
                                
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="first_name" class="form-label">Nombre</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" required
                                                   value="<?= htmlspecialchars($_POST['first_name'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_name" class="form-label">Apellido</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" required
                                                   value="<?= htmlspecialchars($_POST['last_name'] ?? '') ?>">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email" name="email" required
                                               value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="password" class="form-label">Contraseña</label>
                                            <input type="password" class="form-control" id="password" name="password" required minlength="8">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="confirm_password" class="form-label">Confirmar Contraseña</label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="8">
                                        </div>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-user-plus me-2"></i>Crear Cuenta
                                        </button>
                                    </div>
                                </form>
                                
                                <hr>
                                <div class="text-center">
                                    <a href="?route=auth/login" class="btn btn-outline-secondary">
                                        <i class="fas fa-sign-in-alt me-2"></i>Ya tengo cuenta
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }
}
