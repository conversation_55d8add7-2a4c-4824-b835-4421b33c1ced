<?php

declare(strict_types=1);

namespace ControlGastos\Models;

use PDO;
use DateTime;

/**
 * Modelo para gestión de pagos de tarjetas de crédito
 */
class CreditCardPayment
{
    private PDO $db;
    
    public function __construct(PDO $db)
    {
        $this->db = $db;
    }
    
    /**
     * Crear un nuevo pago
     */
    public function create(array $data): int
    {
        $sql = "INSERT INTO credit_card_payments (
            credit_card_id, user_id, payment_type, amount, payment_date,
            payment_method, reference_number, cut_off_period, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['credit_card_id'],
            $data['user_id'],
            $data['payment_type'] ?? 'partial',
            $data['amount'],
            $data['payment_date'],
            $data['payment_method'] ?? 'bank_transfer',
            $data['reference_number'] ?? null,
            $data['cut_off_period'] ?? null,
            $data['notes'] ?? null
        ]);
        
        return (int) $this->db->lastInsertId();
    }
    
    /**
     * Obtener pago por ID
     */
    public function findById(int $id): ?array
    {
        $sql = "SELECT ccp.*, cc.card_name, cc.bank_name
                FROM credit_card_payments ccp
                LEFT JOIN credit_cards cc ON ccp.credit_card_id = cc.id
                WHERE ccp.id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        
        $payment = $stmt->fetch();
        return $payment ?: null;
    }
    
    /**
     * Obtener pagos por tarjeta de crédito
     */
    public function findByCardId(int $cardId, int $limit = 50, int $offset = 0): array
    {
        $sql = "SELECT * FROM credit_card_payments 
                WHERE credit_card_id = ?
                ORDER BY payment_date DESC, created_at DESC
                LIMIT ? OFFSET ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $limit, $offset]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Obtener pagos por usuario
     */
    public function findByUserId(int $userId, int $limit = 50, int $offset = 0): array
    {
        $sql = "SELECT ccp.*, cc.card_name, cc.bank_name
                FROM credit_card_payments ccp
                LEFT JOIN credit_cards cc ON ccp.credit_card_id = cc.id
                WHERE ccp.user_id = ?
                ORDER BY ccp.payment_date DESC, ccp.created_at DESC
                LIMIT ? OFFSET ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $limit, $offset]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Obtener pagos por rango de fechas
     */
    public function findByDateRange(int $cardId, string $startDate, string $endDate): array
    {
        $sql = "SELECT * FROM credit_card_payments 
                WHERE credit_card_id = ?
                AND payment_date BETWEEN ? AND ?
                ORDER BY payment_date DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $startDate, $endDate]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Actualizar pago
     */
    public function update(int $id, array $data): bool
    {
        $sql = "UPDATE credit_card_payments SET 
            payment_type = ?, amount = ?, payment_date = ?, payment_method = ?,
            reference_number = ?, cut_off_period = ?, notes = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['payment_type'] ?? 'partial',
            $data['amount'],
            $data['payment_date'],
            $data['payment_method'] ?? 'bank_transfer',
            $data['reference_number'] ?? null,
            $data['cut_off_period'] ?? null,
            $data['notes'] ?? null,
            $id
        ]);
    }
    
    /**
     * Eliminar pago
     */
    public function delete(int $id): bool
    {
        $sql = "DELETE FROM credit_card_payments WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    /**
     * Obtener total de pagos por período
     */
    public function getTotalByPeriod(int $cardId, string $startDate, string $endDate): float
    {
        $sql = "SELECT COALESCE(SUM(amount), 0) as total 
                FROM credit_card_payments 
                WHERE credit_card_id = ? 
                AND payment_date BETWEEN ? AND ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $startDate, $endDate]);
        
        return (float) $stmt->fetch()['total'];
    }
    
    /**
     * Obtener total de pagos por tipo
     */
    public function getTotalByType(int $cardId, string $type, string $startDate = null, string $endDate = null): float
    {
        $whereClause = "WHERE credit_card_id = ? AND payment_type = ?";
        $params = [$cardId, $type];
        
        if ($startDate && $endDate) {
            $whereClause .= " AND payment_date BETWEEN ? AND ?";
            $params[] = $startDate;
            $params[] = $endDate;
        }
        
        $sql = "SELECT COALESCE(SUM(amount), 0) as total 
                FROM credit_card_payments 
                {$whereClause}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return (float) $stmt->fetch()['total'];
    }
    
    /**
     * Obtener estadísticas de pagos mensuales
     */
    public function getMonthlyStats(int $cardId, int $year, int $month): array
    {
        $sql = "SELECT 
            payment_type,
            payment_method,
            COUNT(*) as count,
            SUM(amount) as total,
            AVG(amount) as average
        FROM credit_card_payments 
        WHERE credit_card_id = ? 
        AND YEAR(payment_date) = ? 
        AND MONTH(payment_date) = ?
        GROUP BY payment_type, payment_method";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $year, $month]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Obtener historial de pagos por período de corte
     */
    public function getPaymentsByCutOffPeriod(int $cardId, string $cutOffPeriod): array
    {
        $sql = "SELECT * FROM credit_card_payments 
                WHERE credit_card_id = ? 
                AND cut_off_period = ?
                ORDER BY payment_date DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $cutOffPeriod]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Calcular pago mínimo sugerido
     */
    public function calculateMinimumPayment(int $cardId, float $currentBalance, float $interestRate = 0.02): float
    {
        // Pago mínimo típico: 2-5% del saldo o monto mínimo fijo
        $minimumPercentage = 0.02; // 2%
        $minimumFixed = 50.00; // Monto mínimo fijo
        
        $percentagePayment = $currentBalance * $minimumPercentage;
        $interestPayment = $currentBalance * $interestRate;
        
        return max($percentagePayment, $minimumFixed, $interestPayment);
    }
    
    /**
     * Obtener próximos pagos vencidos
     */
    public function getOverduePayments(int $userId): array
    {
        $sql = "SELECT cc.*, 
                DATEDIFF(CURDATE(), DATE_ADD(
                    LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), 
                    INTERVAL cc.cut_off_day DAY
                )) as days_overdue
                FROM credit_cards cc
                WHERE cc.user_id = ? 
                AND cc.is_active = 1
                AND NOT EXISTS (
                    SELECT 1 FROM credit_card_payments ccp 
                    WHERE ccp.credit_card_id = cc.id 
                    AND YEAR(ccp.payment_date) = YEAR(CURDATE())
                    AND MONTH(ccp.payment_date) = MONTH(CURDATE())
                )
                AND DATEDIFF(CURDATE(), DATE_ADD(
                    LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), 
                    INTERVAL (cc.cut_off_day + cc.payment_due_days) DAY
                )) > 0";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Registrar pago automático
     */
    public function createAutomaticPayment(int $cardId, int $userId, float $amount, string $type = 'automatic'): int
    {
        $paymentData = [
            'credit_card_id' => $cardId,
            'user_id' => $userId,
            'payment_type' => $type,
            'amount' => $amount,
            'payment_date' => date('Y-m-d'),
            'payment_method' => 'automatic_debit',
            'notes' => 'Pago automático generado por el sistema'
        ];
        
        return $this->create($paymentData);
    }
    
    /**
     * Obtener resumen de pagos por año
     */
    public function getYearlyPaymentSummary(int $cardId, int $year): array
    {
        $sql = "SELECT 
            MONTH(payment_date) as month,
            COUNT(*) as payment_count,
            SUM(amount) as total_paid,
            AVG(amount) as average_payment,
            GROUP_CONCAT(DISTINCT payment_type) as payment_types
        FROM credit_card_payments 
        WHERE credit_card_id = ? 
        AND YEAR(payment_date) = ?
        GROUP BY MONTH(payment_date)
        ORDER BY month";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $year]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Buscar pagos por referencia
     */
    public function findByReference(string $referenceNumber): array
    {
        $sql = "SELECT ccp.*, cc.card_name, cc.bank_name
                FROM credit_card_payments ccp
                LEFT JOIN credit_cards cc ON ccp.credit_card_id = cc.id
                WHERE ccp.reference_number = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$referenceNumber]);
        
        return $stmt->fetchAll();
    }
}
