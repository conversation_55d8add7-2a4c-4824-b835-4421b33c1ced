-- =====================================================
-- DATOS INICIALES Y TRIGGERS
-- Control de Gastos Personales
-- =====================================================

USE control_gastos;

-- =====================================================
-- TRIGGERS PARA ACTUALIZAR SALDOS DE CUENTAS
-- =====================================================

DELIMITER $$

-- Trigger para actualizar saldo al insertar transacción
CREATE TRIGGER tr_transactions_insert_update_balance
AFTER INSERT ON transactions
FOR EACH ROW
BEGIN
    IF NEW.type = 'income' THEN
        UPDATE accounts 
        SET balance = balance + NEW.amount 
        WHERE id = NEW.account_id;
    ELSEIF NEW.type = 'expense' THEN
        UPDATE accounts 
        SET balance = balance - NEW.amount 
        WHERE id = NEW.account_id;
    END IF;
END$$

-- Trigger para actualizar saldo al modificar transacción
CREATE TRIGGER tr_transactions_update_update_balance
AFTER UPDATE ON transactions
FOR EACH ROW
BEGIN
    -- Revertir transacción anterior
    IF OLD.type = 'income' THEN
        UPDATE accounts 
        SET balance = balance - OLD.amount 
        WHERE id = OLD.account_id;
    ELSEIF OLD.type = 'expense' THEN
        UPDATE accounts 
        SET balance = balance + OLD.amount 
        WHERE id = OLD.account_id;
    END IF;
    
    -- Aplicar nueva transacción
    IF NEW.type = 'income' THEN
        UPDATE accounts 
        SET balance = balance + NEW.amount 
        WHERE id = NEW.account_id;
    ELSEIF NEW.type = 'expense' THEN
        UPDATE accounts 
        SET balance = balance - NEW.amount 
        WHERE id = NEW.account_id;
    END IF;
END$$

-- Trigger para actualizar saldo al eliminar transacción
CREATE TRIGGER tr_transactions_delete_update_balance
AFTER DELETE ON transactions
FOR EACH ROW
BEGIN
    IF OLD.type = 'income' THEN
        UPDATE accounts 
        SET balance = balance - OLD.amount 
        WHERE id = OLD.account_id;
    ELSEIF OLD.type = 'expense' THEN
        UPDATE accounts 
        SET balance = balance + OLD.amount 
        WHERE id = OLD.account_id;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- PROCEDIMIENTOS ALMACENADOS
-- =====================================================

DELIMITER $$

-- Procedimiento para obtener resumen financiero del usuario
CREATE PROCEDURE sp_get_financial_summary(IN p_user_id INT, IN p_month INT, IN p_year INT)
BEGIN
    SELECT 
        -- Ingresos del mes
        COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END), 0) as total_income,
        -- Gastos del mes
        COALESCE(SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END), 0) as total_expenses,
        -- Balance del mes
        COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE -t.amount END), 0) as monthly_balance,
        -- Total de cuentas activas
        (SELECT COUNT(*) FROM accounts WHERE user_id = p_user_id AND is_active = TRUE) as active_accounts,
        -- Saldo total en todas las cuentas
        (SELECT COALESCE(SUM(balance), 0) FROM accounts WHERE user_id = p_user_id AND is_active = TRUE) as total_balance
    FROM transactions t
    WHERE t.user_id = p_user_id 
        AND MONTH(t.transaction_date) = p_month 
        AND YEAR(t.transaction_date) = p_year;
END$$

-- Procedimiento para obtener gastos por categoría
CREATE PROCEDURE sp_get_expenses_by_category(IN p_user_id INT, IN p_month INT, IN p_year INT)
BEGIN
    SELECT 
        c.id,
        c.name as category_name,
        c.color,
        c.icon,
        COALESCE(SUM(t.amount), 0) as total_amount,
        COUNT(t.id) as transaction_count
    FROM categories c
    LEFT JOIN transactions t ON c.id = t.category_id 
        AND t.user_id = p_user_id 
        AND t.type = 'expense'
        AND MONTH(t.transaction_date) = p_month 
        AND YEAR(t.transaction_date) = p_year
    WHERE c.user_id = p_user_id AND c.is_active = TRUE
    GROUP BY c.id, c.name, c.color, c.icon
    ORDER BY total_amount DESC;
END$$

-- Procedimiento para obtener compromisos próximos a vencer
CREATE PROCEDURE sp_get_upcoming_commitments(IN p_user_id INT, IN p_days_ahead INT)
BEGIN
    SELECT 
        cm.id,
        cm.name,
        cm.amount,
        cm.due_date,
        cm.frequency,
        cm.is_recurring,
        a.name as account_name,
        c.name as category_name,
        sc.name as subcategory_name,
        DATEDIFF(cm.due_date, CURDATE()) as days_until_due
    FROM commitments cm
    INNER JOIN accounts a ON cm.account_id = a.id
    INNER JOIN categories c ON cm.category_id = c.id
    LEFT JOIN subcategories sc ON cm.subcategory_id = sc.id
    WHERE cm.user_id = p_user_id 
        AND cm.is_paid = FALSE
        AND cm.due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL p_days_ahead DAY)
    ORDER BY cm.due_date ASC;
END$$

DELIMITER ;

-- =====================================================
-- DATOS INICIALES - CATEGORÍAS PREDETERMINADAS
-- =====================================================

-- Función para insertar categorías predeterminadas para un usuario
DELIMITER $$

CREATE PROCEDURE sp_create_default_categories(IN p_user_id INT)
BEGIN
    DECLARE category_id INT;
    
    -- Categoría: Hogar
    INSERT INTO categories (user_id, name, description, color, icon) 
    VALUES (p_user_id, 'Hogar', 'Gastos relacionados con el hogar', '#28a745', 'fas fa-home');
    SET category_id = LAST_INSERT_ID();
    
    INSERT INTO subcategories (category_id, name) VALUES 
    (category_id, 'Arriendo/Hipoteca'),
    (category_id, 'Servicios Públicos'),
    (category_id, 'Mantenimiento'),
    (category_id, 'Decoración'),
    (category_id, 'Seguros');
    
    -- Categoría: Alimentación
    INSERT INTO categories (user_id, name, description, color, icon) 
    VALUES (p_user_id, 'Alimentación', 'Gastos en comida y bebidas', '#fd7e14', 'fas fa-utensils');
    SET category_id = LAST_INSERT_ID();
    
    INSERT INTO subcategories (category_id, name) VALUES 
    (category_id, 'Supermercado'),
    (category_id, 'Restaurantes'),
    (category_id, 'Comida Rápida'),
    (category_id, 'Bebidas'),
    (category_id, 'Delivery');
    
    -- Categoría: Transporte
    INSERT INTO categories (user_id, name, description, color, icon) 
    VALUES (p_user_id, 'Transporte', 'Gastos de movilidad', '#007bff', 'fas fa-car');
    SET category_id = LAST_INSERT_ID();
    
    INSERT INTO subcategories (category_id, name) VALUES 
    (category_id, 'Combustible'),
    (category_id, 'Transporte Público'),
    (category_id, 'Taxi/Uber'),
    (category_id, 'Mantenimiento Vehículo'),
    (category_id, 'Parqueadero');
    
    -- Categoría: Salud
    INSERT INTO categories (user_id, name, description, color, icon) 
    VALUES (p_user_id, 'Salud', 'Gastos médicos y de bienestar', '#dc3545', 'fas fa-heartbeat');
    SET category_id = LAST_INSERT_ID();
    
    INSERT INTO subcategories (category_id, name) VALUES 
    (category_id, 'Medicamentos'),
    (category_id, 'Consultas Médicas'),
    (category_id, 'Exámenes'),
    (category_id, 'Seguros Médicos'),
    (category_id, 'Gimnasio');
    
    -- Categoría: Entretenimiento
    INSERT INTO categories (user_id, name, description, color, icon) 
    VALUES (p_user_id, 'Entretenimiento', 'Gastos de ocio y diversión', '#6f42c1', 'fas fa-gamepad');
    SET category_id = LAST_INSERT_ID();
    
    INSERT INTO subcategories (category_id, name) VALUES 
    (category_id, 'Cine'),
    (category_id, 'Streaming'),
    (category_id, 'Videojuegos'),
    (category_id, 'Libros'),
    (category_id, 'Eventos');
    
    -- Categoría: Educación
    INSERT INTO categories (user_id, name, description, color, icon) 
    VALUES (p_user_id, 'Educación', 'Gastos educativos y formación', '#20c997', 'fas fa-graduation-cap');
    SET category_id = LAST_INSERT_ID();
    
    INSERT INTO subcategories (category_id, name) VALUES 
    (category_id, 'Cursos'),
    (category_id, 'Libros Académicos'),
    (category_id, 'Certificaciones'),
    (category_id, 'Material de Estudio'),
    (category_id, 'Matrícula');
    
    -- Categoría: Ingresos
    INSERT INTO categories (user_id, name, description, color, icon) 
    VALUES (p_user_id, 'Ingresos', 'Fuentes de ingresos', '#198754', 'fas fa-money-bill-wave');
    SET category_id = LAST_INSERT_ID();
    
    INSERT INTO subcategories (category_id, name) VALUES 
    (category_id, 'Salario'),
    (category_id, 'Freelance'),
    (category_id, 'Inversiones'),
    (category_id, 'Bonificaciones'),
    (category_id, 'Otros Ingresos');
    
END$$

DELIMITER ;

-- =====================================================
-- VISTAS ÚTILES
-- =====================================================

-- Vista para resumen de transacciones con información completa
CREATE VIEW v_transactions_summary AS
SELECT 
    t.id,
    t.user_id,
    u.first_name,
    u.last_name,
    a.name as account_name,
    a.type as account_type,
    c.name as category_name,
    c.color as category_color,
    c.icon as category_icon,
    sc.name as subcategory_name,
    t.type,
    t.amount,
    t.description,
    t.transaction_date,
    t.reference,
    t.notes,
    t.created_at
FROM transactions t
INNER JOIN users u ON t.user_id = u.id
INNER JOIN accounts a ON t.account_id = a.id
INNER JOIN categories c ON t.category_id = c.id
LEFT JOIN subcategories sc ON t.subcategory_id = sc.id;

-- Vista para compromisos con información completa
CREATE VIEW v_commitments_summary AS
SELECT 
    cm.id,
    cm.user_id,
    u.first_name,
    u.last_name,
    a.name as account_name,
    c.name as category_name,
    sc.name as subcategory_name,
    cm.name,
    cm.amount,
    cm.due_date,
    cm.frequency,
    cm.is_recurring,
    cm.is_paid,
    cm.description,
    cm.notification_enabled,
    cm.notification_days_before,
    DATEDIFF(cm.due_date, CURDATE()) as days_until_due,
    cm.created_at
FROM commitments cm
INNER JOIN users u ON cm.user_id = u.id
INNER JOIN accounts a ON cm.account_id = a.id
INNER JOIN categories c ON cm.category_id = c.id
LEFT JOIN subcategories sc ON cm.subcategory_id = sc.id;
