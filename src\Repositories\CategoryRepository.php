<?php

declare(strict_types=1);

namespace ControlGastos\Repositories;

use ControlGastos\Core\Database;
use ControlGastos\Models\Category;
use ControlGastos\Models\Subcategory;
use Exception;

/**
 * Repositorio de categorías
 * Maneja todas las operaciones de base de datos relacionadas con categorías
 */
class CategoryRepository
{
    private Database $database;

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Crear nueva categoría
     */
    public function create(Category $category): Category
    {
        $query = "
            INSERT INTO categories (
                user_id, name, description, color, icon, 
                is_active, created_at, updated_at
            ) VALUES (
                :user_id, :name, :description, :color, :icon,
                :is_active, :created_at, :updated_at
            )
        ";

        $params = [
            'user_id' => $category->getUserId(),
            'name' => $category->getName(),
            'description' => $category->getDescription(),
            'color' => $category->getColor(),
            'icon' => $category->getIcon(),
            'is_active' => $category->isActive() ? 1 : 0,
            'created_at' => $category->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $category->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        $id = $this->database->insert($query, $params);
        $category->setId($id);

        return $category;
    }

    /**
     * Buscar categoría por ID
     */
    public function findById(int $id): ?Category
    {
        $query = "SELECT * FROM categories WHERE id = :id";
        $result = $this->database->selectOne($query, ['id' => $id]);

        if (!$result) {
            return null;
        }

        $category = new Category($result);
        $this->loadSubcategories($category);
        
        return $category;
    }

    /**
     * Buscar categoría por ID y usuario
     */
    public function findByIdAndUser(int $id, int $userId): ?Category
    {
        $query = "SELECT * FROM categories WHERE id = :id AND user_id = :user_id";
        $result = $this->database->selectOne($query, ['id' => $id, 'user_id' => $userId]);

        if (!$result) {
            return null;
        }

        $category = new Category($result);
        $this->loadSubcategories($category);
        
        return $category;
    }

    /**
     * Obtener todas las categorías de un usuario
     */
    public function findByUser(int $userId, bool $activeOnly = false): array
    {
        $query = "SELECT * FROM categories WHERE user_id = :user_id";
        $params = ['user_id' => $userId];

        if ($activeOnly) {
            $query .= " AND is_active = 1";
        }

        $query .= " ORDER BY name ASC";

        $results = $this->database->select($query, $params);
        $categories = array_map(fn($row) => new Category($row), $results);

        // Cargar subcategorías para cada categoría
        foreach ($categories as $category) {
            $this->loadSubcategories($category);
        }

        return $categories;
    }

    /**
     * Actualizar categoría
     */
    public function update(Category $category): bool
    {
        $query = "
            UPDATE categories SET
                name = :name,
                description = :description,
                color = :color,
                icon = :icon,
                is_active = :is_active,
                updated_at = :updated_at
            WHERE id = :id
        ";

        $params = [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'description' => $category->getDescription(),
            'color' => $category->getColor(),
            'icon' => $category->getIcon(),
            'is_active' => $category->isActive() ? 1 : 0,
            'updated_at' => $category->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        return $this->database->update($query, $params) > 0;
    }

    /**
     * Eliminar categoría (soft delete)
     */
    public function delete(int $id): bool
    {
        $query = "
            UPDATE categories SET 
                is_active = 0,
                updated_at = NOW()
            WHERE id = :id
        ";

        return $this->database->update($query, ['id' => $id]) > 0;
    }

    /**
     * Eliminar categoría permanentemente
     */
    public function forceDelete(int $id): bool
    {
        return $this->database->transaction(function($db) use ($id) {
            // Eliminar subcategorías primero
            $this->database->delete("DELETE FROM subcategories WHERE category_id = :id", ['id' => $id]);
            
            // Eliminar categoría
            return $this->database->delete("DELETE FROM categories WHERE id = :id", ['id' => $id]) > 0;
        });
    }

    /**
     * Verificar si el nombre de categoría ya existe para el usuario
     */
    public function nameExists(string $name, int $userId, ?int $excludeId = null): bool
    {
        $query = "SELECT COUNT(*) as count FROM categories WHERE name = :name AND user_id = :user_id";
        $params = ['name' => $name, 'user_id' => $userId];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $result = $this->database->selectOne($query, $params);
        return $result['count'] > 0;
    }

    /**
     * Obtener categorías con conteo de subcategorías
     */
    public function findWithSubcategoryCount(int $userId): array
    {
        $query = "
            SELECT 
                c.*,
                COUNT(s.id) as subcategories_count
            FROM categories c
            LEFT JOIN subcategories s ON c.id = s.category_id AND s.is_active = 1
            WHERE c.user_id = :user_id
            GROUP BY c.id
            ORDER BY c.name ASC
        ";

        $results = $this->database->select($query, ['user_id' => $userId]);
        
        return array_map(function($row) {
            $category = new Category($row);
            // Agregar el conteo como propiedad adicional
            $categoryArray = $category->toArray();
            $categoryArray['subcategories_count'] = (int) $row['subcategories_count'];
            return $categoryArray;
        }, $results);
    }

    /**
     * Obtener estadísticas de categorías
     */
    public function getStats(int $userId): array
    {
        $query = "
            SELECT 
                COUNT(*) as total_categories,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_categories,
                (SELECT COUNT(*) FROM subcategories s 
                 INNER JOIN categories c ON s.category_id = c.id 
                 WHERE c.user_id = :user_id AND s.is_active = 1) as total_subcategories
            FROM categories
            WHERE user_id = :user_id
        ";

        return $this->database->selectOne($query, ['user_id' => $userId]) ?: [];
    }

    /**
     * Cargar subcategorías para una categoría
     */
    private function loadSubcategories(Category $category): void
    {
        if (!$category->getId()) {
            return;
        }

        $query = "
            SELECT * FROM subcategories 
            WHERE category_id = :category_id 
            ORDER BY name ASC
        ";

        $results = $this->database->select($query, ['category_id' => $category->getId()]);
        $subcategories = array_map(function($row) use ($category) {
            $subcategory = new Subcategory($row);
            $subcategory->setCategory($category);
            return $subcategory;
        }, $results);

        $category->setSubcategories($subcategories);
    }

    /**
     * Obtener categorías más utilizadas
     */
    public function getMostUsed(int $userId, int $limit = 10): array
    {
        $query = "
            SELECT 
                c.*,
                COUNT(t.id) as usage_count
            FROM categories c
            LEFT JOIN transactions t ON c.id = t.category_id
            WHERE c.user_id = :user_id AND c.is_active = 1
            GROUP BY c.id
            ORDER BY usage_count DESC, c.name ASC
            LIMIT :limit
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'limit' => $limit
        ]);

        return array_map(function($row) {
            $category = new Category($row);
            $this->loadSubcategories($category);
            return $category;
        }, $results);
    }

    /**
     * Buscar categorías por nombre
     */
    public function searchByName(int $userId, string $search): array
    {
        $query = "
            SELECT * FROM categories 
            WHERE user_id = :user_id 
            AND name LIKE :search 
            AND is_active = 1
            ORDER BY name ASC
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'search' => '%' . $search . '%'
        ]);

        return array_map(fn($row) => new Category($row), $results);
    }

    /**
     * Obtener categorías con paginación
     */
    public function getPaginated(int $userId, int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ['user_id = :user_id'];
        $params = ['user_id' => $userId];

        // Filtros
        if (isset($filters['is_active'])) {
            $whereConditions[] = "is_active = :is_active";
            $params['is_active'] = $filters['is_active'] ? 1 : 0;
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = "(name LIKE :search OR description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Consulta principal con conteo de subcategorías
        $query = "
            SELECT 
                c.*,
                COUNT(s.id) as subcategories_count
            FROM categories c
            LEFT JOIN subcategories s ON c.id = s.category_id AND s.is_active = 1
            {$whereClause}
            GROUP BY c.id
            ORDER BY c.name ASC 
            LIMIT :limit OFFSET :offset
        ";

        $params['limit'] = $perPage;
        $params['offset'] = $offset;

        $results = $this->database->select($query, $params);
        $categories = array_map(function($row) {
            $category = new Category($row);
            $this->loadSubcategories($category);
            return $category;
        }, $results);

        // Contar total
        $countQuery = "SELECT COUNT(*) as total FROM categories {$whereClause}";
        unset($params['limit'], $params['offset']);
        $totalResult = $this->database->selectOne($countQuery, $params);
        $total = $totalResult['total'];

        return [
            'data' => $categories,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
}
