<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Session;
use ControlGastos\Core\SimpleDatabase;
use PDO;

/**
 * Controlador de transacciones simplificado
 */
class TransactionController
{
    private PDO $db;
    private Session $session;

    public function __construct(SimpleDatabase $database, Session $session)
    {
        $this->db = $database->getConnection();
        $this->session = $session;
    }

    /**
     * Mostrar lista de transacciones
     */
    public function index(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        $this->render('transactions/index', [
            'title' => 'Transacciones',
            'transactions' => [],
            'pagination' => [],
            'accounts' => [],
            'categories' => [],
            'stats' => [],
            'filters' => [],
            'transaction_types' => [],
            'recurring_types' => []
        ]);
    }

    /**
     * Mostrar formulario de creación
     */
    public function create(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        $this->render('transactions/create', [
            'title' => 'Nueva Transacción'
        ]);
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): void
    {
        extract($data);
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            // Vista de fallback para transacciones
            echo "<div class='container mt-4'>";
            echo "<div class='alert alert-info'>";
            echo "<h4><span class='icon-emoji'>📊</span> Transacciones</h4>";
            echo "<p>Esta funcionalidad está en desarrollo y estará disponible próximamente.</p>";
            echo "<p><strong>Características planificadas:</strong></p>";
            echo "<ul>";
            echo "<li>📋 Lista paginada de transacciones con DataTables</li>";
            echo "<li>🔍 Filtros avanzados por fecha, categoría, cuenta</li>";
            echo "<li>📊 Exportación a Excel y PDF</li>";
            echo "<li>🖨️ Función de impresión</li>";
            echo "<li>📈 Gráficos y estadísticas en tiempo real</li>";
            echo "<li>🔄 Importación masiva desde CSV/Excel</li>";
            echo "<li>🏷️ Etiquetado y categorización automática</li>";
            echo "</ul>";
            echo "<div class='mt-3'>";
            echo "<a href='?route=dashboard' class='btn btn-primary me-2'>";
            echo "<span class='icon-emoji'>🏠</span> Volver al Dashboard";
            echo "</a>";
            echo "<a href='?route=accounts' class='btn btn-outline-primary'>";
            echo "<span class='icon-emoji'>📈</span> Control Integrado";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
    }
}
