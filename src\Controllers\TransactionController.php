<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Session;
use ControlGastos\Core\SimpleDatabase;
use PDO;

/**
 * Controlador de transacciones simplificado
 */
class TransactionController
{
    private PDO $db;
    private Session $session;

    public function __construct(SimpleDatabase $database, Session $session)
    {
        $this->db = $database->getConnection();
        $this->session = $session;
    }

    /**
     * Mostrar lista de transacciones
     */
    public function index(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        // Obtener filtros de la URL
        $filters = [
            'type' => $_GET['type'] ?? '',
            'account_id' => $_GET['account_id'] ?? '',
            'category_id' => $_GET['category_id'] ?? '',
            'start_date' => $_GET['start_date'] ?? '',
            'end_date' => $_GET['end_date'] ?? '',
            'search' => $_GET['search'] ?? ''
        ];

        // Obtener datos para los filtros
        $accounts = $this->getAccounts($userId);
        $categories = $this->getCategories($userId);
        $transactionTypes = [
            'income' => 'Ingresos',
            'expense' => 'Gastos'
        ];

        // Obtener transacciones (por ahora vacío, se implementará después)
        $transactions = [];
        $stats = [];
        $pagination = ['total' => 0];

        $this->render('transactions/index', [
            'title' => 'Transacciones',
            'transactions' => $transactions,
            'pagination' => $pagination,
            'accounts' => $accounts,
            'categories' => $categories,
            'stats' => $stats,
            'filters' => $filters,
            'transaction_types' => $transactionTypes,
            'recurring_types' => []
        ]);
    }

    /**
     * Mostrar formulario de creación
     */
    public function create(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        $this->render('transactions/create', [
            'title' => 'Nueva Transacción'
        ]);
    }

    /**
     * Obtener cuentas del usuario
     */
    private function getAccounts(int $userId): array
    {
        try {
            // Obtener cuentas bancarias y tarjetas de crédito
            $sql = "SELECT ba.id, CONCAT(b.bank_name, ' - ', ba.account_number) as name, 'bank_account' as type
                    FROM bank_accounts ba
                    JOIN banks b ON ba.bank_id = b.id
                    WHERE ba.user_id = ? AND ba.status = 'active'
                    UNION ALL
                    SELECT cc.id, CONCAT(b.bank_name, ' - ', cc.card_name) as name, 'credit_card' as type
                    FROM credit_cards cc
                    JOIN banks b ON cc.bank_id = b.id
                    WHERE cc.user_id = ? AND cc.is_active = 1
                    ORDER BY name";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId, $userId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Obtener categorías del usuario
     */
    private function getCategories(int $userId): array
    {
        try {
            $sql = "SELECT id, name FROM categories WHERE user_id = ? AND is_active = 1 ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): void
    {
        extract($data);
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            // Vista de fallback para transacciones
            echo "<div class='container mt-4'>";
            echo "<div class='alert alert-info'>";
            echo "<h4><span class='icon-emoji'>📊</span> Transacciones</h4>";
            echo "<p>Esta funcionalidad está en desarrollo y estará disponible próximamente.</p>";
            echo "<p><strong>Características planificadas:</strong></p>";
            echo "<ul>";
            echo "<li>📋 Lista paginada de transacciones con DataTables</li>";
            echo "<li>🔍 Filtros avanzados por fecha, categoría, cuenta</li>";
            echo "<li>📊 Exportación a Excel y PDF</li>";
            echo "<li>🖨️ Función de impresión</li>";
            echo "<li>📈 Gráficos y estadísticas en tiempo real</li>";
            echo "<li>🔄 Importación masiva desde CSV/Excel</li>";
            echo "<li>🏷️ Etiquetado y categorización automática</li>";
            echo "</ul>";
            echo "<div class='mt-3'>";
            echo "<a href='?route=dashboard' class='btn btn-primary me-2'>";
            echo "<span class='icon-emoji'>🏠</span> Volver al Dashboard";
            echo "</a>";
            echo "<a href='?route=accounts' class='btn btn-outline-primary'>";
            echo "<span class='icon-emoji'>📈</span> Control Integrado";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
    }
}
