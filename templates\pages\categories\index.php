<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Categorías y Subcategorías</h2>
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
            <i class="fas fa-plus"></i> Nueva Categoría
        </button>
        <a href="/categories/export" class="btn btn-outline-secondary">
            <i class="fas fa-download"></i> Exportar
        </a>
    </div>
</div>

<!-- Estadísticas -->
<?php if (!empty($stats)): ?>
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">Total Categorías</h5>
                <h3><?= $stats['total_categories'] ?? 0 ?></h3>
                <small class="text-muted"><?= $stats['active_categories'] ?? 0 ?> activas</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">Subcategorías</h5>
                <h3><?= $stats['total_subcategories'] ?? 0 ?></h3>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Promedio</h5>
                <h3><?= ($stats['total_categories'] ?? 0) > 0 ? round(($stats['total_subcategories'] ?? 0) / ($stats['total_categories'] ?? 1), 1) : 0 ?></h3>
                <small class="text-muted">subcategorías por categoría</small>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Buscador -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="searchInput" placeholder="Buscar categorías...">
            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Lista de Categorías -->
<?php if (empty($categories)): ?>
    <div class="text-center py-5">
        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No tienes categorías registradas</h4>
        <p class="text-muted">Crea tu primera categoría para organizar tus transacciones</p>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
            <i class="fas fa-plus"></i> Crear Primera Categoría
        </button>
    </div>
<?php else: ?>
    <div class="row" id="categoriesContainer">
        <?php foreach ($categories as $category): ?>
            <div class="col-md-6 col-lg-4 mb-4 category-item" data-category-id="<?= $category['id'] ?>">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center" 
                         style="background-color: <?= htmlspecialchars($category['color']) ?>20; border-left: 4px solid <?= htmlspecialchars($category['color']) ?>;">
                        <div>
                            <i class="<?= htmlspecialchars($category['icon']) ?> me-2" style="color: <?= htmlspecialchars($category['color']) ?>;"></i>
                            <strong><?= htmlspecialchars($category['name']) ?></strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editCategory(<?= $category['id'] ?>)">
                                    <i class="fas fa-edit"></i> Editar
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="addSubcategory(<?= $category['id'] ?>)">
                                    <i class="fas fa-plus"></i> Agregar Subcategoría
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="toggleCategoryStatus(<?= $category['id'] ?>)">
                                    <i class="fas fa-<?= $category['is_active'] ? 'eye-slash' : 'eye' ?>"></i> 
                                    <?= $category['is_active'] ? 'Desactivar' : 'Activar' ?>
                                </a></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteCategory(<?= $category['id'] ?>)">
                                    <i class="fas fa-trash"></i> Eliminar
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge <?= ($category['is_active'] ?? 1) ? 'bg-success' : 'bg-danger' ?>">
                                <?= $category['status_label'] ?? (($category['is_active'] ?? 1) ? 'Activa' : 'Inactiva') ?>
                            </span>
                            <small class="text-muted">
                                <?= $category['subcategories_count'] ?? ($category['subcategory_count'] ?? 0) ?> subcategorías
                            </small>
                        </div>
                        
                        <?php if ($category['description']): ?>
                            <p class="card-text text-muted small">
                                <?= htmlspecialchars($category['description']) ?>
                            </p>
                        <?php endif; ?>
                        
                        <!-- Subcategorías -->
                        <?php if (!empty($category['subcategories'])): ?>
                            <div class="mt-3">
                                <h6 class="text-muted mb-2">Subcategorías:</h6>
                                <div class="subcategories-list">
                                    <?php foreach (array_slice($category['subcategories'], 0, 3) as $subcategory): ?>
                                        <span class="badge bg-light text-dark me-1 mb-1" 
                                              style="border: 1px solid <?= htmlspecialchars($category['color']) ?>;">
                                            <?= htmlspecialchars($subcategory['name']) ?>
                                        </span>
                                    <?php endforeach; ?>
                                    <?php if (count($category['subcategories']) > 3): ?>
                                        <span class="badge bg-secondary">
                                            +<?= count($category['subcategories']) - 3 ?> más
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-3 d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                Creada: <?= date('d/m/Y', strtotime($category['created_at'])) ?>
                            </small>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewSubcategories(<?= $category['id'] ?>)">
                                <i class="fas fa-list"></i> Ver Todo
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- Modal para Categoría -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalTitle">Nueva Categoría</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="categoryForm">
                <div class="modal-body">
                    <input type="hidden" id="categoryId" name="category_id">
                    
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Nombre *</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Descripción</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="categoryColor" class="form-label">Color</label>
                            <select class="form-select" id="categoryColor" name="color" onchange="updateCategoryPreview()">
                                <option value="">Seleccionar color...</option>
                                <?php foreach ($colors as $value => $label): ?>
                                    <option value="<?= htmlspecialchars($value) ?>"
                                            style="background-color: <?= htmlspecialchars($value) ?>20;">
                                        <?= htmlspecialchars($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="categoryIcon" class="form-label">Icono</label>
                            <select class="form-select" id="categoryIcon" name="icon" onchange="updateCategoryPreview()">
                                <option value="">Seleccionar icono...</option>
                                <?php foreach ($icons as $value => $label): ?>
                                    <option value="<?= htmlspecialchars($value) ?>">
                                        <i class="<?= htmlspecialchars($value) ?>"></i> <?= htmlspecialchars($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Vista previa -->
                    <div class="mt-3">
                        <label class="form-label">Vista Previa</label>
                        <div class="card" style="max-width: 200px;">
                            <div class="card-header d-flex align-items-center" id="categoryPreviewHeader"
                                 style="background-color: #f8f9fa; border-left: 4px solid #dee2e6;">
                                <i id="categoryPreviewIcon" class="fas fa-tag me-2" style="color: #6c757d;"></i>
                                <span id="categoryPreviewName">Nombre de Categoría</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Subcategoría -->
<div class="modal fade" id="subcategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="subcategoryModalTitle">Nueva Subcategoría</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="subcategoryForm">
                <div class="modal-body">
                    <input type="hidden" id="subcategoryId" name="subcategory_id">
                    <input type="hidden" id="subcategoryCategoryId" name="category_id">
                    
                    <div class="mb-3">
                        <label for="subcategoryName" class="form-label">Nombre *</label>
                        <input type="text" class="form-control" id="subcategoryName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subcategoryDescription" class="form-label">Descripción</label>
                        <textarea class="form-control" id="subcategoryDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Variables globales
let currentCategoryId = null;
let currentSubcategoryId = null;

// Formulario de categoría
document.getElementById('categoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const categoryId = document.getElementById('categoryId').value;
    
    // Agregar route a formData
    formData.append('route', 'categories');
    formData.append('action', categoryId ? 'update' : 'store');
    if (categoryId) {
        formData.append('id', categoryId);
    }

    const url = window.location.pathname + window.location.search.split('&')[0];

    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        alert('Error al guardar la categoría');
    });
});

// Formulario de subcategoría
document.getElementById('subcategoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const subcategoryId = document.getElementById('subcategoryId').value;
    
    const url = subcategoryId ? `/subcategories/${subcategoryId}` : '/subcategories';
    const method = subcategoryId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        alert('Error al guardar la subcategoría');
    });
});

// Funciones de categorías
function editCategory(categoryId) {
    // Buscar datos de la categoría en el DOM o hacer fetch
    document.getElementById('categoryModalTitle').textContent = 'Editar Categoría';
    document.getElementById('categoryId').value = categoryId;
    
    // Aquí deberías cargar los datos de la categoría
    // Por simplicidad, se puede hacer con fetch o buscar en el DOM
    
    new bootstrap.Modal(document.getElementById('categoryModal')).show();
}

function deleteCategory(categoryId) {
    if (confirm('¿Está seguro de que desea eliminar esta categoría?')) {
        fetch(`/categories/${categoryId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('Error al eliminar la categoría');
        });
    }
}

function toggleCategoryStatus(categoryId) {
    fetch(`/categories/${categoryId}/toggle-status`, {
        method: 'PATCH',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        alert('Error al cambiar el estado');
    });
}

function addSubcategory(categoryId) {
    document.getElementById('subcategoryModalTitle').textContent = 'Nueva Subcategoría';
    document.getElementById('subcategoryId').value = '';
    document.getElementById('subcategoryCategoryId').value = categoryId;
    document.getElementById('subcategoryForm').reset();
    
    new bootstrap.Modal(document.getElementById('subcategoryModal')).show();
}

function viewSubcategories(categoryId) {
    // Implementar vista detallada de subcategorías
    console.log('Ver subcategorías de categoría:', categoryId);
}

// Búsqueda
document.getElementById('searchBtn').addEventListener('click', function() {
    const search = document.getElementById('searchInput').value;
    // Implementar búsqueda
    console.log('Buscar:', search);
});

// Limpiar formularios al cerrar modales
document.getElementById('categoryModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    document.getElementById('categoryModalTitle').textContent = 'Nueva Categoría';
});

document.getElementById('subcategoryModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('subcategoryForm').reset();
    document.getElementById('subcategoryId').value = '';
    document.getElementById('subcategoryCategoryId').value = '';
    document.getElementById('subcategoryModalTitle').textContent = 'Nueva Subcategoría';
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
