<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificación de Requisitos - Control de Gastos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding: 2rem 0;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        .check-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .check-item:last-child {
            border-bottom: none;
        }
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        .summary {
            margin-top: 2rem;
            padding: 1.5rem;
            border-radius: 10px;
        }
        .summary.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .summary.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .summary.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header text-center">
                        <h1 class="mb-0">
                            <i class="fas fa-server me-2"></i>
                            Verificación de Requisitos del Sistema
                        </h1>
                        <p class="mb-0 mt-2">Control de Gastos - Verificación Pre-Instalación</p>
                    </div>
                    <div class="card-body">
                        <?php
                        $checks = [];
                        $errors = 0;
                        $warnings = 0;

                        // Verificar versión de PHP
                        $phpVersion = PHP_VERSION;
                        $phpRequired = '8.0.0';
                        if (version_compare($phpVersion, $phpRequired, '>=')) {
                            $checks[] = [
                                'name' => 'Versión de PHP',
                                'status' => 'ok',
                                'message' => "PHP $phpVersion (Requerido: $phpRequired+)",
                                'icon' => 'fas fa-check-circle'
                            ];
                        } else {
                            $checks[] = [
                                'name' => 'Versión de PHP',
                                'status' => 'error',
                                'message' => "PHP $phpVersion - Se requiere $phpRequired o superior",
                                'icon' => 'fas fa-times-circle'
                            ];
                            $errors++;
                        }

                        // Verificar extensiones PHP requeridas
                        $requiredExtensions = [
                            'pdo' => 'PDO (Base de datos)',
                            'pdo_mysql' => 'PDO MySQL',
                            'openssl' => 'OpenSSL (Cifrado)',
                            'mbstring' => 'Multibyte String',
                            'json' => 'JSON',
                            'curl' => 'cURL (HTTP requests)',
                            'gd' => 'GD (Imágenes)',
                            'zip' => 'ZIP (Compresión)'
                        ];

                        foreach ($requiredExtensions as $ext => $description) {
                            if (extension_loaded($ext)) {
                                $checks[] = [
                                    'name' => "Extensión $description",
                                    'status' => 'ok',
                                    'message' => 'Instalada',
                                    'icon' => 'fas fa-check-circle'
                                ];
                            } else {
                                $status = in_array($ext, ['gd', 'zip']) ? 'warning' : 'error';
                                $checks[] = [
                                    'name' => "Extensión $description",
                                    'status' => $status,
                                    'message' => 'No instalada',
                                    'icon' => $status === 'error' ? 'fas fa-times-circle' : 'fas fa-exclamation-triangle'
                                ];
                                if ($status === 'error') $errors++;
                                else $warnings++;
                            }
                        }

                        // Verificar configuración PHP
                        $phpConfigs = [
                            'memory_limit' => ['128M', 'Límite de memoria'],
                            'max_execution_time' => ['30', 'Tiempo máximo de ejecución'],
                            'upload_max_filesize' => ['2M', 'Tamaño máximo de archivo'],
                            'post_max_size' => ['8M', 'Tamaño máximo POST']
                        ];

                        foreach ($phpConfigs as $config => $info) {
                            $current = ini_get($config);
                            $checks[] = [
                                'name' => $info[1],
                                'status' => 'ok',
                                'message' => "$config: $current",
                                'icon' => 'fas fa-info-circle'
                            ];
                        }

                        // Verificar permisos de directorios
                        $directories = [
                            '../logs' => 'Directorio de logs',
                            '../storage' => 'Directorio de almacenamiento',
                            '../storage/cache' => 'Cache',
                            '../storage/sessions' => 'Sesiones',
                            '../storage/uploads' => 'Uploads'
                        ];

                        foreach ($directories as $dir => $description) {
                            if (is_dir($dir)) {
                                if (is_writable($dir)) {
                                    $checks[] = [
                                        'name' => $description,
                                        'status' => 'ok',
                                        'message' => 'Directorio escribible',
                                        'icon' => 'fas fa-check-circle'
                                    ];
                                } else {
                                    $checks[] = [
                                        'name' => $description,
                                        'status' => 'warning',
                                        'message' => 'Directorio no escribible',
                                        'icon' => 'fas fa-exclamation-triangle'
                                    ];
                                    $warnings++;
                                }
                            } else {
                                $checks[] = [
                                    'name' => $description,
                                    'status' => 'warning',
                                    'message' => 'Directorio no existe (se creará automáticamente)',
                                    'icon' => 'fas fa-exclamation-triangle'
                                ];
                                $warnings++;
                            }
                        }

                        // Verificar archivo .env
                        if (file_exists('../.env')) {
                            $checks[] = [
                                'name' => 'Archivo de configuración',
                                'status' => 'ok',
                                'message' => 'Archivo .env encontrado',
                                'icon' => 'fas fa-check-circle'
                            ];
                        } else {
                            $checks[] = [
                                'name' => 'Archivo de configuración',
                                'status' => 'warning',
                                'message' => 'Archivo .env no encontrado (renombra .env.production)',
                                'icon' => 'fas fa-exclamation-triangle'
                            ];
                            $warnings++;
                        }

                        // Verificar conexión a base de datos
                        if (file_exists('../.env')) {
                            $env = [];
                            $lines = file('../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                            foreach ($lines as $line) {
                                if (strpos($line, '#') === 0) continue;
                                if (strpos($line, '=') !== false) {
                                    list($key, $value) = explode('=', $line, 2);
                                    $env[trim($key)] = trim($value);
                                }
                            }

                            try {
                                $pdo = new PDO(
                                    "mysql:host={$env['DB_HOST']};dbname={$env['DB_NAME']}",
                                    $env['DB_USER'],
                                    $env['DB_PASS']
                                );
                                $checks[] = [
                                    'name' => 'Conexión a base de datos',
                                    'status' => 'ok',
                                    'message' => 'Conexión exitosa',
                                    'icon' => 'fas fa-check-circle'
                                ];
                            } catch (Exception $e) {
                                $checks[] = [
                                    'name' => 'Conexión a base de datos',
                                    'status' => 'error',
                                    'message' => 'Error: ' . $e->getMessage(),
                                    'icon' => 'fas fa-times-circle'
                                ];
                                $errors++;
                            }
                        }

                        // Mostrar resultados
                        foreach ($checks as $check) {
                            $statusClass = "status-{$check['status']}";
                            echo "<div class='check-item'>";
                            echo "<div>";
                            echo "<i class='{$check['icon']} me-2'></i>";
                            echo "<strong>{$check['name']}</strong>";
                            echo "</div>";
                            echo "<div class='$statusClass'>";
                            echo $check['message'];
                            echo "</div>";
                            echo "</div>";
                        }
                        ?>
                    </div>
                </div>

                <!-- Resumen -->
                <?php
                if ($errors > 0) {
                    $summaryClass = 'error';
                    $summaryIcon = 'fas fa-times-circle';
                    $summaryTitle = 'Errores Críticos Encontrados';
                    $summaryMessage = "Se encontraron $errors errores críticos que deben solucionarse antes de continuar.";
                } elseif ($warnings > 0) {
                    $summaryClass = 'warning';
                    $summaryIcon = 'fas fa-exclamation-triangle';
                    $summaryTitle = 'Advertencias Encontradas';
                    $summaryMessage = "Se encontraron $warnings advertencias. La aplicación puede funcionar, pero se recomienda solucionarlas.";
                } else {
                    $summaryClass = 'success';
                    $summaryIcon = 'fas fa-check-circle';
                    $summaryTitle = '¡Sistema Listo!';
                    $summaryMessage = 'Todos los requisitos se cumplen. Puedes proceder con la instalación.';
                }
                ?>

                <div class="summary <?= $summaryClass ?>">
                    <h4>
                        <i class="<?= $summaryIcon ?> me-2"></i>
                        <?= $summaryTitle ?>
                    </h4>
                    <p class="mb-3"><?= $summaryMessage ?></p>
                    
                    <?php if ($errors === 0): ?>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="../install-cpanel.php" class="btn btn-success">
                                <i class="fas fa-play me-2"></i>
                                Continuar con la Instalación
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger mt-3">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Acciones Requeridas:</h6>
                            <ul class="mb-0">
                                <li>Actualiza PHP a la versión 8.0 o superior</li>
                                <li>Instala las extensiones PHP faltantes</li>
                                <li>Configura correctamente la base de datos</li>
                                <li>Verifica los permisos de directorios</li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Información adicional -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Información del Sistema
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Sistema Operativo:</strong><br>
                                <?= PHP_OS ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Servidor Web:</strong><br>
                                <?= $_SERVER['SERVER_SOFTWARE'] ?? 'No detectado' ?>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Versión PHP:</strong><br>
                                <?= PHP_VERSION ?>
                            </div>
                            <div class="col-md-6">
                                <strong>SAPI:</strong><br>
                                <?= PHP_SAPI ?>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Memoria Disponible:</strong><br>
                                <?= ini_get('memory_limit') ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Tiempo Máximo:</strong><br>
                                <?= ini_get('max_execution_time') ?>s
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="javascript:location.reload()" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-2"></i>
                        Verificar Nuevamente
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
