<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Admin Local - Control de Gastos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 2rem;
            text-align: center;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-outline-secondary {
            border-radius: 10px;
            padding: 12px 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h2 class="mb-0">
                            <i class="fas fa-user-shield me-2"></i>
                            Crear Usuario Administrador
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">Configuración inicial para desarrollo local</p>
                    </div>
                    <div class="card-body p-4">
                        
                        <?php
                        if ($_POST) {
                            try {
                                // Primero verificar conexión directa a MySQL
                                echo "<div class='alert alert-info'>";
                                echo "<h6><i class='fas fa-info-circle me-2'></i>Verificando conexión...</h6>";

                                // Probar conexión directa
                                try {
                                    $pdo = new PDO("mysql:host=localhost", "root", "");
                                    echo "<p>✅ Conexión a MySQL: OK</p>";

                                    // Verificar si existe la base de datos
                                    $stmt = $pdo->query("SHOW DATABASES LIKE 'control_gastos'");
                                    if ($stmt->rowCount() > 0) {
                                        echo "<p>✅ Base de datos 'control_gastos': Existe</p>";

                                        // Conectar a la base de datos específica
                                        $pdo = new PDO("mysql:host=localhost;dbname=control_gastos", "root", "");
                                        echo "<p>✅ Conexión a 'control_gastos': OK</p>";

                                        // Verificar si existe la tabla users
                                        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
                                        if ($stmt->rowCount() > 0) {
                                            echo "<p>✅ Tabla 'users': Existe</p>";

                                            // Verificar estructura de la tabla users
                                            $stmt = $pdo->query("DESCRIBE users");
                                            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                                            echo "<p><strong>Columnas encontradas:</strong> " . implode(', ', $columns) . "</p>";

                                            // Verificar columnas esenciales (adaptado a la estructura actual)
                                            $essentialColumns = ['id', 'first_name', 'last_name', 'email'];
                                            $missingEssential = array_diff($essentialColumns, $columns);

                                            // Verificar si tiene password o password_hash
                                            $hasPassword = in_array('password', $columns) || in_array('password_hash', $columns);

                                            if (!empty($missingEssential) || !$hasPassword) {
                                                echo "<p>❌ Columnas esenciales faltantes: " . implode(', ', $missingEssential) . "</p>";
                                                if (!$hasPassword) {
                                                    echo "<p>❌ No se encontró columna de contraseña (password o password_hash)</p>";
                                                }
                                                echo "<p><strong>Solución:</strong> Ve a <a href='fix-database.php'>fix-database.php</a> para reparar la estructura.</p>";
                                                echo "</div>";
                                                return;
                                            } else {
                                                echo "<p>✅ Estructura de tabla: Compatible</p>";

                                                // Mostrar qué columnas de contraseña y estado tiene
                                                if (in_array('password_hash', $columns)) {
                                                    echo "<p>📝 Usando columna: password_hash</p>";
                                                } else {
                                                    echo "<p>📝 Usando columna: password</p>";
                                                }

                                                if (in_array('status', $columns)) {
                                                    echo "<p>📝 Estado de usuario: columna 'status'</p>";
                                                } else if (in_array('is_verified', $columns)) {
                                                    echo "<p>📝 Estado de usuario: columna 'is_verified'</p>";
                                                } else {
                                                    echo "<p>📝 Estado de usuario: se asumirá activo</p>";
                                                }
                                            }
                                        } else {
                                            echo "<p>❌ Tabla 'users': No existe</p>";
                                            echo "<p><strong>Solución:</strong> Ejecuta las migraciones primero en <a href='setup-local.php'>setup-local.php</a></p>";
                                            echo "</div>";
                                            return;
                                        }
                                    } else {
                                        echo "<p>❌ Base de datos 'control_gastos': No existe</p>";
                                        echo "<p><strong>Solución:</strong> Crea la base de datos primero en <a href='setup-local.php'>setup-local.php</a></p>";
                                        echo "</div>";
                                        return;
                                    }
                                } catch (PDOException $e) {
                                    echo "<p>❌ Error de conexión: " . $e->getMessage() . "</p>";
                                    echo "<p><strong>Solución:</strong> Verifica que MySQL esté corriendo en XAMPP</p>";
                                    echo "</div>";
                                    return;
                                }

                                echo "</div>";

                                // Validar datos del formulario
                                $errors = [];

                                if (empty($_POST['first_name'])) {
                                    $errors[] = 'El nombre es requerido';
                                }

                                if (empty($_POST['last_name'])) {
                                    $errors[] = 'El apellido es requerido';
                                }

                                if (empty($_POST['email']) || !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
                                    $errors[] = 'Email válido es requerido';
                                }

                                if (empty($_POST['password']) || strlen($_POST['password']) < 8) {
                                    $errors[] = 'La contraseña debe tener al menos 8 caracteres';
                                }

                                if (!empty($errors)) {
                                    echo "<div class='alert alert-danger'>";
                                    echo "<ul class='mb-0'>";
                                    foreach ($errors as $error) {
                                        echo "<li>$error</li>";
                                    }
                                    echo "</ul>";
                                    echo "</div>";
                                } else {
                                    // Verificar si ya existe un usuario con este email
                                    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                                    $stmt->execute([$_POST['email']]);
                                    $existingUser = $stmt->fetch();

                                    if ($existingUser) {
                                        echo "<div class='alert alert-warning'>";
                                        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                                        echo "Ya existe un usuario con este email.";
                                        echo "</div>";
                                    } else {
                                        // Insertar usuario adaptado a la estructura actual
                                        $passwordColumn = in_array('password_hash', $columns) ? 'password_hash' : 'password';

                                        // Construir query dinámicamente según las columnas disponibles
                                        $insertColumns = ['first_name', 'last_name', 'email', $passwordColumn];
                                        $insertValues = ['?', '?', '?', '?'];
                                        $insertData = [
                                            $_POST['first_name'],
                                            $_POST['last_name'],
                                            $_POST['email'],
                                            password_hash($_POST['password'], PASSWORD_DEFAULT)
                                        ];

                                        // Agregar columnas opcionales si existen
                                        if (in_array('status', $columns)) {
                                            $insertColumns[] = 'status';
                                            $insertValues[] = '?';
                                            $insertData[] = 'active';
                                        }

                                        if (in_array('email_verified', $columns)) {
                                            $insertColumns[] = 'email_verified';
                                            $insertValues[] = '?';
                                            $insertData[] = 1;
                                        }

                                        if (in_array('role', $columns)) {
                                            $insertColumns[] = 'role';
                                            $insertValues[] = '?';
                                            $insertData[] = 'admin';
                                        }

                                        // Agregar timestamps si existen
                                        if (in_array('created_at', $columns)) {
                                            $insertColumns[] = 'created_at';
                                            $insertValues[] = 'NOW()';
                                        }

                                        if (in_array('updated_at', $columns)) {
                                            $insertColumns[] = 'updated_at';
                                            $insertValues[] = 'NOW()';
                                        }

                                        $sql = "INSERT INTO users (" . implode(', ', $insertColumns) . ") VALUES (" . implode(', ', $insertValues) . ")";

                                        echo "<div class='alert alert-info'>";
                                        echo "<small><strong>Query a ejecutar:</strong><br><code>" . htmlspecialchars($sql) . "</code></small>";
                                        echo "</div>";

                                        $stmt = $pdo->prepare($sql);
                                        $result = $stmt->execute($insertData);

                                        if ($result) {
                                            $userId = $pdo->lastInsertId();

                                            echo "<div class='alert alert-success'>";
                                            echo "<h5><i class='fas fa-check-circle me-2'></i>¡Usuario creado exitosamente!</h5>";
                                            echo "<p class='mb-2'><strong>ID:</strong> $userId</p>";
                                            echo "<p class='mb-2'><strong>Email:</strong> {$_POST['email']}</p>";
                                            echo "<p class='mb-3'><strong>Rol:</strong> Administrador</p>";
                                            echo "<div class='d-grid gap-2'>";
                                            echo "<a href='public/' class='btn btn-success'>";
                                            echo "<i class='fas fa-external-link-alt me-2'></i>";
                                            echo "Ir a la Aplicación";
                                            echo "</a>";
                                            echo "<a href='public/index.php?route=auth/login' class='btn btn-outline-primary'>";
                                            echo "<i class='fas fa-sign-in-alt me-2'></i>";
                                            echo "Página de Login";
                                            echo "</a>";
                                            echo "</div>";
                                            echo "</div>";

                                            // Verificar si existen las tablas categories y accounts antes de crear datos
                                            $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
                                            if ($stmt->rowCount() > 0) {
                                                // Crear algunas categorías por defecto
                                                $defaultCategories = [
                                                    ['Alimentación', '#FF6B6B', 'fas fa-utensils'],
                                                    ['Transporte', '#4ECDC4', 'fas fa-car'],
                                                    ['Entretenimiento', '#45B7D1', 'fas fa-gamepad'],
                                                    ['Salud', '#96CEB4', 'fas fa-heartbeat'],
                                                    ['Educación', '#FFEAA7', 'fas fa-graduation-cap'],
                                                    ['Hogar', '#DDA0DD', 'fas fa-home'],
                                                    ['Trabajo', '#98D8C8', 'fas fa-briefcase'],
                                                    ['Otros', '#F7DC6F', 'fas fa-ellipsis-h']
                                                ];

                                                try {
                                                    $categoryStmt = $pdo->prepare("
                                                        INSERT INTO categories (user_id, name, color, icon, type, created_at, updated_at)
                                                        VALUES (?, ?, ?, ?, 'expense', NOW(), NOW())
                                                    ");

                                                    foreach ($defaultCategories as $category) {
                                                        $categoryStmt->execute([$userId, $category[0], $category[1], $category[2]]);
                                                    }
                                                    echo "<p>✅ Categorías por defecto creadas</p>";
                                                } catch (Exception $e) {
                                                    echo "<p>⚠️ No se pudieron crear categorías: " . htmlspecialchars($e->getMessage()) . "</p>";
                                                }
                                            } else {
                                                echo "<p>⚠️ Tabla 'categories' no existe - categorías no creadas</p>";
                                            }

                                            // Verificar tabla accounts
                                            $stmt = $pdo->query("SHOW TABLES LIKE 'accounts'");
                                            if ($stmt->rowCount() > 0) {
                                                try {
                                                    // Crear cuenta por defecto
                                                    $accountStmt = $pdo->prepare("
                                                        INSERT INTO accounts (user_id, name, type, balance, currency, is_active, created_at, updated_at)
                                                        VALUES (?, 'Efectivo', 'cash', 0.00, 'MXN', 1, NOW(), NOW())
                                                    ");
                                                    $accountStmt->execute([$userId]);
                                                    echo "<p>✅ Cuenta por defecto creada</p>";
                                                } catch (Exception $e) {
                                                    echo "<p>⚠️ No se pudo crear cuenta: " . htmlspecialchars($e->getMessage()) . "</p>";
                                                }
                                            } else {
                                                echo "<p>⚠️ Tabla 'accounts' no existe - cuenta no creada</p>";
                                            }

                                            echo "<div class='alert alert-info mt-3'>";
                                            echo "<i class='fas fa-info-circle me-2'></i>";
                                            echo "Se han creado categorías y una cuenta por defecto para comenzar.";
                                            echo "</div>";

                                            // No mostrar el formulario
                                            $showForm = false;
                                        } else {
                                            echo "<div class='alert alert-danger'>";
                                            echo "<i class='fas fa-times-circle me-2'></i>";
                                            echo "Error al crear el usuario. Verifica la conexión a la base de datos.";
                                            echo "</div>";
                                        }
                                    }
                                }

                            } catch (Exception $e) {
                                echo "<div class='alert alert-danger'>";
                                echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>Error Detallado</h5>";
                                echo "<p><strong>Mensaje:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
                                echo "<p><strong>Archivo:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
                                echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
                                echo "<hr>";
                                echo "<h6>Pasos para solucionar:</h6>";
                                echo "<ol>";
                                echo "<li>Verifica que XAMPP esté funcionando</li>";
                                echo "<li>Asegúrate de que MySQL esté iniciado (verde en XAMPP)</li>";
                                echo "<li>Ve a <a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></li>";
                                echo "<li>Verifica que existe la base de datos 'control_gastos'</li>";
                                echo "<li>Si no existe, ve a <a href='setup-local.php'>setup-local.php</a> primero</li>";
                                echo "</ol>";
                                echo "</div>";
                            }
                        }
                        
                        // Mostrar formulario si no se ha creado el usuario exitosamente
                        if (!isset($showForm) || $showForm !== false):
                        ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nombre
                                </label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= htmlspecialchars($_POST['first_name'] ?? 'Admin') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="last_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>Apellido
                                </label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= htmlspecialchars($_POST['last_name'] ?? 'Local') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= htmlspecialchars($_POST['email'] ?? 'admin@localhost') ?>" required>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Contraseña
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="Mínimo 8 caracteres" required minlength="8">
                                <div class="form-text">
                                    <small>Para desarrollo local puedes usar: <code>admin123</code></small>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Crear Usuario Administrador
                                </button>
                                <a href="setup-local.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Volver a Configuración
                                </a>
                            </div>
                        </form>
                        
                        <?php endif; ?>
                        
                    </div>
                </div>
                
                <!-- Información adicional -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6><i class="fas fa-info-circle me-2"></i>Información de Desarrollo</h6>
                        <ul class="mb-0">
                            <li><strong>Entorno:</strong> Desarrollo local (XAMPP)</li>
                            <li><strong>Base de datos:</strong> control_gastos</li>
                            <li><strong>Usuario BD:</strong> root (sin contraseña)</li>
                            <li><strong>URL:</strong> http://localhost/controlGastos/public/</li>
                        </ul>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</body>
</html>
