<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Session;
use ControlGastos\Core\SimpleDatabase;
use PDO;
use Exception;

/**
 * Controlador de categorías simplificado
 */
class CategoryController
{
    private PDO $db;
    private Session $session;

    public function __construct(SimpleDatabase $database, Session $session)
    {
        $this->db = $database->getConnection();
        $this->session = $session;
    }

    /**
     * Mostrar lista de categorías
     */
    public function index(): void
    {
        // Asegurar codificación UTF-8
        header('Content-Type: text/html; charset=UTF-8');

        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        // Obtener categorías del usuario
        $categories = [];
        $stats = [];

        try {
            // Obtener categorías con subcategorías
            $sql = "
                SELECT c.*,
                       COUNT(s.id) as subcategory_count
                FROM categories c
                LEFT JOIN subcategories s ON c.id = s.category_id AND s.is_active = 1
                WHERE c.user_id = ? AND c.is_active = 1
                GROUP BY c.id
                ORDER BY c.name ASC
            ";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            $categories = $stmt->fetchAll();

            // Obtener estadísticas de categorías y subcategorías
            $sql = "
                SELECT
                    COUNT(*) as total_categories,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_categories,
                    (SELECT COUNT(*) FROM subcategories s
                     INNER JOIN categories c ON s.category_id = c.id
                     WHERE c.user_id = ? AND s.is_active = 1) as total_subcategories
                FROM categories
                WHERE user_id = ?
            ";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId, $userId]);
            $statsData = $stmt->fetch();

            $stats = [
                'total_categories' => $statsData['total_categories'] ?? 0,
                'active_categories' => $statsData['active_categories'] ?? 0,
                'total_subcategories' => $statsData['total_subcategories'] ?? 0
            ];

            // Procesar cada categoría para agregar campos adicionales
            foreach ($categories as &$category) {
                // Obtener subcategorías
                $sql = "SELECT * FROM subcategories WHERE category_id = ? AND is_active = 1 ORDER BY name ASC";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$category['id']]);
                $category['subcategories'] = $stmt->fetchAll();

                // Agregar campos adicionales requeridos por la vista
                $category['status_label'] = $category['is_active'] ? 'Activa' : 'Inactiva';
                $category['subcategories_count'] = $category['subcategory_count'] ?? 0;

                // Formatear fecha de creación
                if (isset($category['created_at'])) {
                    $date = new \DateTime($category['created_at']);
                    $category['created_at_formatted'] = $date->format('d/m/Y');
                }
            }

        } catch (Exception $e) {
            // En caso de error, usar arrays vacíos
            $categories = [];
            $stats = [];
        }

        // Obtener colores disponibles
        $colors = [
            '#007bff' => 'Azul',
            '#28a745' => 'Verde',
            '#dc3545' => 'Rojo',
            '#ffc107' => 'Amarillo',
            '#17a2b8' => 'Cian',
            '#6f42c1' => 'Púrpura',
            '#fd7e14' => 'Naranja',
            '#20c997' => 'Verde Agua',
            '#e83e8c' => 'Rosa',
            '#6c757d' => 'Gris'
        ];

        // Obtener iconos disponibles
        $icons = [
            'fas fa-shopping-cart' => 'Compras',
            'fas fa-utensils' => 'Comida',
            'fas fa-car' => 'Transporte',
            'fas fa-home' => 'Hogar',
            'fas fa-medkit' => 'Salud',
            'fas fa-graduation-cap' => 'Educación',
            'fas fa-gamepad' => 'Entretenimiento',
            'fas fa-tshirt' => 'Ropa',
            'fas fa-gas-pump' => 'Combustible',
            'fas fa-phone' => 'Servicios',
            'fas fa-gift' => 'Regalos',
            'fas fa-plane' => 'Viajes',
            'fas fa-dumbbell' => 'Deportes',
            'fas fa-book' => 'Libros',
            'fas fa-music' => 'Música',
            'fas fa-coffee' => 'Café',
            'fas fa-pizza-slice' => 'Comida Rápida',
            'fas fa-bus' => 'Transporte Público',
            'fas fa-credit-card' => 'Pagos',
            'fas fa-coins' => 'Ahorros'
        ];

        $this->render('categories/index', [
            'title' => 'Categorías',
            'categories' => $categories,
            'stats' => $stats,
            'colors' => $colors,
            'icons' => $icons,
            'csrf_token' => '',
            'success' => '',
            'error' => ''
        ]);
    }

    /**
     * Crear nueva categoría
     */
    public function store(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('HTTP/1.1 401 Unauthorized');
            echo json_encode(['success' => false, 'message' => 'No autorizado']);
            exit;
        }

        try {
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $color = $_POST['color'] ?? '#007bff';
            $icon = $_POST['icon'] ?? 'fas fa-tag';

            if (empty($name)) {
                echo json_encode(['success' => false, 'message' => 'El nombre es obligatorio']);
                exit;
            }

            // Verificar si ya existe una categoría con ese nombre
            $sql = "SELECT id FROM categories WHERE user_id = ? AND name = ? AND is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId, $name]);

            if ($stmt->fetch()) {
                echo json_encode(['success' => false, 'message' => 'Ya existe una categoría con ese nombre']);
                exit;
            }

            // Insertar nueva categoría
            $sql = "INSERT INTO categories (user_id, name, description, color, icon, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, 1, NOW())";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$userId, $name, $description, $color, $icon]);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Categoría creada exitosamente']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Error al crear la categoría']);
            }

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error interno del servidor']);
        }
        exit;
    }

    /**
     * Actualizar categoría
     */
    public function update(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('HTTP/1.1 401 Unauthorized');
            echo json_encode(['success' => false, 'message' => 'No autorizado']);
            exit;
        }

        try {
            $categoryId = $_POST['id'] ?? '';
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $color = $_POST['color'] ?? '#007bff';
            $icon = $_POST['icon'] ?? 'fas fa-tag';

            if (empty($categoryId) || empty($name)) {
                echo json_encode(['success' => false, 'message' => 'ID y nombre son obligatorios']);
                exit;
            }

            // Verificar que la categoría pertenece al usuario
            $sql = "SELECT id FROM categories WHERE id = ? AND user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$categoryId, $userId]);

            if (!$stmt->fetch()) {
                echo json_encode(['success' => false, 'message' => 'Categoría no encontrada']);
                exit;
            }

            // Actualizar categoría
            $sql = "UPDATE categories SET name = ?, description = ?, color = ?, icon = ?, updated_at = NOW()
                    WHERE id = ? AND user_id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$name, $description, $color, $icon, $categoryId, $userId]);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Categoría actualizada exitosamente']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Error al actualizar la categoría']);
            }

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error interno del servidor']);
        }
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): void
    {
        extract($data);
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            // Vista de fallback para categorías
            echo "<div class='container mt-4'>";
            echo "<div class='alert alert-info'>";
            echo "<h4><span class='icon-emoji'>🏷️</span> Categorías y Subcategorías</h4>";
            echo "<p>Este módulo está en desarrollo y estará disponible próximamente.</p>";
            echo "<p><strong>Funcionalidades planificadas:</strong></p>";
            echo "<ul>";
            echo "<li>🏷️ Gestión de categorías principales</li>";
            echo "<li>📂 Subcategorías organizadas</li>";
            echo "<li>🎨 Colores personalizados</li>";
            echo "<li>📊 Estadísticas por categoría</li>";
            echo "<li>🔍 Filtros y búsqueda</li>";
            echo "<li>📈 Reportes de gastos por categoría</li>";
            echo "<li>🔄 Importación/Exportación</li>";
            echo "</ul>";
            echo "<p><strong>Importancia:</strong></p>";
            echo "<div class='alert alert-warning'>";
            echo "<p>Las categorías son fundamentales para:</p>";
            echo "<ul>";
            echo "<li>📊 Generar reportes detallados</li>";
            echo "<li>📈 Crear gráficas de gastos</li>";
            echo "<li>🎯 Análisis de patrones de gasto</li>";
            echo "<li>💰 Control presupuestario</li>";
            echo "</ul>";
            echo "</div>";
            echo "<div class='mt-3'>";
            echo "<a href='?route=dashboard' class='btn btn-primary me-2'>";
            echo "<span class='icon-emoji'>🏠</span> Volver al Dashboard";
            echo "</a>";
            echo "<a href='?route=accounts' class='btn btn-outline-primary'>";
            echo "<span class='icon-emoji'>📈</span> Control Integrado";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
    }
}
