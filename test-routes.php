<?php
/**
 * Test de Rutas de Tarjetas de Crédito
 * Verifica que todas las rutas respondan correctamente
 */

echo "🛣️ TESTING DE RUTAS - TARJETAS DE CRÉDITO\n";
echo "=" . str_repeat("=", 60) . "\n\n";

$baseUrl = 'http://localhost/controlGastos/public/';
$routes = [
    'credit-cards' => 'Dashboard de tarjetas',
    'credit-cards/create' => 'Crear nueva tarjeta',
    'credit-cards/show&id=2' => 'Ver detalles tarjeta ID 2',
    'credit-cards/edit&id=2' => 'Editar tarjeta ID 2',
    'credit-cards/reports&id=2' => 'Reportes tarjeta ID 2'
];

$results = [];

foreach ($routes as $route => $description) {
    $url = $baseUrl . '?route=' . $route;
    
    echo "🔍 Probando: {$description}\n";
    echo "   URL: {$url}\n";
    
    $startTime = microtime(true);
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => "User-Agent: RouteTestSuite/1.0\r\n"
        ]
    ]);
    
    $content = @file_get_contents($url, false, $context);
    $loadTime = round((microtime(true) - $startTime) * 1000, 2);
    
    if ($content !== false) {
        // Verificar que no sea una página de error
        if (strpos($content, 'Fatal error') === false && 
            strpos($content, 'Parse error') === false &&
            strpos($content, 'Warning') === false) {
            
            // Verificar contenido específico según la ruta
            $contentChecks = [
                'credit-cards' => ['Mis Tarjetas', 'Nueva Tarjeta'],
                'credit-cards/create' => ['Nueva Tarjeta de Crédito', 'card_name'],
                'credit-cards/show&id=2' => ['Visa Principal', 'Resumen Financiero'],
                'credit-cards/edit&id=2' => ['Editar Tarjeta', 'card_name'],
                'credit-cards/reports&id=2' => ['Reportes', 'Estadísticas']
            ];
            
            $routeKey = explode('&', $route)[0]; // Remover parámetros para la verificación
            $expectedContent = $contentChecks[$routeKey] ?? [];
            
            $contentFound = true;
            $missingContent = [];
            
            foreach ($expectedContent as $needle) {
                if (strpos($content, $needle) === false) {
                    $contentFound = false;
                    $missingContent[] = $needle;
                }
            }
            
            if ($contentFound) {
                echo "   ✅ ÉXITO: Carga correctamente ({$loadTime}ms)\n";
                $results[$route] = true;
            } else {
                echo "   ⚠️ PARCIAL: Carga pero falta contenido: " . implode(', ', $missingContent) . "\n";
                $results[$route] = 'partial';
            }
        } else {
            echo "   ❌ ERROR: Contiene errores PHP\n";
            $results[$route] = false;
        }
    } else {
        echo "   ❌ ERROR: No responde\n";
        $results[$route] = false;
    }
    
    echo "\n";
}

// Reporte final
echo "📋 REPORTE FINAL DE RUTAS\n";
echo "=" . str_repeat("=", 60) . "\n";

$total = count($results);
$success = count(array_filter($results, fn($r) => $r === true));
$partial = count(array_filter($results, fn($r) => $r === 'partial'));
$failed = count(array_filter($results, fn($r) => $r === false));

echo "Total de rutas probadas: {$total}\n";
echo "✅ Exitosas: {$success}\n";
echo "⚠️ Parciales: {$partial}\n";
echo "❌ Fallidas: {$failed}\n";
echo "📊 Porcentaje de éxito: " . round((($success + $partial) / $total) * 100, 2) . "%\n\n";

if ($failed > 0) {
    echo "❌ RUTAS FALLIDAS:\n";
    foreach ($results as $route => $result) {
        if ($result === false) {
            echo "   • {$route}\n";
        }
    }
    echo "\n";
}

if ($partial > 0) {
    echo "⚠️ RUTAS PARCIALES (cargan pero falta contenido):\n";
    foreach ($results as $route => $result) {
        if ($result === 'partial') {
            echo "   • {$route}\n";
        }
    }
    echo "\n";
}

if ($success === $total) {
    echo "🎉 ¡TODAS LAS RUTAS FUNCIONAN PERFECTAMENTE!\n";
} elseif ($success + $partial === $total) {
    echo "🎯 ¡TODAS LAS RUTAS CARGAN! (Algunas necesitan ajustes de contenido)\n";
} else {
    echo "⚠️ Algunas rutas necesitan corrección\n";
}

echo "\n🔗 URLS PARA TESTING MANUAL:\n";
foreach ($routes as $route => $description) {
    $url = $baseUrl . '?route=' . $route;
    echo "• {$description}: {$url}\n";
}

echo "\n🎯 TESTING DE RUTAS COMPLETADO\n";
