<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Estilos - Control de Gastos</title>
    <?php
    require_once 'src/helpers/assets.php';
    echo includeBasicCSS();
    ?>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🎨 Prueba de Estilos</h1>
                
                <!-- Test Bootstrap -->
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> Bootstrap CSS</h5>
                    <p>Si ves este alert con estilos verdes, Bootstrap está funcionando.</p>
                </div>
                
                <!-- Test FontAwesome -->
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> FontAwesome Icons</h5>
                    <p>Si ves los iconos correctamente, FontAwesome está funcionando.</p>
                    <div class="mt-2">
                        <i class="fas fa-home fa-2x me-3"></i>
                        <i class="fas fa-user fa-2x me-3"></i>
                        <i class="fas fa-chart-line fa-2x me-3"></i>
                        <i class="fas fa-wallet fa-2x me-3"></i>
                        <i class="fas fa-cog fa-2x"></i>
                    </div>
                </div>
                
                <!-- Test Custom CSS -->
                <div class="alert alert-warning">
                    <h5><i class="fas fa-palette"></i> CSS Personalizado</h5>
                    <p>Si ves gradientes y estilos personalizados, el CSS custom está funcionando.</p>
                </div>
                
                <!-- Test Cards -->
                <div class="row g-4 mt-4">
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-wallet fa-2x mb-2"></i>
                                <h5>Cuentas</h5>
                                <h3>5</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                                <h5>Balance</h5>
                                <h3>$1,234.56</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-up fa-2x mb-2"></i>
                                <h5>Ingresos</h5>
                                <h3>$2,500.00</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-down fa-2x mb-2"></i>
                                <h5>Gastos</h5>
                                <h3>$1,265.44</h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Test Buttons -->
                <div class="mt-5">
                    <h4>Botones de Prueba</h4>
                    <div class="d-flex gap-2 flex-wrap">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> Primario
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-check"></i> Éxito
                        </button>
                        <button class="btn btn-danger">
                            <i class="fas fa-times"></i> Peligro
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-exclamation"></i> Advertencia
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-info"></i> Información
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> Outline
                        </button>
                    </div>
                </div>
                
                <!-- Test Table -->
                <div class="mt-5">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-table"></i> Tabla de Prueba</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Descripción</th>
                                        <th>Monto</th>
                                        <th>Estado</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>Compra de supermercado</td>
                                        <td>$125.50</td>
                                        <td><span class="badge bg-success">Completado</span></td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>Pago de servicios</td>
                                        <td>$89.99</td>
                                        <td><span class="badge bg-warning">Pendiente</span></td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>Salario mensual</td>
                                        <td>$2,500.00</td>
                                        <td><span class="badge bg-success">Completado</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Test Results -->
                <div class="mt-5">
                    <div class="card">
                        <div class="card-header bg-gradient-primary text-white">
                            <h5><i class="fas fa-clipboard-check"></i> Resultados de la Prueba</h5>
                        </div>
                        <div class="card-body">
                            <div id="test-results">
                                <p><strong>Verificando estilos...</strong></p>
                            </div>
                            
                            <div class="mt-3">
                                <a href="public/" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> Ir a la Aplicación
                                </a>
                                <a href="public/?route=auth/login" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt"></i> Página de Login
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo includeBasicJS(); ?>
    <script>
        // Verificar que los estilos se cargaron
        document.addEventListener('DOMContentLoaded', function() {
            const results = document.getElementById('test-results');
            let html = '<ul class="list-unstyled">';
            
            // Test Bootstrap
            const bootstrapTest = window.getComputedStyle(document.querySelector('.alert')).backgroundColor;
            html += `<li><i class="fas fa-${bootstrapTest !== 'rgba(0, 0, 0, 0)' ? 'check text-success' : 'times text-danger'}"></i> Bootstrap CSS: ${bootstrapTest !== 'rgba(0, 0, 0, 0)' ? 'OK' : 'FALLO'}</li>`;
            
            // Test FontAwesome
            const iconTest = window.getComputedStyle(document.querySelector('.fas'), ':before').content;
            html += `<li><i class="fas fa-${iconTest !== 'none' && iconTest !== '' ? 'check text-success' : 'times text-danger'}"></i> FontAwesome: ${iconTest !== 'none' && iconTest !== '' ? 'OK' : 'FALLO'}</li>`;
            
            // Test Custom CSS
            const customTest = window.getComputedStyle(document.querySelector('.stat-card')).background;
            html += `<li><i class="fas fa-${customTest.includes('gradient') ? 'check text-success' : 'times text-danger'}"></i> CSS Personalizado: ${customTest.includes('gradient') ? 'OK' : 'FALLO'}</li>`;
            
            html += '</ul>';
            
            if (bootstrapTest !== 'rgba(0, 0, 0, 0)' && customTest.includes('gradient')) {
                html += '<div class="alert alert-success mt-3"><strong>¡Todos los estilos funcionan correctamente!</strong> Puedes proceder a usar la aplicación.</div>';
            } else {
                html += '<div class="alert alert-warning mt-3"><strong>Algunos estilos no se cargaron.</strong> Verifica la consola del navegador para más detalles.</div>';
            }
            
            results.innerHTML = html;
        });
    </script>
</body>
</html>
