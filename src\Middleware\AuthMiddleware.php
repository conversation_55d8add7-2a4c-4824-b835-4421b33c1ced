<?php

declare(strict_types=1);

namespace ControlGastos\Middleware;

use ControlGastos\Core\Container;
use ControlGastos\Core\Session;

/**
 * Middleware de autenticación
 * Verifica que el usuario esté autenticado
 */
class AuthMiddleware
{
    private Session $session;

    public function __construct(Session $session)
    {
        $this->session = $session;
    }

    public function handle(Container $container): void
    {
        // Verificar si el usuario está autenticado
        if (!$this->isAuthenticated()) {
            $this->redirectToLogin();
        }

        // Verificar si la sesión es válida
        if (!$this->isSessionValid()) {
            $this->logout();
            $this->redirectToLogin();
        }

        // Actualizar última actividad
        $this->updateLastActivity();
    }

    /**
     * Verificar si el usuario está autenticado
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('user_id') && 
               $this->session->has('user_authenticated') && 
               $this->session->get('user_authenticated') === true;
    }

    /**
     * Verificar si la sesión es válida
     */
    private function isSessionValid(): bool
    {
        // Verificar tiempo de inactividad
        $lastActivity = $this->session->get('last_activity', 0);
        $maxInactivity = 3600; // 1 hora
        
        if (time() - $lastActivity > $maxInactivity) {
            return false;
        }

        // Verificar IP (opcional, puede causar problemas con proxies)
        $sessionIp = $this->session->get('user_ip');
        $currentIp = $_SERVER['REMOTE_ADDR'] ?? '';
        
        if ($sessionIp && $sessionIp !== $currentIp) {
            // Log de seguridad
            error_log("IP mismatch for user: " . $this->session->get('user_id'));
            return false;
        }

        return true;
    }

    /**
     * Actualizar última actividad
     */
    private function updateLastActivity(): void
    {
        $this->session->set('last_activity', time());
    }

    /**
     * Cerrar sesión
     */
    private function logout(): void
    {
        $this->session->remove('user_id');
        $this->session->remove('user_authenticated');
        $this->session->remove('user_email');
        $this->session->remove('user_name');
        $this->session->remove('user_ip');
        $this->session->remove('last_activity');
    }

    /**
     * Redirigir al login
     */
    private function redirectToLogin(): void
    {
        // Si es una request AJAX, devolver JSON
        if ($this->isAjaxRequest()) {
            http_response_code(401);
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => 'Sesión expirada',
                'redirect' => '/auth/login'
            ]);
            exit;
        }

        // Guardar URL de destino para redirigir después del login
        $currentUrl = $_SERVER['REQUEST_URI'] ?? '/';
        if ($currentUrl !== '/auth/login' && $currentUrl !== '/auth/logout') {
            $this->session->set('intended_url', $currentUrl);
        }

        // Redirigir al login
        header('Location: /auth/login');
        exit;
    }

    /**
     * Verificar si es una request AJAX
     */
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
