<?php
// La autenticación ya se verifica en el controlador
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0"><span class="icon-emoji me-2">💳</span>Mis Tarjetas de Crédito</h1>
                    <p class="text-muted">Gestiona tus tarjetas de crédito y controla tus gastos</p>
                </div>
                <a href="/controlGastos/public/?route=credit-cards/create" class="btn btn-primary">
                    <span class="icon-emoji me-2">➕</span>Nueva Tarjeta
                </a>
            </div>
        </div>
    </div>

    <!-- Mensaje de ayuda para gestión de bancos -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <span class="icon-emoji me-2">💡</span>
                <strong>¿Necesitas agregar un banco emisor?</strong>
                Puedes gestionar tus bancos desde:
                <strong>Menú → Gestión Financiera → Bancos</strong>
                o
                <a href="?route=banks" class="alert-link">hacer clic aquí</a>.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Resumen General -->
    <?php if (!empty($cards)): ?>
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Tarjetas</h6>
                            <h3 class="mb-0"><?= count($cards) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <span class="icon-emoji" style="font-size: 2rem;">💳</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Cupo Total</h6>
                            <h3 class="mb-0">$<?= number_format(array_sum(array_column($cards, 'card.credit_limit')), 0) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <span class="icon-emoji" style="font-size: 2rem;">💰</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Cupo Consumido Total</h6>
                            <h3 class="mb-0">$<?= number_format(array_sum(array_column($cards, 'current_balance')), 0) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Disponible</h6>
                            <h3 class="mb-0">$<?= number_format(array_sum(array_column($cards, 'available_credit')), 0) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Tarjetas de Crédito -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-credit-card me-2"></i>Mis Tarjetas
                        </h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                                <option value="">Todas las tarjetas</option>
                                <option value="active">Solo activas</option>
                                <option value="blocked">Bloqueadas</option>
                                <option value="cancelled">Canceladas</option>
                                <option value="expired">Vencidas</option>
                            </select>
                            <button class="btn btn-sm btn-outline-primary" onclick="checkExpiredCards()">
                                <i class="fas fa-sync-alt me-1"></i>Verificar Vencidas
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($cards)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No tienes tarjetas de crédito registradas</h4>
                        <p class="text-muted">Agrega tu primera tarjeta para comenzar a gestionar tus gastos</p>
                        <a href="/controlGastos/public/?route=credit-cards/create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Agregar Primera Tarjeta
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="row">
                        <?php foreach ($cards as $cardData): ?>
                        <?php $card = $cardData['card']; ?>
                        <div class="col-md-6 col-lg-4 mb-4" data-card-status="<?= $card['status'] ?>">
                            <div class="card h-100 shadow-sm <?= $card['status'] !== 'active' ? 'border-warning' : '' ?>">
                                <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                    <div class="d-flex justify-content-between align-items-center text-white">
                                        <div>
                                            <h6 class="mb-0">
                                                <?= htmlspecialchars($card['card_name']) ?>
                                                <?php if ($card['status'] !== 'active'): ?>
                                                <span class="badge bg-warning text-dark ms-1">
                                                    <?php
                                                    $statusLabels = [
                                                        'cancelled' => 'Cancelada',
                                                        'expired' => 'Vencida',
                                                        'blocked' => 'Bloqueada',
                                                        'pending' => 'Pendiente'
                                                    ];
                                                    echo $statusLabels[$card['status']] ?? ucfirst($card['status']);
                                                    ?>
                                                </span>
                                                <?php endif; ?>
                                            </h6>
                                            <small><?= htmlspecialchars($card['bank_name'] ?? 'Banco no especificado') ?></small>
                                        </div>
                                        <div>
                                            <?php if ($card['card_number_last4']): ?>
                                            <small>**** <?= htmlspecialchars($card['card_number_last4']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Información financiera -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">Cupo Consumido:</span>
                                            <strong class="<?= $cardData['current_balance'] > 0 ? 'text-danger' : 'text-success' ?>">
                                                $<?= number_format($cardData['current_balance'], 0) ?>
                                            </strong>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">Cupo Total:</span>
                                            <span>$<?= number_format($card['credit_limit'], 0) ?></span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">Disponible:</span>
                                            <strong class="text-success">$<?= number_format($cardData['available_credit'], 0) ?></strong>
                                        </div>
                                    </div>

                                    <!-- Barra de utilización -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <small class="text-muted">Utilización</small>
                                            <small class="text-muted"><?= number_format($cardData['credit_utilization'], 1) ?>%</small>
                                        </div>
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar <?= $cardData['credit_utilization'] > 80 ? 'bg-danger' : ($cardData['credit_utilization'] > 50 ? 'bg-warning' : 'bg-success') ?>" 
                                                 style="width: <?= min($cardData['credit_utilization'], 100) ?>%"></div>
                                        </div>
                                    </div>

                                    <!-- Fechas importantes -->
                                    <div class="mb-3">
                                        <small class="text-muted d-block">Próximo corte: <?= date('d/m/Y', strtotime($cardData['next_cut_off_date'])) ?></small>
                                        <small class="text-muted d-block">Fecha límite pago: <?= date('d/m/Y', strtotime($cardData['next_payment_date'])) ?></small>
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex gap-2">
                                        <a href="/controlGastos/public/?route=credit-cards/show&id=<?= $card['id'] ?>" class="btn btn-primary btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>Ver
                                        </a>
                                        <a href="/controlGastos/public/?route=credit-cards/edit&id=<?= $card['id'] ?>" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="/controlGastos/public/?route=credit-cards/reports&id=<?= $card['id'] ?>" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-chart-bar"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Actividad Reciente -->
    <?php if (!empty($recent_transactions) || !empty($recent_payments)): ?>
    <div class="row mt-4">
        <!-- Transacciones Recientes -->
        <?php if (!empty($recent_transactions)): ?>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Transacciones Recientes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?= htmlspecialchars($transaction['description']) ?></h6>
                                    <small class="text-muted">
                                        <?= htmlspecialchars($transaction['card_name']) ?> • 
                                        <?= date('d/m/Y', strtotime($transaction['transaction_date'])) ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="text-danger">-$<?= number_format($transaction['amount'], 0) ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Pagos Recientes -->
        <?php if (!empty($recent_payments)): ?>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>Pagos Recientes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($recent_payments, 0, 5) as $payment): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">Pago <?= ucfirst($payment['payment_type']) ?></h6>
                                    <small class="text-muted">
                                        <?= htmlspecialchars($payment['card_name']) ?> • 
                                        <?= date('d/m/Y', strtotime($payment['payment_date'])) ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="text-success">+$<?= number_format($payment['amount'], 0) ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}
.card-filtered {
    display: none !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filtro por estado
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            const selectedStatus = this.value;
            const cards = document.querySelectorAll('[data-card-status]');

            cards.forEach(card => {
                if (selectedStatus === '' || card.dataset.cardStatus === selectedStatus) {
                    card.classList.remove('card-filtered');
                } else {
                    card.classList.add('card-filtered');
                }
            });
        });
    }
});

function checkExpiredCards() {
    if (confirm('¿Deseas verificar y actualizar automáticamente las tarjetas vencidas?')) {
        // Crear formulario para verificar tarjetas vencidas
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/controlGastos/public/?route=credit-cards/check-expired';

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
