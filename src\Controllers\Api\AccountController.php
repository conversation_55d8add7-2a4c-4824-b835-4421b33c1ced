<?php

declare(strict_types=1);

namespace ControlGastos\Controllers\Api;

use ControlGastos\Core\Container;
use ControlGastos\Services\AccountService;
use ControlGastos\Core\Session;

/**
 * Controlador API de cuentas financieras
 * Maneja endpoints REST para cuentas
 */
class AccountController
{
    private Container $container;
    private AccountService $accountService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->accountService = $container->get('accountService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * GET /api/v1/accounts
     * Obtener todas las cuentas del usuario
     */
    public function index(): void
    {
        try {
            $activeOnly = isset($_GET['active_only']) ? 
                filter_var($_GET['active_only'], FILTER_VALIDATE_BOOLEAN) : true;
            
            $type = $_GET['type'] ?? null;
            $currency = $_GET['currency'] ?? null;

            $result = $this->accountService->getUserAccounts($this->userId, $activeOnly);

            if ($result['success']) {
                $accounts = $result['accounts'];

                // Filtrar por tipo si se especifica
                if ($type) {
                    $accounts = array_filter($accounts, fn($account) => $account['type'] === $type);
                }

                // Filtrar por moneda si se especifica
                if ($currency) {
                    $accounts = array_filter($accounts, fn($account) => $account['currency'] === $currency);
                }

                $this->jsonResponse([
                    'success' => true,
                    'data' => array_values($accounts),
                    'count' => count($accounts)
                ]);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ], 500);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/accounts/{id}
     * Obtener cuenta específica
     */
    public function show(int $id): void
    {
        try {
            $result = $this->accountService->getAccount($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['account']
                ]);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ], 404);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * POST /api/v1/accounts
     * Crear nueva cuenta
     */
    public function store(): void
    {
        try {
            $input = $this->getJsonInput();

            $data = [
                'name' => $input['name'] ?? '',
                'type' => $input['type'] ?? '',
                'currency' => $input['currency'] ?? 'COP',
                'description' => $input['description'] ?? '',
                'initial_balance' => $input['initial_balance'] ?? 0
            ];

            $result = $this->accountService->createAccount($data, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['account']
                ], 201);
            } else {
                $statusCode = isset($result['errors']) ? 422 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * PUT /api/v1/accounts/{id}
     * Actualizar cuenta existente
     */
    public function update(int $id): void
    {
        try {
            $input = $this->getJsonInput();

            $data = [
                'name' => $input['name'] ?? '',
                'type' => $input['type'] ?? '',
                'currency' => $input['currency'] ?? '',
                'description' => $input['description'] ?? ''
            ];

            $result = $this->accountService->updateAccount($id, $data, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['account']
                ]);
            } else {
                $statusCode = isset($result['errors']) ? 422 : 
                            ($result['message'] === 'Cuenta no encontrada' ? 404 : 400);
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * DELETE /api/v1/accounts/{id}
     * Eliminar cuenta
     */
    public function delete(int $id): void
    {
        try {
            $result = $this->accountService->deleteAccount($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message']
                ]);
            } else {
                $statusCode = $result['message'] === 'Cuenta no encontrada' ? 404 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/accounts/{id}/balance
     * Obtener balance de cuenta específica
     */
    public function balance(int $id): void
    {
        try {
            $result = $this->accountService->getAccount($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => [
                        'account_id' => $id,
                        'balance' => $result['account']['balance'],
                        'balance_formatted' => $result['account']['balance_formatted'],
                        'currency' => $result['account']['currency']
                    ]
                ]);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ], 404);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * POST /api/v1/accounts/transfer
     * Transferir entre cuentas
     */
    public function transfer(): void
    {
        try {
            $input = $this->getJsonInput();

            $fromAccountId = (int) ($input['from_account_id'] ?? 0);
            $toAccountId = (int) ($input['to_account_id'] ?? 0);
            $amount = (float) ($input['amount'] ?? 0);

            if ($fromAccountId === $toAccountId) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'No puede transferir a la misma cuenta'
                ], 400);
                return;
            }

            $result = $this->accountService->transferBetweenAccounts(
                $fromAccountId, 
                $toAccountId, 
                $amount, 
                $this->userId
            );

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message']
                ]);
            } else {
                $this->jsonResponse($result, 400);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * PATCH /api/v1/accounts/{id}/toggle-status
     * Activar/Desactivar cuenta
     */
    public function toggleStatus(int $id): void
    {
        try {
            $result = $this->accountService->toggleAccountStatus($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['account']
                ]);
            } else {
                $statusCode = $result['message'] === 'Cuenta no encontrada' ? 404 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/accounts/summary
     * Obtener resumen financiero
     */
    public function summary(): void
    {
        try {
            $result = $this->accountService->getFinancialSummary($this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['summary']
                ]);
            } else {
                $this->jsonResponse($result, 500);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/accounts/types
     * Obtener tipos de cuenta disponibles
     */
    public function types(): void
    {
        $this->jsonResponse([
            'success' => true,
            'data' => $this->accountService->getAccountTypes()
        ]);
    }

    /**
     * GET /api/v1/accounts/currencies
     * Obtener monedas disponibles
     */
    public function currencies(): void
    {
        $this->jsonResponse([
            'success' => true,
            'data' => $this->accountService->getCurrencies()
        ]);
    }

    /**
     * Obtener input JSON del request
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('JSON inválido');
        }
        
        return $data ?: [];
    }

    /**
     * Enviar respuesta JSON
     */
    private function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
