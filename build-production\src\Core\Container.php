<?php

declare(strict_types=1);

namespace ControlGastos\Core;

use Exception;

/**
 * Contenedor de dependencias
 * Implementa inyección de dependencias para la aplicación
 */
class Container
{
    private array $bindings = [];
    private array $instances = [];

    /**
     * Registrar un binding en el contenedor
     */
    public function bind(string $abstract, $concrete = null): void
    {
        if ($concrete === null) {
            $concrete = $abstract;
        }

        $this->bindings[$abstract] = $concrete;
    }

    /**
     * Registrar una instancia singleton
     */
    public function singleton(string $abstract, $concrete = null): void
    {
        $this->bind($abstract, $concrete);
    }

    /**
     * Resolver una dependencia del contenedor
     */
    public function get(string $abstract)
    {
        // Si ya existe una instancia, devolverla
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // Si no hay binding, intentar resolver automáticamente
        if (!isset($this->bindings[$abstract])) {
            return $this->resolve($abstract);
        }

        $concrete = $this->bindings[$abstract];

        // Si es un closure, ejecutarlo
        if ($concrete instanceof \Closure) {
            $instance = $concrete($this);
        } else {
            $instance = $this->resolve($concrete);
        }

        // Guardar instancia para singleton
        $this->instances[$abstract] = $instance;

        return $instance;
    }

    /**
     * Resolver una clase automáticamente
     */
    private function resolve(string $class)
    {
        if (!class_exists($class)) {
            throw new Exception("Clase no encontrada: {$class}");
        }

        $reflector = new \ReflectionClass($class);

        if (!$reflector->isInstantiable()) {
            throw new Exception("Clase no instanciable: {$class}");
        }

        $constructor = $reflector->getConstructor();

        if ($constructor === null) {
            return new $class;
        }

        $parameters = $constructor->getParameters();
        $dependencies = $this->resolveDependencies($parameters);

        return $reflector->newInstanceArgs($dependencies);
    }

    /**
     * Resolver dependencias del constructor
     */
    private function resolveDependencies(array $parameters): array
    {
        $dependencies = [];

        foreach ($parameters as $parameter) {
            $type = $parameter->getType();

            if ($type === null) {
                if ($parameter->isDefaultValueAvailable()) {
                    $dependencies[] = $parameter->getDefaultValue();
                } else {
                    throw new Exception("No se puede resolver el parámetro: {$parameter->getName()}");
                }
            } elseif ($type instanceof \ReflectionNamedType) {
                $dependencies[] = $this->get($type->getName());
            } else {
                throw new Exception("Tipo de parámetro no soportado: {$parameter->getName()}");
            }
        }

        return $dependencies;
    }

    /**
     * Verificar si existe un binding
     */
    public function has(string $abstract): bool
    {
        return isset($this->bindings[$abstract]) || isset($this->instances[$abstract]);
    }

    /**
     * Obtener todos los bindings
     */
    public function getBindings(): array
    {
        return $this->bindings;
    }

    /**
     * Limpiar todas las instancias
     */
    public function flush(): void
    {
        $this->instances = [];
    }
}
