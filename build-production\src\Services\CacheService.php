<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de cache
 * Maneja el almacenamiento en cache para optimizar el rendimiento
 */
class CacheService
{
    private Logger $logger;
    private string $cacheDir;
    private int $defaultTtl;
    private array $memoryCache;

    public function __construct(Logger $logger, string $cacheDir = null, int $defaultTtl = 3600)
    {
        $this->logger = $logger;
        $this->cacheDir = $cacheDir ?: __DIR__ . '/../../storage/cache';
        $this->defaultTtl = $defaultTtl;
        $this->memoryCache = [];

        // Crear directorio de cache si no existe
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    /**
     * Obtener valor del cache
     */
    public function get(string $key, $default = null)
    {
        try {
            // Verificar cache en memoria primero
            if (isset($this->memoryCache[$key])) {
                $item = $this->memoryCache[$key];
                if ($item['expires'] > time()) {
                    return $item['data'];
                } else {
                    unset($this->memoryCache[$key]);
                }
            }

            // Verificar cache en disco
            $filePath = $this->getCacheFilePath($key);
            
            if (!file_exists($filePath)) {
                return $default;
            }

            $content = file_get_contents($filePath);
            if ($content === false) {
                return $default;
            }

            $data = unserialize($content);
            
            if (!is_array($data) || !isset($data['expires'], $data['data'])) {
                $this->delete($key);
                return $default;
            }

            // Verificar si ha expirado
            if ($data['expires'] < time()) {
                $this->delete($key);
                return $default;
            }

            // Guardar en cache de memoria para accesos futuros
            $this->memoryCache[$key] = $data;

            return $data['data'];

        } catch (Exception $e) {
            $this->logger->error('Error obteniendo del cache: ' . $e->getMessage(), [
                'key' => $key
            ]);
            return $default;
        }
    }

    /**
     * Guardar valor en cache
     */
    public function set(string $key, $value, int $ttl = null): bool
    {
        try {
            $ttl = $ttl ?? $this->defaultTtl;
            $expires = time() + $ttl;
            
            $data = [
                'data' => $value,
                'expires' => $expires,
                'created' => time()
            ];

            // Guardar en cache de memoria
            $this->memoryCache[$key] = $data;

            // Guardar en cache de disco
            $filePath = $this->getCacheFilePath($key);
            $dir = dirname($filePath);
            
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            $result = file_put_contents($filePath, serialize($data), LOCK_EX);
            
            if ($result === false) {
                throw new Exception('No se pudo escribir el archivo de cache');
            }

            return true;

        } catch (Exception $e) {
            $this->logger->error('Error guardando en cache: ' . $e->getMessage(), [
                'key' => $key
            ]);
            return false;
        }
    }

    /**
     * Verificar si existe una clave en cache
     */
    public function has(string $key): bool
    {
        return $this->get($key, '__CACHE_MISS__') !== '__CACHE_MISS__';
    }

    /**
     * Eliminar valor del cache
     */
    public function delete(string $key): bool
    {
        try {
            // Eliminar de cache de memoria
            unset($this->memoryCache[$key]);

            // Eliminar archivo de cache
            $filePath = $this->getCacheFilePath($key);
            
            if (file_exists($filePath)) {
                return unlink($filePath);
            }

            return true;

        } catch (Exception $e) {
            $this->logger->error('Error eliminando del cache: ' . $e->getMessage(), [
                'key' => $key
            ]);
            return false;
        }
    }

    /**
     * Limpiar todo el cache
     */
    public function clear(): bool
    {
        try {
            // Limpiar cache de memoria
            $this->memoryCache = [];

            // Limpiar archivos de cache
            $this->clearDirectory($this->cacheDir);

            $this->logger->info('Cache limpiado completamente');
            return true;

        } catch (Exception $e) {
            $this->logger->error('Error limpiando cache: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Limpiar cache expirado
     */
    public function clearExpired(): int
    {
        $cleared = 0;

        try {
            // Limpiar cache de memoria expirado
            foreach ($this->memoryCache as $key => $item) {
                if ($item['expires'] < time()) {
                    unset($this->memoryCache[$key]);
                    $cleared++;
                }
            }

            // Limpiar archivos de cache expirados
            $cleared += $this->clearExpiredFiles($this->cacheDir);

            $this->logger->info('Cache expirado limpiado', ['cleared' => $cleared]);
            return $cleared;

        } catch (Exception $e) {
            $this->logger->error('Error limpiando cache expirado: ' . $e->getMessage());
            return $cleared;
        }
    }

    /**
     * Obtener o establecer valor con callback
     */
    public function remember(string $key, callable $callback, int $ttl = null)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }

        $value = $callback();
        $this->set($key, $value, $ttl);
        
        return $value;
    }

    /**
     * Incrementar valor numérico
     */
    public function increment(string $key, int $value = 1): int
    {
        $current = (int) $this->get($key, 0);
        $new = $current + $value;
        $this->set($key, $new);
        
        return $new;
    }

    /**
     * Decrementar valor numérico
     */
    public function decrement(string $key, int $value = 1): int
    {
        $current = (int) $this->get($key, 0);
        $new = max(0, $current - $value);
        $this->set($key, $new);
        
        return $new;
    }

    /**
     * Obtener múltiples valores
     */
    public function getMultiple(array $keys, $default = null): array
    {
        $result = [];
        
        foreach ($keys as $key) {
            $result[$key] = $this->get($key, $default);
        }
        
        return $result;
    }

    /**
     * Establecer múltiples valores
     */
    public function setMultiple(array $values, int $ttl = null): bool
    {
        $success = true;
        
        foreach ($values as $key => $value) {
            if (!$this->set($key, $value, $ttl)) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * Obtener estadísticas del cache
     */
    public function getStats(): array
    {
        try {
            $stats = [
                'memory_cache_size' => count($this->memoryCache),
                'disk_cache_files' => 0,
                'total_size' => 0,
                'expired_files' => 0
            ];

            $this->calculateDiskStats($this->cacheDir, $stats);

            return $stats;

        } catch (Exception $e) {
            $this->logger->error('Error obteniendo estadísticas de cache: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Generar clave de cache para consultas de base de datos
     */
    public function generateQueryKey(string $query, array $params = []): string
    {
        $key = 'query:' . md5($query . serialize($params));
        return $key;
    }

    /**
     * Cache específico para usuarios
     */
    public function getUserCache(int $userId): UserCacheManager
    {
        return new UserCacheManager($this, $userId);
    }

    /**
     * Obtener ruta del archivo de cache
     */
    private function getCacheFilePath(string $key): string
    {
        $hash = md5($key);
        $dir = substr($hash, 0, 2);
        return $this->cacheDir . '/' . $dir . '/' . $hash . '.cache';
    }

    /**
     * Limpiar directorio recursivamente
     */
    private function clearDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                $this->clearDirectory($path);
                rmdir($path);
            } else {
                unlink($path);
            }
        }
    }

    /**
     * Limpiar archivos expirados
     */
    private function clearExpiredFiles(string $dir): int
    {
        $cleared = 0;
        
        if (!is_dir($dir)) {
            return $cleared;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                $cleared += $this->clearExpiredFiles($path);
                
                // Eliminar directorio si está vacío
                if (count(array_diff(scandir($path), ['.', '..'])) === 0) {
                    rmdir($path);
                }
            } elseif (str_ends_with($file, '.cache')) {
                $content = file_get_contents($path);
                
                if ($content !== false) {
                    $data = unserialize($content);
                    
                    if (is_array($data) && isset($data['expires']) && $data['expires'] < time()) {
                        unlink($path);
                        $cleared++;
                    }
                }
            }
        }
        
        return $cleared;
    }

    /**
     * Calcular estadísticas de disco
     */
    private function calculateDiskStats(string $dir, array &$stats): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                $this->calculateDiskStats($path, $stats);
            } elseif (str_ends_with($file, '.cache')) {
                $stats['disk_cache_files']++;
                $stats['total_size'] += filesize($path);
                
                $content = file_get_contents($path);
                if ($content !== false) {
                    $data = unserialize($content);
                    if (is_array($data) && isset($data['expires']) && $data['expires'] < time()) {
                        $stats['expired_files']++;
                    }
                }
            }
        }
    }
}

/**
 * Gestor de cache específico para usuarios
 */
class UserCacheManager
{
    private CacheService $cache;
    private int $userId;
    private string $prefix;

    public function __construct(CacheService $cache, int $userId)
    {
        $this->cache = $cache;
        $this->userId = $userId;
        $this->prefix = "user:{$userId}:";
    }

    public function get(string $key, $default = null)
    {
        return $this->cache->get($this->prefix . $key, $default);
    }

    public function set(string $key, $value, int $ttl = null): bool
    {
        return $this->cache->set($this->prefix . $key, $value, $ttl);
    }

    public function delete(string $key): bool
    {
        return $this->cache->delete($this->prefix . $key);
    }

    public function has(string $key): bool
    {
        return $this->cache->has($this->prefix . $key);
    }

    public function remember(string $key, callable $callback, int $ttl = null)
    {
        return $this->cache->remember($this->prefix . $key, $callback, $ttl);
    }

    public function clearUserCache(): bool
    {
        // Implementación simplificada - en producción se podría optimizar
        // manteniendo un índice de claves por usuario
        return true;
    }

    public function getAccountBalance(int $accountId): ?float
    {
        return $this->get("account_balance:{$accountId}");
    }

    public function setAccountBalance(int $accountId, float $balance, int $ttl = 300): bool
    {
        return $this->set("account_balance:{$accountId}", $balance, $ttl);
    }

    public function getTransactionStats(string $period): ?array
    {
        return $this->get("transaction_stats:{$period}");
    }

    public function setTransactionStats(string $period, array $stats, int $ttl = 1800): bool
    {
        return $this->set("transaction_stats:{$period}", $stats, $ttl);
    }

    public function getCategoryStats(): ?array
    {
        return $this->get("category_stats");
    }

    public function setCategoryStats(array $stats, int $ttl = 3600): bool
    {
        return $this->set("category_stats", $stats, $ttl);
    }
}
