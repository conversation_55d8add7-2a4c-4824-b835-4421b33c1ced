<?php

declare(strict_types=1);

namespace ControlGastos\Core;

use PDO;
use PDOException;
use Exception;

/**
 * Clase para manejo de base de datos
 * Implementa patrón Singleton y manejo de conexiones
 */
class Database
{
    private static ?Database $instance = null;
    private ?PDO $connection = null;
    private array $config;
    private array $queryLog = [];

    public function __construct(array $config)
    {
        $this->config = $config['database'] ?? [];
    }

    /**
     * Obtener instancia singleton
     */
    public static function getInstance(array $config): Database
    {
        if (self::$instance === null) {
            self::$instance = new self($config);
        }
        return self::$instance;
    }

    /**
     * Obtener conexión PDO
     */
    public function getConnection(): PDO
    {
        if ($this->connection === null) {
            $this->connect();
        }
        return $this->connection;
    }

    /**
     * Establecer conexión a la base de datos
     */
    private function connect(): void
    {
        try {
            $connectionName = $this->config['default'] ?? 'mysql';
            $connectionConfig = $this->config['connections'][$connectionName] ?? [];

            if (empty($connectionConfig)) {
                throw new Exception("Configuración de base de datos no encontrada para: {$connectionName}");
            }

            $dsn = $this->buildDsn($connectionConfig);
            $username = $connectionConfig['username'] ?? '';
            $password = $connectionConfig['password'] ?? '';
            $options = $connectionConfig['options'] ?? [];

            // Opciones por defecto
            $defaultOptions = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $options = array_merge($defaultOptions, $options);

            $this->connection = new PDO($dsn, $username, $password, $options);

        } catch (PDOException $e) {
            throw new Exception("Error de conexión a la base de datos: " . $e->getMessage());
        }
    }

    /**
     * Construir DSN para la conexión
     */
    private function buildDsn(array $config): string
    {
        $driver = $config['driver'] ?? 'mysql';
        $host = $config['host'] ?? 'localhost';
        $port = $config['port'] ?? '3306';
        $database = $config['database'] ?? '';
        $charset = $config['charset'] ?? 'utf8mb4';

        return "{$driver}:host={$host};port={$port};dbname={$database};charset={$charset}";
    }

    /**
     * Ejecutar consulta SELECT
     */
    public function select(string $query, array $params = []): array
    {
        $stmt = $this->execute($query, $params);
        return $stmt->fetchAll();
    }

    /**
     * Ejecutar consulta SELECT y obtener un solo registro
     */
    public function selectOne(string $query, array $params = []): ?array
    {
        $stmt = $this->execute($query, $params);
        $result = $stmt->fetch();
        return $result ?: null;
    }

    /**
     * Ejecutar consulta INSERT
     */
    public function insert(string $query, array $params = []): int
    {
        $this->execute($query, $params);
        return (int) $this->connection->lastInsertId();
    }

    /**
     * Ejecutar consulta UPDATE
     */
    public function update(string $query, array $params = []): int
    {
        $stmt = $this->execute($query, $params);
        return $stmt->rowCount();
    }

    /**
     * Ejecutar consulta DELETE
     */
    public function delete(string $query, array $params = []): int
    {
        $stmt = $this->execute($query, $params);
        return $stmt->rowCount();
    }

    /**
     * Ejecutar consulta preparada
     */
    public function execute(string $query, array $params = []): \PDOStatement
    {
        try {
            $startTime = microtime(true);
            
            $stmt = $this->getConnection()->prepare($query);
            $stmt->execute($params);
            
            $executionTime = microtime(true) - $startTime;
            
            // Log de consulta si está habilitado
            if ($this->shouldLogQuery()) {
                $this->logQuery($query, $params, $executionTime);
            }
            
            return $stmt;
            
        } catch (PDOException $e) {
            throw new Exception("Error en consulta SQL: " . $e->getMessage() . " | Query: " . $query);
        }
    }

    /**
     * Iniciar transacción
     */
    public function beginTransaction(): bool
    {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * Confirmar transacción
     */
    public function commit(): bool
    {
        return $this->getConnection()->commit();
    }

    /**
     * Revertir transacción
     */
    public function rollback(): bool
    {
        return $this->getConnection()->rollBack();
    }

    /**
     * Ejecutar múltiples consultas en transacción
     */
    public function transaction(callable $callback): mixed
    {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Verificar si se debe loggear la consulta
     */
    private function shouldLogQuery(): bool
    {
        return isset($this->config['logging']['log_queries']) && 
               $this->config['logging']['log_queries'] === true;
    }

    /**
     * Loggear consulta
     */
    private function logQuery(string $query, array $params, float $executionTime): void
    {
        $this->queryLog[] = [
            'query' => $query,
            'params' => $params,
            'execution_time' => $executionTime,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Obtener log de consultas
     */
    public function getQueryLog(): array
    {
        return $this->queryLog;
    }

    /**
     * Limpiar log de consultas
     */
    public function clearQueryLog(): void
    {
        $this->queryLog = [];
    }

    /**
     * Obtener información de la base de datos
     */
    public function getDatabaseInfo(): array
    {
        $connection = $this->getConnection();
        
        return [
            'driver' => $connection->getAttribute(PDO::ATTR_DRIVER_NAME),
            'version' => $connection->getAttribute(PDO::ATTR_SERVER_VERSION),
            'connection_status' => $connection->getAttribute(PDO::ATTR_CONNECTION_STATUS)
        ];
    }

    /**
     * Verificar si la tabla existe
     */
    public function tableExists(string $tableName): bool
    {
        try {
            $query = "SELECT 1 FROM {$tableName} LIMIT 1";
            $this->execute($query);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Obtener lista de tablas
     */
    public function getTables(): array
    {
        $query = "SHOW TABLES";
        $result = $this->select($query);
        
        return array_map(function($row) {
            return array_values($row)[0];
        }, $result);
    }

    /**
     * Cerrar conexión
     */
    public function close(): void
    {
        $this->connection = null;
    }

    /**
     * Destructor
     */
    public function __destruct()
    {
        $this->close();
    }
}
