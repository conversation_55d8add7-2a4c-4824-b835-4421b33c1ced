-- Migración 005: Agregar estados a las tarjetas de crédito
-- Fecha: 2024-12-30
-- Descripción: Agregar columna de estado para gestión del ciclo de vida de tarjetas

-- Agregar columna de estado a la tabla credit_cards
ALTER TABLE credit_cards 
ADD COLUMN status ENUM('active', 'cancelled', 'expired', 'blocked', 'pending') NOT NULL DEFAULT 'active' 
COMMENT 'Estado de la tarjeta: active, cancelled, expired, blocked, pending'
AFTER is_active;

-- Agregar índice para consultas por estado
ALTER TABLE credit_cards 
ADD INDEX idx_status (status);

-- Agregar columna de fecha de cancelación/bloqueo
ALTER TABLE credit_cards 
ADD COLUMN status_changed_at TIMESTAMP NULL DEFAULT NULL 
COMMENT 'Fecha del último cambio de estado'
AFTER status;

-- Agregar columna de motivo del cambio de estado
ALTER TABLE credit_cards 
ADD COLUMN status_reason VARCHAR(255) NULL DEFAULT NULL 
COMMENT 'Motivo del cambio de estado'
AFTER status_changed_at;

-- Actualizar tarjetas existentes
UPDATE credit_cards 
SET status = CASE 
    WHEN is_active = 1 AND expiry_date > CURDATE() THEN 'active'
    WHEN is_active = 1 AND expiry_date <= CURDATE() THEN 'expired'
    WHEN is_active = 0 THEN 'cancelled'
    ELSE 'active'
END,
status_changed_at = updated_at
WHERE status_changed_at IS NULL;

-- Comentarios de documentación
ALTER TABLE credit_cards COMMENT = 'Tabla principal de tarjetas de crédito con gestión de estados';
