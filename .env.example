# =====================================================
# CONFIGURACIÓN DE ENTORNO - CONTROL DE GASTOS
# Copiar este archivo como .env y configurar valores
# =====================================================

# Configuración de la aplicación
APP_NAME="Control de Gastos"
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost
APP_TIMEZONE=America/Bogota
APP_LOCALE=es

# Configuración de base de datos
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=control_gastos
DB_USERNAME=root
DB_PASSWORD=

# Base de datos de testing
DB_TEST_HOST=localhost
DB_TEST_PORT=3306
DB_TEST_DATABASE=control_gastos_test
DB_TEST_USERNAME=root
DB_TEST_PASSWORD=

# Configuración de correo electrónico
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Control de Gastos"

# Para desarrollo/testing
MAIL_FAKE_SEND=false
MAIL_LOG_ONLY=false
MAIL_TEST_RECIPIENT=

# Configuración de logs
LOG_LEVEL=info
LOG_QUERIES=false

# Configuración de rutas de herramientas (opcional)
MYSQLDUMP_PATH=mysqldump
MYSQL_PATH=mysql

# Configuración de seguridad
SESSION_SECURE=false
SESSION_HTTPONLY=true
SESSION_SAMESITE=Lax

# Configuración de archivos
MAX_UPLOAD_SIZE=5242880
UPLOAD_PATH=storage/uploads

# Configuración de backup
BACKUP_ENABLED=true
BACKUP_AUTO=false
BACKUP_MAX_FILES=10

# Configuración de caché
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=3600

# Configuración de API
API_RATE_LIMIT=100
API_RATE_LIMIT_WINDOW=60
