<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Core\Logger;
use ControlGastos\Core\Database;
use PHP<PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

/**
 * Servicio de Email con SMTP
 * Maneja el envío de correos electrónicos con plantillas y cola de envío
 */
class EmailService
{
    private Logger $logger;
    private Database $database;
    private array $config;
    private PHPMailer $mailer;
    private string $templatesPath;

    public function __construct(Logger $logger, Database $database, array $config = [])
    {
        $this->logger = $logger;
        $this->database = $database;
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->templatesPath = __DIR__ . '/../../templates/emails/';
        
        $this->initializeMailer();
    }

    /**
     * Configuración por defecto
     */
    private function getDefaultConfig(): array
    {
        return [
            'smtp_host' => $_ENV['SMTP_HOST'] ?? 'localhost',
            'smtp_port' => (int) ($_ENV['SMTP_PORT'] ?? 587),
            'smtp_username' => $_ENV['SMTP_USERNAME'] ?? '',
            'smtp_password' => $_ENV['SMTP_PASSWORD'] ?? '',
            'smtp_encryption' => $_ENV['SMTP_ENCRYPTION'] ?? 'tls',
            'from_email' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
            'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'Control de Gastos',
            'reply_to' => $_ENV['MAIL_REPLY_TO'] ?? '',
            'max_retries' => 3,
            'retry_delay' => 300, // 5 minutos
            'queue_enabled' => true,
            'debug_mode' => $_ENV['MAIL_DEBUG'] ?? false
        ];
    }

    /**
     * Inicializar PHPMailer
     */
    private function initializeMailer(): void
    {
        $this->mailer = new PHPMailer(true);

        try {
            // Configuración del servidor SMTP
            $this->mailer->isSMTP();
            $this->mailer->Host = $this->config['smtp_host'];
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $this->config['smtp_username'];
            $this->mailer->Password = $this->config['smtp_password'];
            $this->mailer->SMTPSecure = $this->config['smtp_encryption'];
            $this->mailer->Port = $this->config['smtp_port'];
            $this->mailer->CharSet = 'UTF-8';

            // Configuración de debug
            if ($this->config['debug_mode']) {
                $this->mailer->SMTPDebug = SMTP::DEBUG_SERVER;
            }

            // Configuración del remitente
            $this->mailer->setFrom(
                $this->config['from_email'],
                $this->config['from_name']
            );

            if (!empty($this->config['reply_to'])) {
                $this->mailer->addReplyTo($this->config['reply_to']);
            }

        } catch (Exception $e) {
            $this->logger->error('Error inicializando PHPMailer: ' . $e->getMessage());
            throw new \Exception('Error de configuración de email: ' . $e->getMessage());
        }
    }

    /**
     * Enviar email de verificación de cuenta
     */
    public function sendVerificationEmail(array $user, string $token): bool
    {
        $verificationUrl = $this->generateVerificationUrl($token);
        
        $data = [
            'user_name' => $user['name'] ?? $user['first_name'] . ' ' . $user['last_name'],
            'verification_url' => $verificationUrl,
            'app_name' => $this->config['from_name'],
            'support_email' => $this->config['reply_to'] ?: $this->config['from_email']
        ];

        return $this->sendTemplatedEmail(
            $user['email'],
            'Verificación de Cuenta - ' . $this->config['from_name'],
            'verification',
            $data,
            'high'
        );
    }

    /**
     * Enviar email de reset de contraseña
     */
    public function sendPasswordResetEmail(string $email, string $token, array $user = []): bool
    {
        $resetUrl = $this->generatePasswordResetUrl($token);
        
        $data = [
            'user_name' => $user['name'] ?? 'Usuario',
            'reset_url' => $resetUrl,
            'app_name' => $this->config['from_name'],
            'support_email' => $this->config['reply_to'] ?: $this->config['from_email'],
            'expires_in' => '24 horas'
        ];

        return $this->sendTemplatedEmail(
            $email,
            'Restablecer Contraseña - ' . $this->config['from_name'],
            'password-reset',
            $data,
            'high'
        );
    }

    /**
     * Enviar recordatorio de pago
     */
    public function sendPaymentReminder(array $user, array $reminder): bool
    {
        $data = [
            'user_name' => $user['name'],
            'reminder_title' => $reminder['title'],
            'reminder_description' => $reminder['description'],
            'due_date' => date('d/m/Y', strtotime($reminder['due_date'])),
            'amount' => $reminder['amount'] ? '$' . number_format($reminder['amount'], 2) : null,
            'days_until_due' => $this->calculateDaysUntilDue($reminder['due_date']),
            'app_name' => $this->config['from_name'],
            'dashboard_url' => $this->generateDashboardUrl()
        ];

        $priority = $data['days_until_due'] <= 1 ? 'high' : 'medium';

        return $this->sendTemplatedEmail(
            $user['email'],
            'Recordatorio de Pago: ' . $reminder['title'],
            'payment-reminder',
            $data,
            $priority
        );
    }

    /**
     * Enviar notificación de transacción
     */
    public function sendTransactionNotification(array $user, array $transaction): bool
    {
        $data = [
            'user_name' => $user['name'],
            'transaction_type' => $transaction['type'] === 'income' ? 'Ingreso' : 'Gasto',
            'transaction_amount' => '$' . number_format($transaction['amount'], 2),
            'transaction_description' => $transaction['description'],
            'transaction_date' => date('d/m/Y H:i', strtotime($transaction['created_at'])),
            'account_name' => $transaction['account_name'],
            'category_name' => $transaction['category_name'],
            'app_name' => $this->config['from_name'],
            'dashboard_url' => $this->generateDashboardUrl()
        ];

        return $this->sendTemplatedEmail(
            $user['email'],
            'Nueva Transacción Registrada',
            'transaction-notification',
            $data,
            'low'
        );
    }

    /**
     * Enviar reporte mensual
     */
    public function sendMonthlyReport(array $user, array $reportData): bool
    {
        $data = array_merge($reportData, [
            'user_name' => $user['name'],
            'month_year' => date('F Y', strtotime($reportData['period_start'])),
            'app_name' => $this->config['from_name'],
            'dashboard_url' => $this->generateDashboardUrl(),
            'report_url' => $this->generateReportUrl($reportData['period_start'])
        ]);

        return $this->sendTemplatedEmail(
            $user['email'],
            'Reporte Mensual - ' . $data['month_year'],
            'monthly-report',
            $data,
            'medium'
        );
    }

    /**
     * Enviar alerta de seguridad
     */
    public function sendSecurityAlert(array $user, array $alertData): bool
    {
        $data = [
            'user_name' => $user['name'],
            'alert_type' => $alertData['type'],
            'alert_message' => $alertData['message'],
            'ip_address' => $alertData['ip_address'] ?? 'Desconocida',
            'user_agent' => $alertData['user_agent'] ?? 'Desconocido',
            'timestamp' => date('d/m/Y H:i:s', strtotime($alertData['created_at'])),
            'app_name' => $this->config['from_name'],
            'security_url' => $this->generateSecurityUrl()
        ];

        return $this->sendTemplatedEmail(
            $user['email'],
            'Alerta de Seguridad - ' . $this->config['from_name'],
            'security-alert',
            $data,
            'high'
        );
    }

    /**
     * Enviar email con plantilla
     */
    public function sendTemplatedEmail(
        string $to,
        string $subject,
        string $template,
        array $data = [],
        string $priority = 'medium'
    ): bool {
        try {
            // Si la cola está habilitada, agregar a la cola
            if ($this->config['queue_enabled']) {
                return $this->queueEmail($to, $subject, $template, $data, $priority);
            }

            // Enviar inmediatamente
            return $this->sendEmailNow($to, $subject, $template, $data);

        } catch (Exception $e) {
            $this->logger->error('Error enviando email: ' . $e->getMessage(), [
                'to' => $to,
                'subject' => $subject,
                'template' => $template
            ]);
            return false;
        }
    }

    /**
     * Enviar email inmediatamente
     */
    private function sendEmailNow(string $to, string $subject, string $template, array $data): bool
    {
        try {
            // Limpiar destinatarios previos
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();

            // Configurar destinatario
            $this->mailer->addAddress($to);

            // Configurar asunto
            $this->mailer->Subject = $subject;

            // Generar contenido HTML y texto
            $htmlContent = $this->renderTemplate($template, $data, 'html');
            $textContent = $this->renderTemplate($template, $data, 'text');

            $this->mailer->isHTML(true);
            $this->mailer->Body = $htmlContent;
            $this->mailer->AltBody = $textContent;

            // Enviar email
            $result = $this->mailer->send();

            if ($result) {
                $this->logger->info('Email enviado exitosamente', [
                    'to' => $to,
                    'subject' => $subject,
                    'template' => $template
                ]);

                // Registrar en base de datos
                $this->logEmailSent($to, $subject, $template, 'sent');
            }

            return $result;

        } catch (Exception $e) {
            $this->logger->error('Error en envío de email: ' . $e->getMessage(), [
                'to' => $to,
                'subject' => $subject,
                'template' => $template
            ]);

            // Registrar fallo en base de datos
            $this->logEmailSent($to, $subject, $template, 'failed', $e->getMessage());

            return false;
        }
    }

    /**
     * Agregar email a la cola
     */
    private function queueEmail(
        string $to,
        string $subject,
        string $template,
        array $data,
        string $priority
    ): bool {
        try {
            $priorityOrder = [
                'low' => 3,
                'medium' => 2,
                'high' => 1
            ];

            $query = "
                INSERT INTO email_queue (
                    to_email, subject, template, data, priority, 
                    status, created_at, scheduled_at
                ) VALUES (?, ?, ?, ?, ?, 'pending', NOW(), NOW())
            ";

            $this->database->execute($query, [
                $to,
                $subject,
                $template,
                json_encode($data),
                $priorityOrder[$priority] ?? 2
            ]);

            $this->logger->info('Email agregado a la cola', [
                'to' => $to,
                'subject' => $subject,
                'template' => $template,
                'priority' => $priority
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Error agregando email a la cola: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Procesar cola de emails
     */
    public function processEmailQueue(int $batchSize = 10): array
    {
        $processed = 0;
        $failed = 0;

        try {
            // Obtener emails pendientes ordenados por prioridad
            $query = "
                SELECT * FROM email_queue 
                WHERE status = 'pending' 
                AND scheduled_at <= NOW()
                ORDER BY priority ASC, created_at ASC 
                LIMIT ?
            ";

            $emails = $this->database->select($query, [$batchSize]);

            foreach ($emails as $email) {
                $success = $this->processQueuedEmail($email);
                
                if ($success) {
                    $processed++;
                } else {
                    $failed++;
                }
            }

            $this->logger->info('Cola de emails procesada', [
                'processed' => $processed,
                'failed' => $failed,
                'batch_size' => $batchSize
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Error procesando cola de emails: ' . $e->getMessage());
        }

        return [
            'processed' => $processed,
            'failed' => $failed
        ];
    }

    /**
     * Procesar email individual de la cola
     */
    private function processQueuedEmail(array $email): bool
    {
        try {
            $data = json_decode($email['data'], true) ?? [];
            
            $success = $this->sendEmailNow(
                $email['to_email'],
                $email['subject'],
                $email['template'],
                $data
            );

            if ($success) {
                // Marcar como enviado
                $this->updateQueueStatus($email['id'], 'sent');
                return true;
            } else {
                // Incrementar intentos y programar reintento
                $this->handleFailedEmail($email);
                return false;
            }

        } catch (\Exception $e) {
            $this->logger->error('Error procesando email de la cola: ' . $e->getMessage(), [
                'email_id' => $email['id']
            ]);
            
            $this->handleFailedEmail($email, $e->getMessage());
            return false;
        }
    }

    /**
     * Manejar email fallido
     */
    private function handleFailedEmail(array $email, string $error = ''): void
    {
        $attempts = $email['attempts'] + 1;
        
        if ($attempts >= $this->config['max_retries']) {
            // Marcar como fallido permanentemente
            $this->updateQueueStatus($email['id'], 'failed', $error);
        } else {
            // Programar reintento
            $nextAttempt = date('Y-m-d H:i:s', time() + ($this->config['retry_delay'] * $attempts));
            
            $query = "
                UPDATE email_queue 
                SET attempts = ?, scheduled_at = ?, last_error = ?
                WHERE id = ?
            ";
            
            $this->database->execute($query, [
                $attempts,
                $nextAttempt,
                $error,
                $email['id']
            ]);
        }
    }

    /**
     * Actualizar estado de email en cola
     */
    private function updateQueueStatus(int $emailId, string $status, string $error = ''): void
    {
        $query = "
            UPDATE email_queue 
            SET status = ?, processed_at = NOW(), last_error = ?
            WHERE id = ?
        ";
        
        $this->database->execute($query, [$status, $error, $emailId]);
    }

    /**
     * Renderizar plantilla de email
     */
    private function renderTemplate(string $template, array $data, string $format = 'html'): string
    {
        $templateFile = $this->templatesPath . $template . '.' . $format . '.php';
        
        if (!file_exists($templateFile)) {
            throw new \Exception("Plantilla de email no encontrada: {$templateFile}");
        }

        // Extraer variables para la plantilla
        extract($data);
        
        ob_start();
        include $templateFile;
        return ob_get_clean();
    }

    /**
     * Registrar email enviado
     */
    private function logEmailSent(
        string $to,
        string $subject,
        string $template,
        string $status,
        string $error = ''
    ): void {
        try {
            $query = "
                INSERT INTO email_log (
                    to_email, subject, template, status, error_message, sent_at
                ) VALUES (?, ?, ?, ?, ?, NOW())
            ";

            $this->database->execute($query, [
                $to,
                $subject,
                $template,
                $status,
                $error
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Error registrando email en log: ' . $e->getMessage());
        }
    }

    /**
     * Generar URLs para emails
     */
    private function generateVerificationUrl(string $token): string
    {
        $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost';
        return $baseUrl . '/auth/verify-email?token=' . urlencode($token);
    }

    private function generatePasswordResetUrl(string $token): string
    {
        $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost';
        return $baseUrl . '/auth/reset-password?token=' . urlencode($token);
    }

    private function generateDashboardUrl(): string
    {
        $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost';
        return $baseUrl . '/dashboard';
    }

    private function generateReportUrl(string $period): string
    {
        $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost';
        return $baseUrl . '/reports?period=' . urlencode($period);
    }

    private function generateSecurityUrl(): string
    {
        $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost';
        return $baseUrl . '/security';
    }

    /**
     * Calcular días hasta vencimiento
     */
    private function calculateDaysUntilDue(string $dueDate): int
    {
        $due = strtotime($dueDate);
        $now = time();
        return (int) ceil(($due - $now) / 86400);
    }

    /**
     * Obtener estadísticas de emails
     */
    public function getEmailStats(int $days = 30): array
    {
        try {
            $query = "
                SELECT 
                    status,
                    COUNT(*) as count,
                    DATE(sent_at) as date
                FROM email_log 
                WHERE sent_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY status, DATE(sent_at)
                ORDER BY date DESC
            ";

            $stats = $this->database->select($query, [$days]);

            return [
                'daily_stats' => $stats,
                'total_sent' => array_sum(array_column(
                    array_filter($stats, fn($s) => $s['status'] === 'sent'), 
                    'count'
                )),
                'total_failed' => array_sum(array_column(
                    array_filter($stats, fn($s) => $s['status'] === 'failed'), 
                    'count'
                ))
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo estadísticas de email: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Limpiar logs antiguos
     */
    public function cleanupOldLogs(int $days = 90): int
    {
        try {
            $query = "DELETE FROM email_log WHERE sent_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $result = $this->database->execute($query, [$days]);
            
            $this->logger->info("Logs de email limpiados: {$result} registros eliminados");
            return $result;

        } catch (\Exception $e) {
            $this->logger->error('Error limpiando logs de email: ' . $e->getMessage());
            return 0;
        }
    }
}
