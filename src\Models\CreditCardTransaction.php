<?php

declare(strict_types=1);

namespace ControlGastos\Models;

use PDO;
use DateTime;

/**
 * Modelo para gestión de transacciones de tarjetas de crédito
 */
class CreditCardTransaction
{
    private PDO $db;
    
    public function __construct(PDO $db)
    {
        $this->db = $db;
    }
    
    /**
     * Crear una nueva transacción
     */
    public function create(array $data): int
    {
        $sql = "INSERT INTO credit_card_transactions (
            credit_card_id, user_id, transaction_type, amount, description,
            merchant, category_id, transaction_date, posting_date, reference_number,
            installments, installment_number, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['credit_card_id'],
            $data['user_id'],
            $data['transaction_type'] ?? 'purchase',
            $data['amount'],
            $data['description'],
            $data['merchant'] ?? null,
            $data['category_id'] ?? null,
            $data['transaction_date'],
            $data['posting_date'] ?? $data['transaction_date'],
            $data['reference_number'] ?? null,
            $data['installments'] ?? 1,
            $data['installment_number'] ?? 1,
            $data['notes'] ?? null
        ]);
        
        return (int) $this->db->lastInsertId();
    }
    
    /**
     * Obtener transacción por ID
     */
    public function findById(int $id): ?array
    {
        $sql = "SELECT cct.*, c.name as category_name, cc.card_name
                FROM credit_card_transactions cct
                LEFT JOIN categories c ON cct.category_id = c.id
                LEFT JOIN credit_cards cc ON cct.credit_card_id = cc.id
                WHERE cct.id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        
        $transaction = $stmt->fetch();
        return $transaction ?: null;
    }
    
    /**
     * Obtener transacciones por tarjeta de crédito
     */
    public function findByCardId(int $cardId, int $limit = 50, int $offset = 0): array
    {
        $sql = "SELECT cct.*, c.name as category_name
                FROM credit_card_transactions cct
                LEFT JOIN categories c ON cct.category_id = c.id
                WHERE cct.credit_card_id = ?
                ORDER BY cct.transaction_date DESC, cct.created_at DESC
                LIMIT ? OFFSET ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $limit, $offset]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Obtener transacciones por usuario
     */
    public function findByUserId(int $userId, int $limit = 50, int $offset = 0): array
    {
        $sql = "SELECT cct.*, c.name as category_name, cc.card_name
                FROM credit_card_transactions cct
                LEFT JOIN categories c ON cct.category_id = c.id
                LEFT JOIN credit_cards cc ON cct.credit_card_id = cc.id
                WHERE cct.user_id = ?
                ORDER BY cct.transaction_date DESC, cct.created_at DESC
                LIMIT ? OFFSET ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $limit, $offset]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Obtener transacciones por rango de fechas
     */
    public function findByDateRange(int $cardId, string $startDate, string $endDate): array
    {
        $sql = "SELECT cct.*, c.name as category_name
                FROM credit_card_transactions cct
                LEFT JOIN categories c ON cct.category_id = c.id
                WHERE cct.credit_card_id = ?
                AND cct.transaction_date BETWEEN ? AND ?
                ORDER BY cct.transaction_date DESC, cct.created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $startDate, $endDate]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Actualizar transacción
     */
    public function update(int $id, array $data): bool
    {
        $sql = "UPDATE credit_card_transactions SET 
            transaction_type = ?, amount = ?, description = ?, merchant = ?,
            category_id = ?, transaction_date = ?, posting_date = ?,
            reference_number = ?, installments = ?, installment_number = ?,
            notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['transaction_type'] ?? 'purchase',
            $data['amount'],
            $data['description'],
            $data['merchant'] ?? null,
            $data['category_id'] ?? null,
            $data['transaction_date'],
            $data['posting_date'] ?? $data['transaction_date'],
            $data['reference_number'] ?? null,
            $data['installments'] ?? 1,
            $data['installment_number'] ?? 1,
            $data['notes'] ?? null,
            $id
        ]);
    }
    
    /**
     * Eliminar transacción
     */
    public function delete(int $id): bool
    {
        $sql = "DELETE FROM credit_card_transactions WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    /**
     * Obtener total de transacciones por tipo
     */
    public function getTotalByType(int $cardId, string $type, string $startDate = null, string $endDate = null): float
    {
        $whereClause = "WHERE credit_card_id = ? AND transaction_type = ?";
        $params = [$cardId, $type];
        
        if ($startDate && $endDate) {
            $whereClause .= " AND transaction_date BETWEEN ? AND ?";
            $params[] = $startDate;
            $params[] = $endDate;
        }
        
        $sql = "SELECT COALESCE(SUM(amount), 0) as total 
                FROM credit_card_transactions 
                {$whereClause}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return (float) $stmt->fetch()['total'];
    }
    
    /**
     * Obtener transacciones pendientes en cuotas
     */
    public function getPendingInstallments(int $cardId): array
    {
        $sql = "SELECT *, 
                (installments - installment_number) as remaining_installments,
                (amount * (installments - installment_number)) as remaining_amount
                FROM credit_card_transactions 
                WHERE credit_card_id = ? 
                AND installments > 1 
                AND installment_number < installments
                ORDER BY transaction_date DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Crear transacciones automáticas para cuotas
     */
    public function createInstallmentTransactions(array $baseTransaction): array
    {
        $createdIds = [];
        $installments = (int) $baseTransaction['installments'];
        
        if ($installments <= 1) {
            // Si no hay cuotas, crear solo una transacción
            $createdIds[] = $this->create($baseTransaction);
            return $createdIds;
        }
        
        // Calcular monto por cuota
        $installmentAmount = $baseTransaction['amount'] / $installments;
        $transactionDate = new DateTime($baseTransaction['transaction_date']);
        
        for ($i = 1; $i <= $installments; $i++) {
            $installmentData = $baseTransaction;
            $installmentData['amount'] = $installmentAmount;
            $installmentData['installment_number'] = $i;
            $installmentData['transaction_date'] = $transactionDate->format('Y-m-d');
            $installmentData['description'] = $baseTransaction['description'] . " (Cuota {$i}/{$installments})";
            
            $createdIds[] = $this->create($installmentData);
            
            // Avanzar al siguiente mes para la próxima cuota
            if ($i < $installments) {
                $transactionDate->modify('+1 month');
            }
        }
        
        return $createdIds;
    }
    
    /**
     * Obtener estadísticas mensuales
     */
    public function getMonthlyStats(int $cardId, int $year, int $month): array
    {
        $sql = "SELECT 
            transaction_type,
            COUNT(*) as count,
            SUM(amount) as total,
            AVG(amount) as average
        FROM credit_card_transactions 
        WHERE credit_card_id = ? 
        AND YEAR(transaction_date) = ? 
        AND MONTH(transaction_date) = ?
        GROUP BY transaction_type";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $year, $month]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Buscar transacciones por texto
     */
    public function search(int $userId, string $searchTerm, int $limit = 50): array
    {
        $searchTerm = "%{$searchTerm}%";
        
        $sql = "SELECT cct.*, c.name as category_name, cc.card_name
                FROM credit_card_transactions cct
                LEFT JOIN categories c ON cct.category_id = c.id
                LEFT JOIN credit_cards cc ON cct.credit_card_id = cc.id
                WHERE cct.user_id = ?
                AND (cct.description LIKE ? 
                     OR cct.merchant LIKE ? 
                     OR cct.reference_number LIKE ?
                     OR cc.card_name LIKE ?)
                ORDER BY cct.transaction_date DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $limit]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Obtener transacciones por categoría
     */
    public function findByCategory(int $cardId, int $categoryId, string $startDate = null, string $endDate = null): array
    {
        $whereClause = "WHERE cct.credit_card_id = ? AND cct.category_id = ?";
        $params = [$cardId, $categoryId];
        
        if ($startDate && $endDate) {
            $whereClause .= " AND cct.transaction_date BETWEEN ? AND ?";
            $params[] = $startDate;
            $params[] = $endDate;
        }
        
        $sql = "SELECT cct.*, c.name as category_name
                FROM credit_card_transactions cct
                LEFT JOIN categories c ON cct.category_id = c.id
                {$whereClause}
                ORDER BY cct.transaction_date DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
}
