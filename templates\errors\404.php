<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Página no encontrada - Control de Gastos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .error-icon {
            font-size: 5rem;
            color: #ffc107;
            margin-bottom: 1rem;
        }
        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: transform 0.3s ease;
            margin-right: 1rem;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            color: white;
        }
        .btn-back {
            background: transparent;
            border: 2px solid #667eea;
            padding: 10px 28px;
            border-radius: 50px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-back:hover {
            background: #667eea;
            color: white;
        }
        .error-code {
            font-size: 0.9rem;
            color: #999;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <h1 class="error-title">Página no encontrada</h1>
        
        <p class="error-message">
            La página que buscas no existe o ha sido movida. 
            Verifica la URL o regresa al inicio para continuar navegando.
        </p>
        
        <div class="mb-3">
            <a href="/" class="btn btn-home">
                <i class="fas fa-home me-2"></i>
                Ir al Inicio
            </a>
            <a href="javascript:history.back()" class="btn btn-back">
                <i class="fas fa-arrow-left me-2"></i>
                Regresar
            </a>
        </div>
        
        <div class="error-code">
            Error 404 - Page Not Found
        </div>
    </div>
</body>
</html>
