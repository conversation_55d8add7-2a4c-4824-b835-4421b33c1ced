<?php
/**
 * Punto de entrada principal de la aplicación
 * Control de Gastos Personales
 * 
 * <AUTHOR> Gastos Team
 * @version 1.0.0
 */

declare(strict_types=1);

// Configuración de errores para desarrollo
if (file_exists(__DIR__ . '/../.env')) {
    $env = parse_ini_file(__DIR__ . '/../.env');
    if (isset($env['APP_DEBUG']) && $env['APP_DEBUG'] === 'true') {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
    }
}

// Configuración de zona horaria
date_default_timezone_set('America/Bogota');

// Configuración de sesión segura
ini_set('session.cookie_httponly', '1');
ini_set('session.use_only_cookies', '1');
ini_set('session.cookie_secure', '0'); // Cambiar a '1' en producción con HTTPS
ini_set('session.cookie_samesite', 'Lax');

// Autoloader de Composer
require_once __DIR__ . '/../vendor/autoload.php';

// Cargar configuración
$config = require_once __DIR__ . '/../config/app.php';

// Configurar zona horaria desde configuración
if (isset($config['app']['timezone'])) {
    date_default_timezone_set($config['app']['timezone']);
}

// Configurar locale
if (isset($config['app']['locale'])) {
    setlocale(LC_ALL, $config['app']['locale'] . '.UTF-8');
}

// Inicializar aplicación
try {
    // Importar clases necesarias
    use ControlGastos\Core\Application;
    use ControlGastos\Core\Router;
    use ControlGastos\Core\Database;
    use ControlGastos\Core\Session;
    use ControlGastos\Core\Logger;
    use ControlGastos\Middleware\SecurityMiddleware;
    use ControlGastos\Middleware\AuthMiddleware;
    use ControlGastos\Middleware\CsrfMiddleware;

    // Inicializar logger
    $logger = new Logger($config);
    
    // Inicializar base de datos
    $database = new Database($config);
    
    // Inicializar sesión
    $session = new Session($config);
    $session->start();
    
    // Crear aplicación
    $app = new Application($config, $database, $session, $logger);
    
    // Configurar router
    $router = new Router();
    
    // Middleware global
    $router->addMiddleware(new SecurityMiddleware());
    $router->addMiddleware(new CsrfMiddleware($session));
    
    // =====================================================
    // RUTAS PÚBLICAS (sin autenticación)
    // =====================================================
    
    // Página principal
    $router->get('/', 'HomeController@index');
    
    // Autenticación
    $router->get('/auth/login', 'AuthController@showLogin');
    $router->post('/auth/login', 'AuthController@login');
    $router->get('/auth/register', 'AuthController@showRegister');
    $router->post('/auth/register', 'AuthController@register');
    $router->get('/auth/logout', 'AuthController@logout');
    
    // Recuperación de contraseña
    $router->get('/auth/forgot-password', 'AuthController@showForgotPassword');
    $router->post('/auth/forgot-password', 'AuthController@forgotPassword');
    $router->get('/auth/reset-password', 'AuthController@showResetPassword');
    $router->post('/auth/reset-password', 'AuthController@resetPassword');
    
    // Verificación de email
    $router->get('/auth/verify-email', 'AuthController@verifyEmail');
    $router->post('/auth/resend-verification', 'AuthController@resendVerification');
    
    // =====================================================
    // RUTAS PROTEGIDAS (requieren autenticación)
    // =====================================================
    
    $router->group(['middleware' => [new AuthMiddleware($session)]], function($router) {
        
        // Dashboard
        $router->get('/dashboard', 'DashboardController@index');
        $router->get('/dashboard/refresh', 'DashboardController@refresh');
        $router->get('/dashboard/chart-data', 'DashboardController@getChartData');
        
        // Perfil de usuario
        $router->get('/profile', 'ProfileController@show');
        $router->post('/profile', 'ProfileController@update');
        $router->post('/profile/password', 'ProfileController@updatePassword');
        $router->post('/profile/2fa/enable', 'ProfileController@enable2FA');
        $router->post('/profile/2fa/disable', 'ProfileController@disable2FA');
        
        // Cuentas
        $router->get('/accounts', 'AccountController@index');
        $router->get('/accounts/create', 'AccountController@create');
        $router->post('/accounts', 'AccountController@store');
        $router->get('/accounts/{id}', 'AccountController@show');
        $router->get('/accounts/{id}/edit', 'AccountController@edit');
        $router->put('/accounts/{id}', 'AccountController@update');
        $router->delete('/accounts/{id}', 'AccountController@delete');
        $router->get('/accounts/transfer', 'AccountController@showTransfer');
        $router->post('/accounts/transfer', 'AccountController@transfer');
        $router->patch('/accounts/{id}/toggle-status', 'AccountController@toggleStatus');
        
        // Categorías
        $router->get('/categories', 'CategoryController@index');
        $router->post('/categories', 'CategoryController@store');
        $router->put('/categories/{id}', 'CategoryController@update');
        $router->delete('/categories/{id}', 'CategoryController@delete');
        $router->patch('/categories/{id}/toggle-status', 'CategoryController@toggleStatus');
        $router->get('/categories/{id}/subcategories', 'CategoryController@getSubcategories');
        $router->get('/categories/search', 'CategoryController@search');
        $router->get('/categories/export', 'CategoryController@export');
        
        // Subcategorías
        $router->post('/subcategories', 'CategoryController@storeSubcategory');
        $router->put('/subcategories/{id}', 'CategoryController@updateSubcategory');
        $router->delete('/subcategories/{id}', 'CategoryController@deleteSubcategory');
        
        // Transacciones
        $router->get('/transactions', 'TransactionController@index');
        $router->get('/transactions/create', 'TransactionController@create');
        $router->post('/transactions', 'TransactionController@store');
        $router->get('/transactions/{id}', 'TransactionController@show');
        $router->get('/transactions/{id}/edit', 'TransactionController@edit');
        $router->put('/transactions/{id}', 'TransactionController@update');
        $router->delete('/transactions/{id}', 'TransactionController@delete');
        $router->get('/transactions/search', 'TransactionController@search');
        $router->get('/transactions/stats', 'TransactionController@getStats');
        $router->get('/transactions/export', 'TransactionController@export');
        $router->get('/transactions/categories/{id}/subcategories', 'TransactionController@getSubcategories');

        // Recordatorios
        $router->get('/reminders', 'ReminderController@index');
        $router->get('/reminders/create', 'ReminderController@create');
        $router->post('/reminders', 'ReminderController@store');
        $router->get('/reminders/{id}', 'ReminderController@show');
        $router->get('/reminders/{id}/edit', 'ReminderController@edit');
        $router->put('/reminders/{id}', 'ReminderController@update');
        $router->delete('/reminders/{id}', 'ReminderController@delete');
        $router->post('/reminders/{id}/complete', 'ReminderController@markCompleted');
        $router->get('/reminders/search', 'ReminderController@search');
        $router->get('/reminders/stats', 'ReminderController@getStats');
        $router->get('/reminders/calendar', 'ReminderController@calendar');
        $router->get('/reminders/dashboard', 'ReminderController@dashboard');
        $router->get('/reminders/export', 'ReminderController@export');

        // Compromisos
        $router->get('/commitments', 'CommitmentController@index');
        $router->get('/commitments/create', 'CommitmentController@create');
        $router->post('/commitments', 'CommitmentController@store');
        $router->get('/commitments/{id}/edit', 'CommitmentController@edit');
        $router->put('/commitments/{id}', 'CommitmentController@update');
        $router->delete('/commitments/{id}', 'CommitmentController@delete');
        $router->post('/commitments/{id}/pay', 'CommitmentController@markAsPaid');
        
        // Reportes
        $router->get('/reports', 'ReportController@index');
        $router->get('/reports/cash-flow', 'ReportController@cashFlow');
        $router->get('/reports/category-expenses', 'ReportController@categoryExpenses');
        $router->get('/reports/trends', 'ReportController@trends');
        $router->get('/reports/budget', 'ReportController@budget');
        $router->get('/reports/accounts', 'ReportController@accounts');
        $router->get('/reports/compare-periods', 'ReportController@comparePeriods');
        $router->get('/reports/chart-data', 'ReportController@getChartData');
        $router->get('/reports/export/financial-summary', 'ReportController@exportFinancialSummary');
        $router->get('/reports/export/category-report', 'ReportController@exportCategoryReport');
        $router->get('/reports/export/cash-flow', 'ReportController@exportCashFlow');
        
        // Backup
        $router->get('/backup', 'BackupController@index');
        $router->post('/backup/create', 'BackupController@create');
        $router->get('/backup/download/{id}', 'BackupController@download');
        $router->post('/backup/restore', 'BackupController@restore');
        $router->delete('/backup/{id}', 'BackupController@delete');
        
        // Notificaciones
        $router->get('/notifications', 'NotificationController@index');
        $router->post('/notifications/{id}/read', 'NotificationController@markAsRead');
        $router->post('/notifications/read-all', 'NotificationController@markAllAsRead');
        
    });
    
    // =====================================================
    // RUTAS API
    // =====================================================
    
    $router->group(['prefix' => '/api/v1'], function($router) {
        
        // API de dashboard
        $router->get('/dashboard/summary', 'Api\DashboardController@summary');
        $router->get('/dashboard/chart-data', 'Api\DashboardController@chartData');
        
        // API de transacciones
        $router->get('/transactions', 'Api\TransactionController@index');
        $router->post('/transactions', 'Api\TransactionController@store');
        
        // API de cuentas
        $router->get('/accounts', 'Api\AccountController@index');
        $router->post('/accounts', 'Api\AccountController@store');
        $router->get('/accounts/{id}', 'Api\AccountController@show');
        $router->put('/accounts/{id}', 'Api\AccountController@update');
        $router->delete('/accounts/{id}', 'Api\AccountController@delete');
        $router->get('/accounts/{id}/balance', 'Api\AccountController@balance');
        $router->post('/accounts/transfer', 'Api\AccountController@transfer');
        $router->patch('/accounts/{id}/toggle-status', 'Api\AccountController@toggleStatus');
        $router->get('/accounts/summary', 'Api\AccountController@summary');
        $router->get('/accounts/types', 'Api\AccountController@types');
        $router->get('/accounts/currencies', 'Api\AccountController@currencies');
        
        // API de categorías
        $router->get('/categories', 'Api\CategoryController@index');
        $router->post('/categories', 'Api\CategoryController@store');
        $router->get('/categories/{id}', 'Api\CategoryController@show');
        $router->put('/categories/{id}', 'Api\CategoryController@update');
        $router->delete('/categories/{id}', 'Api\CategoryController@delete');
        $router->get('/categories/{id}/subcategories', 'Api\CategoryController@subcategories');
        $router->patch('/categories/{id}/toggle-status', 'Api\CategoryController@toggleStatus');
        $router->get('/categories/stats', 'Api\CategoryController@stats');
        $router->get('/categories/options', 'Api\CategoryController@options');

        // API de subcategorías
        $router->post('/subcategories', 'Api\CategoryController@storeSubcategory');
        $router->put('/subcategories/{id}', 'Api\CategoryController@updateSubcategory');
        $router->delete('/subcategories/{id}', 'Api\CategoryController@deleteSubcategory');

        // API de transacciones
        $router->get('/transactions', 'Api\TransactionController@index');
        $router->post('/transactions', 'Api\TransactionController@store');
        $router->get('/transactions/{id}', 'Api\TransactionController@show');
        $router->put('/transactions/{id}', 'Api\TransactionController@update');
        $router->delete('/transactions/{id}', 'Api\TransactionController@delete');
        $router->get('/transactions/search', 'Api\TransactionController@search');
        $router->get('/transactions/stats', 'Api\TransactionController@stats');
        $router->get('/transactions/types', 'Api\TransactionController@types');
        $router->get('/transactions/recurring-types', 'Api\TransactionController@recurringTypes');
        $router->post('/transactions/process-recurring', 'Api\TransactionController@processRecurring');
        $router->get('/transactions/export', 'Api\TransactionController@export');

        // Backup y Seguridad
        $router->get('/backup', 'BackupController@index');
        $router->post('/backup/create', 'BackupController@createBackup');
        $router->post('/backup/restore', 'BackupController@restoreBackup');
        $router->delete('/backup/{id}', 'BackupController@deleteBackup');
        $router->get('/backup/{id}/verify', 'BackupController@verifyBackup');
        $router->get('/backup/{id}/download', 'BackupController@downloadBackup');
        $router->post('/backup/schedule', 'BackupController@scheduleBackup');
        $router->post('/backup/clean', 'BackupController@cleanOldBackups');
        $router->get('/backup/audit-log', 'BackupController@auditLog');
        $router->get('/backup/security-analysis', 'BackupController@securityAnalysis');

        // Optimización y Rendimiento
        $router->get('/optimization', 'OptimizationController@index');
        $router->get('/optimization/performance', 'OptimizationController@performance');
        $router->get('/optimization/cache', 'OptimizationController@cache');
        $router->get('/optimization/database', 'OptimizationController@database');
        $router->post('/optimization/clear-cache', 'OptimizationController@clearCache');
        $router->post('/optimization/optimize-database', 'OptimizationController@optimizeDatabase');
        $router->get('/optimization/metrics', 'OptimizationController@getRealTimeMetrics');
        $router->get('/optimization/report', 'OptimizationController@generateReport');
        $router->post('/optimization/configure-alerts', 'OptimizationController@configureAlerts');

    });
    
    // Ejecutar aplicación
    $app->run($router);
    
} catch (Exception $e) {
    // Manejo de errores global
    http_response_code(500);
    
    if (isset($config['app']['debug']) && $config['app']['debug']) {
        echo '<h1>Error de Aplicación</h1>';
        echo '<p><strong>Mensaje:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>Archivo:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Línea:</strong> ' . $e->getLine() . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        echo '<h1>Error del Servidor</h1>';
        echo '<p>Ha ocurrido un error interno. Por favor, inténtelo más tarde.</p>';
    }
    
    // Log del error
    if (isset($logger)) {
        $logger->error('Error de aplicación: ' . $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}
