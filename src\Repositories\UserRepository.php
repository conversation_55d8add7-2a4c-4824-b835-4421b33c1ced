<?php

declare(strict_types=1);

namespace ControlGastos\Repositories;

use ControlGastos\Core\Database;
use ControlGastos\Models\User;
use Exception;

/**
 * Repositorio de usuarios
 * Maneja todas las operaciones de base de datos relacionadas con usuarios
 */
class UserRepository
{
    private Database $database;

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Crear nuevo usuario
     */
    public function create(User $user): User
    {
        $query = "
            INSERT INTO users (
                email, password_hash, first_name, last_name, phone,
                two_factor_enabled, two_factor_secret, email_verified,
                email_verification_token, status, created_at, updated_at
            ) VALUES (
                :email, :password_hash, :first_name, :last_name, :phone,
                :two_factor_enabled, :two_factor_secret, :email_verified,
                :email_verification_token, :status, :created_at, :updated_at
            )
        ";

        $params = [
            'email' => $user->getEmail(),
            'password_hash' => $user->getPasswordHash(),
            'first_name' => $user->getFirstName(),
            'last_name' => $user->getLastName(),
            'phone' => $user->getPhone(),
            'two_factor_enabled' => $user->isTwoFactorEnabled() ? 1 : 0,
            'two_factor_secret' => $user->getTwoFactorSecret(),
            'email_verified' => $user->isEmailVerified() ? 1 : 0,
            'email_verification_token' => $user->getEmailVerificationToken(),
            'status' => $user->getStatus(),
            'created_at' => $user->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $user->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        $id = $this->database->insert($query, $params);
        $user->setId($id);

        return $user;
    }

    /**
     * Buscar usuario por ID
     */
    public function findById(int $id): ?User
    {
        $query = "SELECT * FROM users WHERE id = :id";
        $result = $this->database->selectOne($query, ['id' => $id]);

        return $result ? new User($result) : null;
    }

    /**
     * Buscar usuario por email
     */
    public function findByEmail(string $email): ?User
    {
        $query = "SELECT * FROM users WHERE email = :email";
        $result = $this->database->selectOne($query, ['email' => $email]);

        return $result ? new User($result) : null;
    }

    /**
     * Buscar usuario por token de verificación de email
     */
    public function findByEmailVerificationToken(string $token): ?User
    {
        $query = "SELECT * FROM users WHERE email_verification_token = :token";
        $result = $this->database->selectOne($query, ['token' => $token]);

        return $result ? new User($result) : null;
    }

    /**
     * Buscar usuario por token de reset de contraseña
     */
    public function findByPasswordResetToken(string $token): ?User
    {
        $query = "
            SELECT * FROM users 
            WHERE password_reset_token = :token 
            AND password_reset_expires > NOW()
        ";
        $result = $this->database->selectOne($query, ['token' => $token]);

        return $result ? new User($result) : null;
    }

    /**
     * Actualizar usuario
     */
    public function update(User $user): bool
    {
        $query = "
            UPDATE users SET
                email = :email,
                password_hash = :password_hash,
                first_name = :first_name,
                last_name = :last_name,
                phone = :phone,
                two_factor_enabled = :two_factor_enabled,
                two_factor_secret = :two_factor_secret,
                email_verified = :email_verified,
                email_verification_token = :email_verification_token,
                password_reset_token = :password_reset_token,
                password_reset_expires = :password_reset_expires,
                status = :status,
                updated_at = :updated_at
            WHERE id = :id
        ";

        $params = [
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'password_hash' => $user->getPasswordHash(),
            'first_name' => $user->getFirstName(),
            'last_name' => $user->getLastName(),
            'phone' => $user->getPhone(),
            'two_factor_enabled' => $user->isTwoFactorEnabled() ? 1 : 0,
            'two_factor_secret' => $user->getTwoFactorSecret(),
            'email_verified' => $user->isEmailVerified() ? 1 : 0,
            'email_verification_token' => $user->getEmailVerificationToken(),
            'password_reset_token' => $user->getPasswordResetToken(),
            'password_reset_expires' => $user->getPasswordResetExpires()?->format('Y-m-d H:i:s'),
            'status' => $user->getStatus(),
            'updated_at' => $user->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        return $this->database->update($query, $params) > 0;
    }

    /**
     * Eliminar usuario
     */
    public function delete(int $id): bool
    {
        $query = "DELETE FROM users WHERE id = :id";
        return $this->database->delete($query, ['id' => $id]) > 0;
    }

    /**
     * Verificar si existe un email
     */
    public function emailExists(string $email, ?int $excludeId = null): bool
    {
        $query = "SELECT COUNT(*) as count FROM users WHERE email = :email";
        $params = ['email' => $email];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $result = $this->database->selectOne($query, $params);
        return $result['count'] > 0;
    }

    /**
     * Obtener usuarios con paginación
     */
    public function getPaginated(int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = [];
        $params = [];

        // Filtros
        if (!empty($filters['status'])) {
            $whereConditions[] = "status = :status";
            $params['status'] = $filters['status'];
        }

        if (!empty($filters['email_verified'])) {
            $whereConditions[] = "email_verified = :email_verified";
            $params['email_verified'] = $filters['email_verified'] ? 1 : 0;
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = "(first_name LIKE :search OR last_name LIKE :search OR email LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Consulta principal
        $query = "
            SELECT * FROM users 
            {$whereClause}
            ORDER BY created_at DESC 
            LIMIT :limit OFFSET :offset
        ";

        $params['limit'] = $perPage;
        $params['offset'] = $offset;

        $results = $this->database->select($query, $params);
        $users = array_map(fn($row) => new User($row), $results);

        // Contar total
        $countQuery = "SELECT COUNT(*) as total FROM users {$whereClause}";
        unset($params['limit'], $params['offset']);
        $totalResult = $this->database->selectOne($countQuery, $params);
        $total = $totalResult['total'];

        return [
            'data' => $users,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }

    /**
     * Obtener estadísticas de usuarios
     */
    public function getStats(): array
    {
        $query = "
            SELECT 
                COUNT(*) as total_users,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
                SUM(CASE WHEN email_verified = 1 THEN 1 ELSE 0 END) as verified_users,
                SUM(CASE WHEN two_factor_enabled = 1 THEN 1 ELSE 0 END) as two_factor_users,
                SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_30_days
            FROM users
        ";

        return $this->database->selectOne($query) ?: [];
    }

    /**
     * Limpiar tokens expirados
     */
    public function cleanExpiredTokens(): int
    {
        $query = "
            UPDATE users SET 
                password_reset_token = NULL,
                password_reset_expires = NULL,
                updated_at = NOW()
            WHERE password_reset_expires < NOW()
        ";

        return $this->database->update($query);
    }
}
