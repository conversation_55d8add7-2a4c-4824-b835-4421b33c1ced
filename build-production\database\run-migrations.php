<?php
/**
 * Ejecutor de migraciones para cPanel
 */

require_once '../src/bootstrap.php';

echo "<h1>📊 Ejecutando Migraciones</h1>";

try {
    $database = $container->get('database');
    
    // Leer archivos de migración
    $migration_files = glob(__DIR__ . '/migrations/*.sql');
    sort($migration_files);
    
    foreach ($migration_files as $file) {
        $filename = basename($file);
        echo "Ejecutando: $filename<br>";
        
        $sql = file_get_contents($file);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $database->execute($statement);
            }
        }
        
        echo "✅ $filename completado<br>";
    }
    
    echo "<h2>✅ Todas las migraciones ejecutadas correctamente</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error ejecutando migraciones:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
