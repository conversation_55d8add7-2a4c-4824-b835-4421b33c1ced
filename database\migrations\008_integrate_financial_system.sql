-- Migración 008: Integración completa del sistema financiero
-- Fecha: 2024-12-30
-- Descripción: Inte<PERSON>r gastos, ingresos y pagos con cuentas bancarias y tarjetas

-- La tabla transactions ya tiene los campos necesarios, solo verificamos restricciones

-- Verificar si las restricciones ya existen antes de agregarlas
-- Agregar restricciones de integridad si no existen
SET @constraint_exists = (SELECT COUNT(*) FROM information_schema.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_SCHEMA = 'control_gastos'
    AND TABLE_NAME = 'transactions'
    AND CONSTRAINT_NAME = 'fk_transactions_bank_account');

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE transactions ADD CONSTRAINT fk_transactions_bank_account FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE SET NULL',
    'SELECT "Constraint fk_transactions_bank_account already exists" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @constraint_exists = (SELECT COUNT(*) FROM information_schema.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_SCHEMA = 'control_gastos'
    AND TABLE_NAME = 'transactions'
    AND CONSTRAINT_NAME = 'fk_transactions_credit_card');

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE transactions ADD CONSTRAINT fk_transactions_credit_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE SET NULL',
    'SELECT "Constraint fk_transactions_credit_card already exists" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Crear tabla para movimientos unificados (timeline)
CREATE TABLE IF NOT EXISTS unified_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    movement_type ENUM('expense', 'income', 'credit_payment', 'bank_movement', 'credit_transaction') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description VARCHAR(255) NOT NULL,
    movement_date DATE NOT NULL,

    -- Referencias a las tablas originales
    transaction_id INT NULL COMMENT 'ID de transacción (gasto o ingreso)',
    credit_payment_id INT NULL COMMENT 'ID de pago de tarjeta',
    bank_movement_id INT NULL COMMENT 'ID de movimiento bancario',
    credit_transaction_id INT NULL COMMENT 'ID de transacción de tarjeta',

    -- Información del método de pago/destino
    bank_account_id INT NULL,
    credit_card_id INT NULL,

    -- Metadatos
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Índices
    INDEX idx_user_id (user_id),
    INDEX idx_movement_date (movement_date),
    INDEX idx_movement_type (movement_type),
    INDEX idx_bank_account (bank_account_id),
    INDEX idx_credit_card (credit_card_id),

    -- Restricciones
    CONSTRAINT fk_unified_movements_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_unified_movements_transaction FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    CONSTRAINT fk_unified_movements_credit_payment FOREIGN KEY (credit_payment_id) REFERENCES credit_card_payments(id) ON DELETE CASCADE,
    CONSTRAINT fk_unified_movements_bank_movement FOREIGN KEY (bank_movement_id) REFERENCES bank_account_movements(id) ON DELETE CASCADE,
    CONSTRAINT fk_unified_movements_credit_transaction FOREIGN KEY (credit_transaction_id) REFERENCES credit_card_transactions(id) ON DELETE CASCADE,
    CONSTRAINT fk_unified_movements_bank_account FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE SET NULL,
    CONSTRAINT fk_unified_movements_credit_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE SET NULL
) COMMENT = 'Timeline unificado de todos los movimientos financieros';

-- Crear triggers para mantener el timeline actualizado
DELIMITER //

-- Trigger para transacciones (gastos e ingresos)
CREATE TRIGGER sync_unified_movements_transactions
AFTER INSERT ON transactions
FOR EACH ROW
BEGIN
    INSERT INTO unified_movements (
        user_id, movement_type, amount, description, movement_date,
        transaction_id, bank_account_id, credit_card_id
    ) VALUES (
        NEW.user_id, NEW.type, NEW.amount, NEW.description, NEW.transaction_date,
        NEW.id, NEW.bank_account_id, NEW.credit_card_id
    );
END//

-- Trigger para pagos de tarjetas
CREATE TRIGGER sync_unified_movements_credit_payments
AFTER INSERT ON credit_card_payments
FOR EACH ROW
BEGIN
    INSERT INTO unified_movements (
        user_id, movement_type, amount, description, movement_date,
        credit_payment_id, bank_account_id, credit_card_id
    ) VALUES (
        NEW.user_id, 'credit_payment', NEW.amount,
        CONCAT('Pago tarjeta - ', COALESCE(NEW.notes, 'Sin descripción')), NEW.payment_date,
        NEW.id, NEW.bank_account_id, NEW.credit_card_id
    );
END//

-- Trigger para movimientos bancarios
CREATE TRIGGER sync_unified_movements_bank_movements
AFTER INSERT ON bank_account_movements
FOR EACH ROW
BEGIN
    INSERT INTO unified_movements (
        user_id, movement_type, amount, description, movement_date,
        bank_movement_id, bank_account_id
    ) VALUES (
        NEW.user_id, 'bank_movement', NEW.amount, NEW.description, NEW.movement_date,
        NEW.id, NEW.bank_account_id
    );
END//

-- Trigger para transacciones de tarjetas
CREATE TRIGGER sync_unified_movements_credit_transactions
AFTER INSERT ON credit_card_transactions
FOR EACH ROW
BEGIN
    INSERT INTO unified_movements (
        user_id, movement_type, amount, description, movement_date,
        credit_transaction_id, credit_card_id
    ) VALUES (
        NEW.user_id, 'credit_transaction', NEW.amount, NEW.description, NEW.transaction_date,
        NEW.id, NEW.credit_card_id
    );
END//

DELIMITER ;

-- Poblar el timeline con datos existentes
INSERT INTO unified_movements (
    user_id, movement_type, amount, description, movement_date,
    transaction_id, bank_account_id, credit_card_id
)
SELECT
    user_id, type, amount, description, transaction_date,
    id, bank_account_id, credit_card_id
FROM transactions;

INSERT INTO unified_movements (
    user_id, movement_type, amount, description, movement_date,
    credit_payment_id, bank_account_id, credit_card_id
)
SELECT
    user_id, 'credit_payment', amount,
    CONCAT('Pago tarjeta - ', COALESCE(notes, 'Sin descripción')), payment_date,
    id, bank_account_id, credit_card_id
FROM credit_card_payments;

INSERT INTO unified_movements (
    user_id, movement_type, amount, description, movement_date,
    bank_movement_id, bank_account_id
)
SELECT 
    user_id, 'bank_movement', amount, description, movement_date,
    id, bank_account_id
FROM bank_account_movements;

INSERT INTO unified_movements (
    user_id, movement_type, amount, description, movement_date,
    credit_transaction_id, credit_card_id
)
SELECT 
    user_id, 'credit_transaction', amount, description, transaction_date,
    id, credit_card_id
FROM credit_card_transactions;
