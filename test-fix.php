<?php
echo "🧪 VERIFICACIÓN POST-CORRECCIÓN\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// Test básico de conexión y cálculos
$config = [
    'host' => 'localhost',
    'dbname' => 'control_gastos',
    'username' => 'root',
    'password' => '',
];

try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4";
    $db = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✅ Conexión exitosa\n";
    
    // Verificar que las tarjetas existen
    $stmt = $db->query('SELECT id, card_name, cut_off_day FROM credit_cards ORDER BY id');
    $cards = $stmt->fetchAll();
    
    echo "📊 Tarjetas encontradas: " . count($cards) . "\n";
    
    foreach ($cards as $card) {
        echo "  • ID: " . $card['id'] . " - " . $card['card_name'] . " (Corte: " . $card['cut_off_day'] . ")\n";
        
        // Test del cálculo de fecha de corte (esto era lo que fallaba)
        $cutOffDay = (int)$card['cut_off_day'];
        $today = new DateTime();
        $currentMonth = $today->format('Y-m');
        $cutOffDate = new DateTime($currentMonth . '-' . str_pad((string)$cutOffDay, 2, '0', STR_PAD_LEFT));
        
        echo "    Próxima fecha de corte: " . $cutOffDate->format('Y-m-d') . "\n";
    }
    
    echo "\n✅ Todos los cálculos funcionan correctamente\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎉 VERIFICACIÓN COMPLETADA\n";
