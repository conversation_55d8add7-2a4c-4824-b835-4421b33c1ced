<?php

declare(strict_types=1);

namespace ControlGastos\Repositories;

use ControlGastos\Core\Database;
use ControlGastos\Models\Reminder;
use Exception;

/**
 * Repositorio de recordatorios
 * Maneja todas las operaciones de base de datos relacionadas con recordatorios
 */
class ReminderRepository
{
    private Database $database;

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Crear nuevo recordatorio
     */
    public function create(Reminder $reminder): Reminder
    {
        $query = "
            INSERT INTO reminders (
                user_id, title, description, type, amount, category_id, subcategory_id,
                account_id, due_date, priority, status, is_recurring, recurring_type,
                recurring_interval, recurring_end_date, auto_create_transaction,
                notification_enabled, notification_days_before, notes, created_at, updated_at
            ) VALUES (
                :user_id, :title, :description, :type, :amount, :category_id, :subcategory_id,
                :account_id, :due_date, :priority, :status, :is_recurring, :recurring_type,
                :recurring_interval, :recurring_end_date, :auto_create_transaction,
                :notification_enabled, :notification_days_before, :notes, :created_at, :updated_at
            )
        ";

        $params = [
            'user_id' => $reminder->getUserId(),
            'title' => $reminder->getTitle(),
            'description' => $reminder->getDescription(),
            'type' => $reminder->getType(),
            'amount' => $reminder->getAmount(),
            'category_id' => $reminder->getCategoryId(),
            'subcategory_id' => $reminder->getSubcategoryId(),
            'account_id' => $reminder->getAccountId(),
            'due_date' => $reminder->getDueDate()->format('Y-m-d H:i:s'),
            'priority' => $reminder->getPriority(),
            'status' => $reminder->getStatus(),
            'is_recurring' => $reminder->isRecurring() ? 1 : 0,
            'recurring_type' => $reminder->getRecurringType(),
            'recurring_interval' => $reminder->getRecurringInterval(),
            'recurring_end_date' => $reminder->getRecurringEndDate()?->format('Y-m-d H:i:s'),
            'auto_create_transaction' => $reminder->getAutoCreateTransaction() ? 1 : 0,
            'notification_enabled' => $reminder->isNotificationEnabled() ? 1 : 0,
            'notification_days_before' => $reminder->getNotificationDaysBefore(),
            'notes' => $reminder->getNotes(),
            'created_at' => $reminder->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $reminder->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        $id = $this->database->insert($query, $params);
        $reminder->setId($id);

        return $reminder;
    }

    /**
     * Buscar recordatorio por ID
     */
    public function findById(int $id): ?Reminder
    {
        $query = "SELECT * FROM reminders WHERE id = :id";
        $result = $this->database->selectOne($query, ['id' => $id]);

        return $result ? new Reminder($result) : null;
    }

    /**
     * Buscar recordatorio por ID y usuario
     */
    public function findByIdAndUser(int $id, int $userId): ?Reminder
    {
        $query = "SELECT * FROM reminders WHERE id = :id AND user_id = :user_id";
        $result = $this->database->selectOne($query, ['id' => $id, 'user_id' => $userId]);

        return $result ? new Reminder($result) : null;
    }

    /**
     * Obtener recordatorios por usuario
     */
    public function findByUser(int $userId, array $filters = []): array
    {
        $whereConditions = ['user_id = :user_id'];
        $params = ['user_id' => $userId];

        // Aplicar filtros
        if (!empty($filters['status'])) {
            $whereConditions[] = "status = :status";
            $params['status'] = $filters['status'];
        }

        if (!empty($filters['type'])) {
            $whereConditions[] = "type = :type";
            $params['type'] = $filters['type'];
        }

        if (!empty($filters['priority'])) {
            $whereConditions[] = "priority = :priority";
            $params['priority'] = $filters['priority'];
        }

        if (!empty($filters['category_id'])) {
            $whereConditions[] = "category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        if (!empty($filters['start_date'])) {
            $whereConditions[] = "due_date >= :start_date";
            $params['start_date'] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $whereConditions[] = "due_date <= :end_date";
            $params['end_date'] = $filters['end_date'];
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        $query = "
            SELECT r.*, 
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name,
                   a.name as account_name
            FROM reminders r
            INNER JOIN categories c ON r.category_id = c.id
            LEFT JOIN subcategories s ON r.subcategory_id = s.id
            LEFT JOIN accounts a ON r.account_id = a.id
            {$whereClause}
            ORDER BY r.due_date ASC, r.priority DESC
        ";

        $results = $this->database->select($query, $params);
        return array_map(fn($row) => $this->mapReminderWithRelations($row), $results);
    }

    /**
     * Obtener recordatorios vencidos
     */
    public function findOverdue(int $userId): array
    {
        $query = "
            SELECT r.*, 
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name,
                   a.name as account_name
            FROM reminders r
            INNER JOIN categories c ON r.category_id = c.id
            LEFT JOIN subcategories s ON r.subcategory_id = s.id
            LEFT JOIN accounts a ON r.account_id = a.id
            WHERE r.user_id = :user_id 
            AND r.due_date < NOW() 
            AND r.status NOT IN ('completed', 'cancelled')
            ORDER BY r.due_date ASC, r.priority DESC
        ";

        $results = $this->database->select($query, ['user_id' => $userId]);
        return array_map(fn($row) => $this->mapReminderWithRelations($row), $results);
    }

    /**
     * Obtener recordatorios que vencen pronto
     */
    public function findDueSoon(int $userId, int $days = 7): array
    {
        $query = "
            SELECT r.*, 
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name,
                   a.name as account_name
            FROM reminders r
            INNER JOIN categories c ON r.category_id = c.id
            LEFT JOIN subcategories s ON r.subcategory_id = s.id
            LEFT JOIN accounts a ON r.account_id = a.id
            WHERE r.user_id = :user_id 
            AND r.due_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL :days DAY)
            AND r.status = 'pending'
            ORDER BY r.due_date ASC, r.priority DESC
        ";

        $results = $this->database->select($query, ['user_id' => $userId, 'days' => $days]);
        return array_map(fn($row) => $this->mapReminderWithRelations($row), $results);
    }

    /**
     * Obtener recordatorios que necesitan notificación
     */
    public function findNeedingNotification(): array
    {
        $query = "
            SELECT r.*, 
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name,
                   a.name as account_name,
                   u.email as user_email, u.name as user_name
            FROM reminders r
            INNER JOIN categories c ON r.category_id = c.id
            LEFT JOIN subcategories s ON r.subcategory_id = s.id
            LEFT JOIN accounts a ON r.account_id = a.id
            INNER JOIN users u ON r.user_id = u.id
            WHERE r.notification_enabled = 1
            AND r.status NOT IN ('completed', 'cancelled')
            AND (
                (r.due_date <= DATE_ADD(NOW(), INTERVAL r.notification_days_before DAY) AND r.due_date >= NOW())
                OR r.due_date < NOW()
            )
            AND (
                r.last_notification_sent IS NULL 
                OR DATE(r.last_notification_sent) < CURDATE()
            )
            ORDER BY r.due_date ASC
        ";

        $results = $this->database->select($query);
        return array_map(fn($row) => $this->mapReminderWithRelations($row), $results);
    }

    /**
     * Obtener recordatorios recurrentes para procesar
     */
    public function findRecurringDue(): array
    {
        $query = "
            SELECT * FROM reminders 
            WHERE is_recurring = 1 
            AND status = 'completed'
            AND (recurring_end_date IS NULL OR recurring_end_date > NOW())
            ORDER BY due_date ASC
        ";

        $results = $this->database->select($query);
        return array_map(fn($row) => new Reminder($row), $results);
    }

    /**
     * Actualizar recordatorio
     */
    public function update(Reminder $reminder): bool
    {
        $query = "
            UPDATE reminders SET
                title = :title,
                description = :description,
                type = :type,
                amount = :amount,
                category_id = :category_id,
                subcategory_id = :subcategory_id,
                account_id = :account_id,
                due_date = :due_date,
                priority = :priority,
                status = :status,
                is_recurring = :is_recurring,
                recurring_type = :recurring_type,
                recurring_interval = :recurring_interval,
                recurring_end_date = :recurring_end_date,
                auto_create_transaction = :auto_create_transaction,
                notification_enabled = :notification_enabled,
                notification_days_before = :notification_days_before,
                last_notification_sent = :last_notification_sent,
                completed_at = :completed_at,
                notes = :notes,
                updated_at = :updated_at
            WHERE id = :id
        ";

        $params = [
            'id' => $reminder->getId(),
            'title' => $reminder->getTitle(),
            'description' => $reminder->getDescription(),
            'type' => $reminder->getType(),
            'amount' => $reminder->getAmount(),
            'category_id' => $reminder->getCategoryId(),
            'subcategory_id' => $reminder->getSubcategoryId(),
            'account_id' => $reminder->getAccountId(),
            'due_date' => $reminder->getDueDate()->format('Y-m-d H:i:s'),
            'priority' => $reminder->getPriority(),
            'status' => $reminder->getStatus(),
            'is_recurring' => $reminder->isRecurring() ? 1 : 0,
            'recurring_type' => $reminder->getRecurringType(),
            'recurring_interval' => $reminder->getRecurringInterval(),
            'recurring_end_date' => $reminder->getRecurringEndDate()?->format('Y-m-d H:i:s'),
            'auto_create_transaction' => $reminder->getAutoCreateTransaction() ? 1 : 0,
            'notification_enabled' => $reminder->isNotificationEnabled() ? 1 : 0,
            'notification_days_before' => $reminder->getNotificationDaysBefore(),
            'last_notification_sent' => $reminder->getLastNotificationSent()?->format('Y-m-d H:i:s'),
            'completed_at' => $reminder->getCompletedAt()?->format('Y-m-d H:i:s'),
            'notes' => $reminder->getNotes(),
            'updated_at' => $reminder->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        return $this->database->update($query, $params) > 0;
    }

    /**
     * Eliminar recordatorio
     */
    public function delete(int $id): bool
    {
        $query = "DELETE FROM reminders WHERE id = :id";
        return $this->database->delete($query, ['id' => $id]) > 0;
    }

    /**
     * Actualizar estados automáticamente
     */
    public function updateOverdueStatuses(): int
    {
        $query = "
            UPDATE reminders 
            SET status = 'overdue', updated_at = NOW()
            WHERE due_date < NOW() 
            AND status = 'pending'
        ";

        return $this->database->update($query, []);
    }

    /**
     * Obtener estadísticas de recordatorios
     */
    public function getStats(int $userId): array
    {
        $query = "
            SELECT 
                COUNT(*) as total_reminders,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_reminders,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_reminders,
                SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue_reminders,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_reminders,
                SUM(CASE WHEN due_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) AND status = 'pending' THEN 1 ELSE 0 END) as due_this_week,
                SUM(CASE WHEN priority = 'urgent' AND status IN ('pending', 'overdue') THEN 1 ELSE 0 END) as urgent_reminders,
                COALESCE(SUM(CASE WHEN status IN ('pending', 'overdue') THEN amount ELSE 0 END), 0) as pending_amount
            FROM reminders
            WHERE user_id = :user_id
        ";

        return $this->database->selectOne($query, ['user_id' => $userId]) ?: [];
    }

    /**
     * Buscar recordatorios
     */
    public function search(int $userId, string $search): array
    {
        $query = "
            SELECT r.*, 
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name,
                   a.name as account_name
            FROM reminders r
            INNER JOIN categories c ON r.category_id = c.id
            LEFT JOIN subcategories s ON r.subcategory_id = s.id
            LEFT JOIN accounts a ON r.account_id = a.id
            WHERE r.user_id = :user_id 
            AND (r.title LIKE :search OR r.description LIKE :search OR r.notes LIKE :search)
            ORDER BY r.due_date ASC
            LIMIT 50
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'search' => '%' . $search . '%'
        ]);

        return array_map(fn($row) => $this->mapReminderWithRelations($row), $results);
    }

    /**
     * Obtener recordatorios con paginación
     */
    public function getPaginated(int $userId, int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ['r.user_id = :user_id'];
        $params = ['user_id' => $userId];

        // Aplicar filtros
        if (!empty($filters['status'])) {
            $whereConditions[] = "r.status = :status";
            $params['status'] = $filters['status'];
        }

        if (!empty($filters['type'])) {
            $whereConditions[] = "r.type = :type";
            $params['type'] = $filters['type'];
        }

        if (!empty($filters['priority'])) {
            $whereConditions[] = "r.priority = :priority";
            $params['priority'] = $filters['priority'];
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = "(r.title LIKE :search OR r.description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Consulta principal
        $query = "
            SELECT r.*, 
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name,
                   a.name as account_name
            FROM reminders r
            INNER JOIN categories c ON r.category_id = c.id
            LEFT JOIN subcategories s ON r.subcategory_id = s.id
            LEFT JOIN accounts a ON r.account_id = a.id
            {$whereClause}
            ORDER BY r.due_date ASC, r.priority DESC
            LIMIT :limit OFFSET :offset
        ";

        $params['limit'] = $perPage;
        $params['offset'] = $offset;

        $results = $this->database->select($query, $params);
        $reminders = array_map(fn($row) => $this->mapReminderWithRelations($row), $results);

        // Contar total
        $countQuery = "
            SELECT COUNT(*) as total 
            FROM reminders r
            INNER JOIN categories c ON r.category_id = c.id
            LEFT JOIN subcategories s ON r.subcategory_id = s.id
            LEFT JOIN accounts a ON r.account_id = a.id
            {$whereClause}
        ";
        unset($params['limit'], $params['offset']);
        $totalResult = $this->database->selectOne($countQuery, $params);
        $total = $totalResult['total'];

        return [
            'data' => $reminders,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }

    /**
     * Mapear recordatorio con relaciones
     */
    private function mapReminderWithRelations(array $row): array
    {
        $reminder = new Reminder($row);
        $reminderArray = $reminder->toDetailedArray();
        
        // Agregar información de relaciones
        $reminderArray['category_name'] = $row['category_name'] ?? null;
        $reminderArray['category_color'] = $row['category_color'] ?? null;
        $reminderArray['category_icon'] = $row['category_icon'] ?? null;
        $reminderArray['subcategory_name'] = $row['subcategory_name'] ?? null;
        $reminderArray['account_name'] = $row['account_name'] ?? null;
        $reminderArray['user_email'] = $row['user_email'] ?? null;
        $reminderArray['user_name'] = $row['user_name'] ?? null;
        
        return $reminderArray;
    }

    /**
     * Obtener resumen por tipo
     */
    public function getTypeSummary(int $userId): array
    {
        $query = "
            SELECT 
                type,
                COUNT(*) as count,
                SUM(CASE WHEN status IN ('pending', 'overdue') THEN amount ELSE 0 END) as pending_amount,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount
            FROM reminders
            WHERE user_id = :user_id
            GROUP BY type
            ORDER BY pending_amount DESC
        ";

        return $this->database->select($query, ['user_id' => $userId]);
    }

    /**
     * Obtener recordatorios del calendario
     */
    public function getCalendarReminders(int $userId, \DateTime $startDate, \DateTime $endDate): array
    {
        $query = "
            SELECT r.*, 
                   c.name as category_name, c.color as category_color, c.icon as category_icon
            FROM reminders r
            INNER JOIN categories c ON r.category_id = c.id
            WHERE r.user_id = :user_id 
            AND r.due_date BETWEEN :start_date AND :end_date
            AND r.status NOT IN ('cancelled')
            ORDER BY r.due_date ASC
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'start_date' => $startDate->format('Y-m-d H:i:s'),
            'end_date' => $endDate->format('Y-m-d H:i:s')
        ]);

        return array_map(fn($row) => $this->mapReminderWithRelations($row), $results);
    }
}
