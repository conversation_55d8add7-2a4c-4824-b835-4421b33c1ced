<?php
// La autenticación ya se verifica en el controlador
$accountInfo = $account['account'];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-university me-2"></i><?= htmlspecialchars($accountInfo['account_name']) ?>
                        <span class="badge bg-<?= $accountInfo['status'] === 'active' ? 'success' : 'warning' ?> ms-2">
                            <?php
                            $statusLabels = [
                                'active' => 'Activa',
                                'inactive' => 'Inactiva',
                                'blocked' => 'Bloqueada',
                                'closed' => 'Cerrada'
                            ];
                            echo $statusLabels[$accountInfo['status']] ?? ucfirst($accountInfo['status']);
                            ?>
                        </span>
                    </h1>
                    <p class="text-muted">
                        <?= htmlspecialchars($accountInfo['bank_name']) ?>
                        <?php if (!empty($accountInfo['account_number'])): ?>
                        • Cuenta: **** <?= htmlspecialchars(substr($accountInfo['account_number'], -4)) ?>
                        <?php endif; ?>
                        <?php if (!empty($accountInfo['debit_card_number'])): ?>
                        • Débito: **** <?= htmlspecialchars($accountInfo['debit_card_number']) ?>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/controlGastos/public/?route=bank-accounts/edit&id=<?= $accountInfo['id'] ?>" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Editar
                    </a>
                    <a href="/controlGastos/public/?route=bank-accounts" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumen Financiero -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Saldo Actual</h5>
                    <h3 class="text-success">
                        $<?= number_format($account['current_balance'], 0) ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Saldo Inicial</h5>
                    <h3 class="text-info">
                        $<?= number_format($account['initial_balance'], 0) ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Cambio Neto</h5>
                    <h3 class="<?= $account['net_change'] >= 0 ? 'text-success' : 'text-danger' ?>">
                        <?= $account['net_change'] >= 0 ? '+' : '' ?>$<?= number_format($account['net_change'], 0) ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Movimientos del Mes</h5>
                    <h3 class="text-primary">
                        <?= $account['monthly_movements'] ?>
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas del Mes -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Depósitos del Mes</h6>
                            <h4 class="mb-0">$<?= number_format($account['monthly_deposits'], 0) ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Retiros del Mes</h6>
                            <h4 class="mb-0">$<?= number_format($account['monthly_withdrawals'], 0) ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Formulario de Movimientos -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>Nuevo Movimiento
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/controlGastos/public/?route=bank-accounts/add-movement">
                        <input type="hidden" name="bank_account_id" value="<?= $accountInfo['id'] ?>">
                        
                        <div class="mb-3">
                            <label for="movement_type" class="form-label">Tipo de Movimiento *</label>
                            <select class="form-select" id="movement_type" name="movement_type" required>
                                <option value="">Seleccionar...</option>
                                <option value="deposit">💰 Depósito / Consignación</option>
                                <option value="withdrawal">💸 Retiro</option>
                                <option value="transfer_in">📥 Transferencia Recibida</option>
                                <option value="transfer_out">📤 Transferencia Enviada</option>
                                <option value="fee">💳 Comisión / Cuota</option>
                                <option value="interest">📈 Interés Ganado</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="amount" class="form-label">Monto *</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="amount" name="amount"
                                       placeholder="50000" required min="1" step="0.01">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Descripción *</label>
                            <input type="text" class="form-control" id="description" name="description" 
                                   placeholder="Ej: Pago de nómina" required maxlength="255">
                        </div>

                        <div class="mb-3">
                            <label for="reference" class="form-label">Referencia</label>
                            <input type="text" class="form-control" id="reference" name="reference" 
                                   placeholder="Número de comprobante" maxlength="100">
                        </div>

                        <div class="mb-3">
                            <label for="movement_date" class="form-label">Fecha *</label>
                            <input type="date" class="form-control" id="movement_date" name="movement_date" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save me-2"></i>Registrar Movimiento
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Historial de Movimientos -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Historial de Movimientos
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($movements)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No hay movimientos registrados</h5>
                            <p class="text-muted">Los movimientos aparecerán aquí una vez que los registres</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Fecha</th>
                                        <th>Tipo</th>
                                        <th>Descripción</th>
                                        <th class="text-end">Monto</th>
                                        <th class="text-end">Saldo</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($movements as $movement): ?>
                                    <tr>
                                        <td>
                                            <small class="text-muted">
                                                <?= date('d/m/Y', strtotime($movement['movement_date'])) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= in_array($movement['movement_type'], ['deposit', 'transfer_in', 'interest']) ? 'success' : 'danger' ?>">
                                                <?php
                                                $typeLabels = [
                                                    'deposit' => 'Depósito',
                                                    'withdrawal' => 'Retiro',
                                                    'transfer_in' => 'Transferencia In',
                                                    'transfer_out' => 'Transferencia Out',
                                                    'fee' => 'Comisión',
                                                    'interest' => 'Interés'
                                                ];
                                                echo $typeLabels[$movement['movement_type']] ?? ucfirst($movement['movement_type']);
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?= htmlspecialchars($movement['description']) ?>
                                            <?php if ($movement['reference']): ?>
                                            <br><small class="text-muted">Ref: <?= htmlspecialchars($movement['reference']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-end">
                                            <strong class="<?= in_array($movement['movement_type'], ['deposit', 'transfer_in', 'interest']) ? 'text-success' : 'text-danger' ?>">
                                                <?= in_array($movement['movement_type'], ['deposit', 'transfer_in', 'interest']) ? '+' : '-' ?>$<?= number_format($movement['amount'], 0) ?>
                                            </strong>
                                        </td>
                                        <td class="text-end">
                                            <small class="text-muted">$<?= number_format($movement['balance_after'], 0) ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Validaciones del formulario
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[action*="add-movement"]');
    const movementType = document.getElementById('movement_type');
    const amount = document.getElementById('amount');
    
    // Cambiar color del formulario según el tipo
    movementType.addEventListener('change', function() {
        const card = form.closest('.card');
        card.className = 'card';
        
        if (['deposit', 'transfer_in', 'interest'].includes(this.value)) {
            card.classList.add('border-success');
        } else {
            card.classList.add('border-danger');
        }
    });
    
    // Validar fondos para retiros
    form.addEventListener('submit', function(e) {
        const currentBalance = <?= $account['current_balance'] ?>;
        const movementAmount = parseFloat(amount.value || 0);
        
        if (movementType.value === 'withdrawal' && movementAmount > currentBalance) {
            e.preventDefault();
            alert('No tienes fondos suficientes para este retiro.\nSaldo actual: $' + currentBalance.toLocaleString());
        }
    });
});
</script>
