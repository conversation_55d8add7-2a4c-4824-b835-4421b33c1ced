Diseña y desarrolla una aplicación web responsiva (php 8.3 y mysql) para control de gastos personales, con las siguientes funcionalidades clave:

Primero panifica todo lo que se va a desarrollar teniendo en cuenta las siguientes funcionalidades, como si fueras un arquitecto software senior.

💼 Gestión de Cuentas Financieras - Desarrollar CRUD para cuentas bancarias, efectivo, tarjetas
🧾 Sistema de Categorías y Subcategorías - Implementar clasificación jerárquica personalizable
💰 Gestión de Transacciones - Sistema completo de ingresos y egresos
🗓 Sistema de Recordatorios - Compromisos de pago y notificaciones
📧 Sistema de Correos SMTP - Envío de notificaciones y recordatorios
📊 Dashboard y Reportes - Panel principal con gráficos y estadísticas
💾 Sistema de Backup - Exportación y restauración de datos
🎨 Interfaz de Usuario - Frontend responsivo con Bootstrap 5
________________________________________
🔐 Gestión de Usuarios
•	Registro e inicio de sesión seguros (con correo electrónico y contraseña).
•	Autenticación con doble factor (opcional).
•	Gestión de perfiles de usuario.
________________________________________
💼 Cuentas e Ingresos
•	Crear múltiples cuentas financieras (efectivo, cuentas bancarias, tarjetas de crédito, etc.).
•	Registrar ingresos y egresos por cuenta.
•	Cada transacción debe poder asignarse a una categoría (ej. Hogar, Salud, Entretenimiento) y una subcategoría (ej. Arriendo, Medicamentos, Cine).
________________________________________
🧾 Clasificación y Orden Financiero
•	Soporte para crear y editar categorías y subcategorías personalizadas.
•	Visualización de estadísticas por categoría y subcategoría (mensuales, anuales).
•	Gráficas de pastel y barras para analizar los gastos.
________________________________________
🗓 Recordatorios de Pagos
•	Permitir agregar compromisos financieros recurrentes o puntuales, como pagos de servicios, tarjetas, arriendos.
•	Enviar notificaciones por correo electrónico 3 días antes de la fecha límite de pago.
•	Opcional: notificaciones push (si es app móvil o PWA).
________________________________________
📧 Sistema de Correos
•	Configuración de servidor SMTP para enviar correos electrónicos.
•	Plantillas HTML básicas para enviar recordatorios con el nombre del compromiso, monto, cuenta asociada y fecha límite.
________________________________________
📊 Panel de Control
•	Dashboard principal con resumen de:
o	Saldo por cuenta.
o	Ingresos vs. gastos del mes.
o	Alertas de pagos próximos.
________________________________________
⚙️ Tecnología sugerida (ajustable según stack del equipo):
•	Frontend: Bootstrap 5., JS, Jquery actualizado
•	Backend: PHP 8.3
•	Base de datos: MySQL.
•	Correo: integración con SMTP,
•	Autenticación: protocolo csrf y las contraseñas se almacenan utilizando password_hash() de PHP.
________________________________________
🧪 Extras sugeridos
•	Exportar reportes en PDF o Excel.
•	Modo oscuro/claro.
•	Backup automático de datos (local/download). (descargar copia de la base de datos de la aplicación completa con la posibilidad de tener un modulo que me permita montar la base de datos en el sistema (importar backup))
