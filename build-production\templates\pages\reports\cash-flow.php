<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Flujo de Efectivo</h2>
    <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-download"></i> Exportar
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="/reports/export/cash-flow?format=json&year=<?= $year ?>&month=<?= $month ?>">
                <i class="fas fa-file-code"></i> JSON
            </a></li>
            <li><a class="dropdown-item" href="/reports/export/cash-flow?format=csv&year=<?= $year ?>&month=<?= $month ?>">
                <i class="fas fa-file-csv"></i> CSV
            </a></li>
        </ul>
    </div>
</div>

<!-- Navegación de Reportes -->
<div class="row mb-4">
    <div class="col-12">
        <div class="nav nav-pills justify-content-center" role="tablist">
            <a class="nav-link" href="/reports">
                <i class="fas fa-chart-pie"></i> Resumen General
            </a>
            <a class="nav-link active" href="/reports/cash-flow">
                <i class="fas fa-exchange-alt"></i> Flujo de Efectivo
            </a>
            <a class="nav-link" href="/reports/category-expenses">
                <i class="fas fa-tags"></i> Por Categorías
            </a>
            <a class="nav-link" href="/reports/trends">
                <i class="fas fa-chart-line"></i> Tendencias
            </a>
            <a class="nav-link" href="/reports/accounts">
                <i class="fas fa-university"></i> Cuentas
            </a>
            <a class="nav-link" href="/reports/compare-periods">
                <i class="fas fa-balance-scale"></i> Comparar Períodos
            </a>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter"></i> Filtros</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="/reports/cash-flow" class="row g-3">
            <div class="col-md-4">
                <label for="year" class="form-label">Año</label>
                <select class="form-select" id="year" name="year">
                    <?php foreach ($available_years as $availableYear): ?>
                        <option value="<?= $availableYear ?>" <?= $availableYear === $year ? 'selected' : '' ?>>
                            <?= $availableYear ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="month" class="form-label">Mes (opcional para detalle diario)</label>
                <select class="form-select" id="month" name="month">
                    <option value="">Todos los meses</option>
                    <?php foreach ($months as $monthNum => $monthName): ?>
                        <option value="<?= $monthNum ?>" <?= $monthNum === $month ? 'selected' : '' ?>>
                            <?= $monthName ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> Generar Reporte
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<?php if ($cash_flow_report): ?>
    <?php if ($month): ?>
        <!-- Reporte Mensual Detallado (Diario) -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day"></i> 
                    Flujo Diario - <?= $months[$month] ?> <?= $year ?>
                </h5>
            </div>
            <div class="card-body">
                <!-- Gráfico de flujo diario -->
                <div class="mb-4">
                    <canvas id="dailyCashFlowChart" width="400" height="150"></canvas>
                </div>
                
                <!-- Resumen del mes -->
                <?php 
                $monthlyIncome = array_sum(array_column($cash_flow_report['data'], 'income'));
                $monthlyExpenses = array_sum(array_column($cash_flow_report['data'], 'expenses'));
                $monthlyNet = $monthlyIncome - $monthlyExpenses;
                $totalTransactions = array_sum(array_column($cash_flow_report['data'], 'transactions'));
                ?>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded bg-light">
                            <h4 class="text-success">+$ <?= number_format($monthlyIncome, 2) ?></h4>
                            <p class="mb-0 text-muted">Total Ingresos</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded bg-light">
                            <h4 class="text-danger">-$ <?= number_format($monthlyExpenses, 2) ?></h4>
                            <p class="mb-0 text-muted">Total Egresos</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded bg-light">
                            <h4 class="<?= $monthlyNet >= 0 ? 'text-success' : 'text-danger' ?>">
                                <?= $monthlyNet >= 0 ? '+' : '' ?>$ <?= number_format($monthlyNet, 2) ?>
                            </h4>
                            <p class="mb-0 text-muted">Balance Neto</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded bg-light">
                            <h4 class="text-info"><?= $totalTransactions ?></h4>
                            <p class="mb-0 text-muted">Transacciones</p>
                        </div>
                    </div>
                </div>
                
                <!-- Tabla de datos diarios -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Fecha</th>
                                <th>Día</th>
                                <th class="text-end">Ingresos</th>
                                <th class="text-end">Egresos</th>
                                <th class="text-end">Neto</th>
                                <th class="text-center">Transacciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cash_flow_report['data'] as $day): ?>
                                <tr class="<?= $day['net'] < 0 ? 'table-warning' : '' ?>">
                                    <td><?= date('d/m/Y', strtotime($day['date'])) ?></td>
                                    <td><?= $day['day_name'] ?></td>
                                    <td class="text-end text-success">
                                        <?= $day['income'] > 0 ? '+$ ' . number_format($day['income'], 2) : '-' ?>
                                    </td>
                                    <td class="text-end text-danger">
                                        <?= $day['expenses'] > 0 ? '-$ ' . number_format($day['expenses'], 2) : '-' ?>
                                    </td>
                                    <td class="text-end <?= $day['net'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                        <strong>
                                            <?= $day['net'] >= 0 ? '+' : '' ?>$ <?= number_format($day['net'], 2) ?>
                                        </strong>
                                    </td>
                                    <td class="text-center">
                                        <?= $day['transactions'] > 0 ? $day['transactions'] : '-' ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Reporte Anual (Por Meses) -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt"></i> 
                    Flujo Mensual - Año <?= $year ?>
                </h5>
            </div>
            <div class="card-body">
                <!-- Gráfico de flujo mensual -->
                <div class="mb-4">
                    <canvas id="monthlyCashFlowChart" width="400" height="150"></canvas>
                </div>
                
                <!-- Resumen del año -->
                <?php 
                $yearlyIncome = array_sum(array_column($cash_flow_report['data'], 'income'));
                $yearlyExpenses = array_sum(array_column($cash_flow_report['data'], 'expenses'));
                $yearlyNet = $yearlyIncome - $yearlyExpenses;
                $totalTransactions = array_sum(array_column($cash_flow_report['data'], 'transactions'));
                ?>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded bg-light">
                            <h4 class="text-success">+$ <?= number_format($yearlyIncome, 2) ?></h4>
                            <p class="mb-0 text-muted">Total Ingresos</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded bg-light">
                            <h4 class="text-danger">-$ <?= number_format($yearlyExpenses, 2) ?></h4>
                            <p class="mb-0 text-muted">Total Egresos</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded bg-light">
                            <h4 class="<?= $yearlyNet >= 0 ? 'text-success' : 'text-danger' ?>">
                                <?= $yearlyNet >= 0 ? '+' : '' ?>$ <?= number_format($yearlyNet, 2) ?>
                            </h4>
                            <p class="mb-0 text-muted">Balance Neto</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded bg-light">
                            <h4 class="text-info"><?= $totalTransactions ?></h4>
                            <p class="mb-0 text-muted">Transacciones</p>
                        </div>
                    </div>
                </div>
                
                <!-- Tabla de datos mensuales -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Mes</th>
                                <th class="text-end">Ingresos</th>
                                <th class="text-end">Egresos</th>
                                <th class="text-end">Neto</th>
                                <th class="text-center">Transacciones</th>
                                <th class="text-center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cash_flow_report['data'] as $monthData): ?>
                                <tr class="<?= $monthData['net'] < 0 ? 'table-warning' : '' ?>">
                                    <td>
                                        <strong><?= $monthData['month_name'] ?> <?= $monthData['year'] ?></strong>
                                    </td>
                                    <td class="text-end text-success">
                                        <?= $monthData['income'] > 0 ? '+$ ' . number_format($monthData['income'], 2) : '-' ?>
                                    </td>
                                    <td class="text-end text-danger">
                                        <?= $monthData['expenses'] > 0 ? '-$ ' . number_format($monthData['expenses'], 2) : '-' ?>
                                    </td>
                                    <td class="text-end <?= $monthData['net'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                        <strong>
                                            <?= $monthData['net'] >= 0 ? '+' : '' ?>$ <?= number_format($monthData['net'], 2) ?>
                                        </strong>
                                    </td>
                                    <td class="text-center">
                                        <?= $monthData['transactions'] > 0 ? $monthData['transactions'] : '-' ?>
                                    </td>
                                    <td class="text-center">
                                        <a href="/reports/cash-flow?year=<?= $monthData['year'] ?>&month=<?= $monthData['month'] ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> Detalle
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php else: ?>
    <div class="text-center py-5">
        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No hay datos disponibles</h4>
        <p class="text-muted">Selecciona un período para generar el reporte de flujo de efectivo</p>
    </div>
<?php endif; ?>

<!-- Scripts para gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if ($cash_flow_report): ?>
        <?php if ($month): ?>
            loadDailyCashFlowChart();
        <?php else: ?>
            loadMonthlyCashFlowChart();
        <?php endif; ?>
    <?php endif; ?>
});

<?php if ($cash_flow_report && $month): ?>
function loadDailyCashFlowChart() {
    const data = <?= json_encode($cash_flow_report['data']) ?>;
    
    const ctx = document.getElementById('dailyCashFlowChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.day + '/' + <?= $month ?>),
            datasets: [{
                label: 'Ingresos',
                data: data.map(item => item.income),
                borderColor: 'rgba(40, 167, 69, 1)',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                fill: false
            }, {
                label: 'Egresos',
                data: data.map(item => item.expenses),
                borderColor: 'rgba(220, 53, 69, 1)',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                borderWidth: 2,
                fill: false
            }, {
                label: 'Neto',
                data: data.map(item => item.net),
                borderColor: 'rgba(0, 123, 255, 1)',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}
<?php endif; ?>

<?php if ($cash_flow_report && !$month): ?>
function loadMonthlyCashFlowChart() {
    const data = <?= json_encode($cash_flow_report['data']) ?>;
    
    const ctx = document.getElementById('monthlyCashFlowChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.month_name),
            datasets: [{
                label: 'Ingresos',
                data: data.map(item => item.income),
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1
            }, {
                label: 'Egresos',
                data: data.map(item => item.expenses),
                backgroundColor: 'rgba(220, 53, 69, 0.8)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}
<?php endif; ?>
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
