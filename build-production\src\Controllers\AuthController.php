<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Services\AuthService;
use ControlGastos\Core\Session;

/**
 * Controlador de autenticación
 * Maneja todas las rutas relacionadas con autenticación
 */
class AuthController
{
    private Container $container;
    private AuthService $authService;
    private Session $session;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->authService = $container->get('authService');
        $this->session = $container->get('session');
    }

    /**
     * Mostrar formulario de login
     */
    public function showLogin(): string
    {
        // Si ya está autenticado, redirigir al dashboard
        if ($this->session->has('user_authenticated')) {
            header('Location: /dashboard');
            exit;
        }

        $data = [
            'title' => 'Iniciar Sesión',
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error'),
            'success' => $this->session->getFlash('success')
        ];

        return $this->render('auth/login', $data);
    }

    /**
     * Procesar login
     */
    public function login(): void
    {
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $rememberMe = isset($_POST['remember_me']);

        $result = $this->authService->login($email, $password, $rememberMe);

        if ($result['success']) {
            if (isset($result['requires_2fa']) && $result['requires_2fa']) {
                // Redirigir a 2FA
                header('Location: /auth/2fa');
                exit;
            }

            // Login exitoso, redirigir
            $intendedUrl = $this->session->get('intended_url', '/dashboard');
            $this->session->remove('intended_url');
            header('Location: ' . $intendedUrl);
            exit;
        }

        // Error en login
        $this->session->flash('error', $result['message']);
        header('Location: /auth/login');
        exit;
    }

    /**
     * Mostrar formulario de registro
     */
    public function showRegister(): string
    {
        // Si ya está autenticado, redirigir al dashboard
        if ($this->session->has('user_authenticated')) {
            header('Location: /dashboard');
            exit;
        }

        $data = [
            'title' => 'Registrarse',
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error'),
            'success' => $this->session->getFlash('success')
        ];

        return $this->render('auth/register', $data);
    }

    /**
     * Procesar registro
     */
    public function register(): void
    {
        $data = [
            'email' => $_POST['email'] ?? '',
            'password' => $_POST['password'] ?? '',
            'password_confirmation' => $_POST['password_confirmation'] ?? '',
            'first_name' => $_POST['first_name'] ?? '',
            'last_name' => $_POST['last_name'] ?? '',
            'phone' => $_POST['phone'] ?? ''
        ];

        // Validar confirmación de contraseña
        if ($data['password'] !== $data['password_confirmation']) {
            $this->session->flash('error', 'Las contraseñas no coinciden');
            header('Location: /auth/register');
            exit;
        }

        $result = $this->authService->register($data);

        if ($result['success']) {
            $this->session->flash('success', 'Registro exitoso. Revise su email para verificar su cuenta.');
            
            // Enviar email de verificación
            $this->sendVerificationEmail($result['user'], $result['verification_token']);
            
            header('Location: /auth/login');
            exit;
        }

        $this->session->flash('error', $result['message']);
        header('Location: /auth/register');
        exit;
    }

    /**
     * Mostrar formulario de 2FA
     */
    public function show2FA(): string
    {
        if (!$this->session->has('2fa_user_id')) {
            header('Location: /auth/login');
            exit;
        }

        $data = [
            'title' => 'Autenticación de Dos Factores',
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('auth/2fa', $data);
    }

    /**
     * Procesar código 2FA
     */
    public function verify2FA(): void
    {
        $code = $_POST['code'] ?? '';

        $result = $this->authService->verify2FA($code);

        if ($result['success']) {
            $intendedUrl = $this->session->get('intended_url', '/dashboard');
            $this->session->remove('intended_url');
            header('Location: ' . $intendedUrl);
            exit;
        }

        $this->session->flash('error', $result['message']);
        header('Location: /auth/2fa');
        exit;
    }

    /**
     * Mostrar formulario de recuperación de contraseña
     */
    public function showForgotPassword(): string
    {
        $data = [
            'title' => 'Recuperar Contraseña',
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error'),
            'success' => $this->session->getFlash('success')
        ];

        return $this->render('auth/forgot-password', $data);
    }

    /**
     * Procesar solicitud de recuperación
     */
    public function forgotPassword(): void
    {
        $email = $_POST['email'] ?? '';

        $result = $this->authService->requestPasswordReset($email);

        if ($result['success']) {
            // Enviar email de reset
            if (isset($result['reset_token'])) {
                $this->sendPasswordResetEmail($email, $result['reset_token']);
            }
        }

        // Siempre mostrar el mismo mensaje por seguridad
        $this->session->flash('success', $result['message']);
        header('Location: /auth/forgot-password');
        exit;
    }

    /**
     * Mostrar formulario de reset de contraseña
     */
    public function showResetPassword(): string
    {
        $token = $_GET['token'] ?? '';

        if (empty($token)) {
            $this->session->flash('error', 'Token de reset requerido');
            header('Location: /auth/forgot-password');
            exit;
        }

        $data = [
            'title' => 'Restablecer Contraseña',
            'csrf_token' => $this->session->getCsrfToken(),
            'token' => $token,
            'error' => $this->session->getFlash('error'),
            'success' => $this->session->getFlash('success')
        ];

        return $this->render('auth/reset-password', $data);
    }

    /**
     * Procesar reset de contraseña
     */
    public function resetPassword(): void
    {
        $token = $_POST['token'] ?? '';
        $password = $_POST['password'] ?? '';
        $passwordConfirmation = $_POST['password_confirmation'] ?? '';

        if ($password !== $passwordConfirmation) {
            $this->session->flash('error', 'Las contraseñas no coinciden');
            header('Location: /auth/reset-password?token=' . urlencode($token));
            exit;
        }

        $result = $this->authService->resetPassword($token, $password);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
            header('Location: /auth/login');
            exit;
        }

        $this->session->flash('error', $result['message']);
        header('Location: /auth/reset-password?token=' . urlencode($token));
        exit;
    }

    /**
     * Verificar email
     */
    public function verifyEmail(): void
    {
        $token = $_GET['token'] ?? '';

        if (empty($token)) {
            $this->session->flash('error', 'Token de verificación requerido');
            header('Location: /auth/login');
            exit;
        }

        $result = $this->authService->verifyEmail($token);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /auth/login');
        exit;
    }

    /**
     * Reenviar verificación de email
     */
    public function resendVerification(): void
    {
        $email = $_POST['email'] ?? '';

        // Implementar lógica de reenvío
        $this->session->flash('success', 'Si el email existe, se ha enviado un nuevo enlace de verificación');
        header('Location: /auth/login');
        exit;
    }

    /**
     * Cerrar sesión
     */
    public function logout(): void
    {
        $this->authService->logout();
        header('Location: /auth/login');
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        // Implementación básica de renderizado
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Enviar email de verificación
     */
    private function sendVerificationEmail(array $user, string $token): void
    {
        // Implementar envío de email
        // Se integrará con el servicio de mail
    }

    /**
     * Enviar email de reset de contraseña
     */
    private function sendPasswordResetEmail(string $email, string $token): void
    {
        // Implementar envío de email
        // Se integrará con el servicio de mail
    }
}
