<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Models\CreditCard;
use ControlGastos\Models\CreditCardTransaction;
use ControlGastos\Models\CreditCardPayment;
use ControlGastos\Core\Session;
use ControlGastos\Core\SimpleDatabase;
use PDO;

/**
 * Controlador para gestión de tarjetas de crédito
 */
class CreditCardController
{
    private PDO $db;
    private Session $session;
    private CreditCard $creditCardModel;
    private CreditCardTransaction $transactionModel;
    private CreditCardPayment $paymentModel;

    public function __construct(SimpleDatabase $database, Session $session)
    {
        $this->db = $database->getConnection();
        $this->session = $session;
        $this->creditCardModel = new CreditCard($this->db);
        $this->transactionModel = new CreditCardTransaction($this->db);
        $this->paymentModel = new CreditCardPayment($this->db);
    }
    
    /**
     * Mostrar dashboard de tarjetas de crédito
     */
    public function index(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }
        
        $cards = $this->creditCardModel->findByUserId($userId);
        $cardsSummary = [];
        
        foreach ($cards as $card) {
            $cardsSummary[] = $this->creditCardModel->getFinancialSummary($card['id']);
        }
        
        // Obtener transacciones recientes
        $recentTransactions = $this->transactionModel->findByUserId($userId, 10);
        
        // Obtener pagos recientes
        $recentPayments = $this->paymentModel->findByUserId($userId, 5);
        
        $this->render('credit_cards/index', [
            'cards' => $cardsSummary,
            'recent_transactions' => $recentTransactions,
            'recent_payments' => $recentPayments,
            'title' => 'Mis Tarjetas de Crédito'
        ]);
    }
    
    /**
     * Mostrar formulario para crear nueva tarjeta
     */
    public function create(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        // Obtener bancos activos del usuario
        $sql = "SELECT id, bank_name, bank_code, color FROM banks WHERE user_id = ? AND is_active = 1 ORDER BY bank_name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $banks = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $this->render('credit_cards/create', [
            'title' => 'Nueva Tarjeta de Crédito',
            'banks' => $banks
        ]);
    }
    
    /**
     * Procesar creación de nueva tarjeta
     */
    public function store(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /controlGastos/public/?route=credit-cards/create');
            exit;
        }
        
        $data = [
            'user_id' => $userId,
            'card_name' => trim($_POST['card_name'] ?? ''),
            'bank_id' => (int) ($_POST['bank_id'] ?? 0),
            'card_type' => trim($_POST['card_type'] ?? 'visa'),
            'card_number_last4' => trim($_POST['card_number_last4'] ?? ''),
            'credit_limit' => (float) ($_POST['credit_limit'] ?? 0),
            'management_fee' => (float) ($_POST['management_fee'] ?? 0),
            'expiry_date' => $_POST['expiry_date'] ?? '',
            'cut_off_day' => (int) ($_POST['cut_off_day'] ?? 1),
            'payment_due_days' => (int) ($_POST['payment_due_days'] ?? 20),
            'cvv' => trim($_POST['cvv'] ?? ''),
            'description' => trim($_POST['description'] ?? '')
        ];

        // Validaciones
        $errors = [];
        if (empty($data['card_name'])) {
            $errors[] = 'El nombre de la tarjeta es obligatorio';
        }
        if (empty($data['bank_id'])) {
            $errors[] = 'Debe seleccionar un banco emisor';
        }
        if ($data['credit_limit'] <= 0) {
            $errors[] = 'El cupo debe ser mayor a 0';
        }
        if (empty($data['expiry_date'])) {
            $errors[] = 'La fecha de expiración es obligatoria';
        }
        
        if (!empty($errors)) {
            $this->session->flash('error', implode('<br>', $errors));
            header('Location: /controlGastos/public/?route=credit-cards/create');
            exit;
        }

        try {
            $cardId = $this->creditCardModel->create($data);
            $this->session->flash('success', 'Tarjeta de crédito creada exitosamente');
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al crear la tarjeta: ' . $e->getMessage());
            header('Location: /controlGastos/public/?route=credit-cards/create');
            exit;
        }
    }
    
    /**
     * Mostrar detalles de una tarjeta
     */
    public function show(): void
    {
        $cardId = (int) ($_GET['id'] ?? 0);
        $userId = $this->session->get('user_id');
        
        $card = $this->creditCardModel->findById($cardId);
        if (!$card || $card['user_id'] != $userId) {
            $this->session->flash('error', 'Tarjeta no encontrada');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        }
        
        $summary = $this->creditCardModel->getFinancialSummary($cardId);
        $transactions = $this->transactionModel->findByCardId($cardId, 20);
        $payments = $this->paymentModel->findByCardId($cardId, 10);

        // Obtener categorías del usuario
        $stmt = $this->db->prepare("SELECT * FROM categories WHERE user_id = ? AND is_active = 1 ORDER BY name");
        $stmt->execute([$userId]);
        $categories = $stmt->fetchAll();
        
        $this->render('credit_cards/show', [
            'card' => $summary,
            'transactions' => $transactions,
            'payments' => $payments,
            'categories' => $categories,
            'title' => 'Detalles de ' . $card['card_name']
        ]);
    }
    
    /**
     * Mostrar formulario para editar tarjeta
     */
    public function edit(): void
    {
        $cardId = (int) ($_GET['id'] ?? 0);
        $userId = $this->session->get('user_id');
        
        $card = $this->creditCardModel->findById($cardId);
        if (!$card || $card['user_id'] != $userId) {
            $this->session->flash('error', 'Tarjeta no encontrada');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        }
        
        $this->render('credit_cards/edit', [
            'card' => $card,
            'title' => 'Editar ' . $card['card_name']
        ]);
    }
    
    /**
     * Procesar actualización de tarjeta
     */
    public function update(): void
    {
        $cardId = (int) ($_POST['id'] ?? 0);
        $userId = $this->session->get('user_id');
        
        $card = $this->creditCardModel->findById($cardId);
        if (!$card || $card['user_id'] != $userId) {
            $this->session->flash('error', 'Tarjeta no encontrada');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header("Location: /controlGastos/public/?route=credit-cards/edit&id={$cardId}");
            exit;
        }
        
        $data = [
            'card_name' => trim($_POST['card_name'] ?? ''),
            'bank_id' => (int) ($_POST['bank_id'] ?? 0),
            'card_type' => trim($_POST['card_type'] ?? 'visa'),
            'card_number_last4' => trim($_POST['card_number_last4'] ?? ''),
            'credit_limit' => (float) ($_POST['credit_limit'] ?? 0),
            'management_fee' => (float) ($_POST['management_fee'] ?? 0),
            'expiry_date' => $_POST['expiry_date'] ?? '',
            'cut_off_day' => (int) ($_POST['cut_off_day'] ?? 1),
            'payment_due_days' => (int) ($_POST['payment_due_days'] ?? 20),
            'cvv' => trim($_POST['cvv'] ?? ''),
            'description' => trim($_POST['description'] ?? '')
        ];
        
        try {
            $this->creditCardModel->update($cardId, $data);
            $this->session->flash('success', 'Tarjeta actualizada exitosamente');
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al actualizar la tarjeta: ' . $e->getMessage());
            header("Location: /controlGastos/public/?route=credit-cards/edit&id={$cardId}");
            exit;
        }
    }
    
    /**
     * Eliminar tarjeta
     */
    public function delete(): void
    {
        $cardId = (int) ($_POST['id'] ?? 0);
        $userId = $this->session->get('user_id');
        
        $card = $this->creditCardModel->findById($cardId);
        if (!$card || $card['user_id'] != $userId) {
            $this->session->flash('error', 'Tarjeta no encontrada');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        }
        
        try {
            $this->creditCardModel->delete($cardId);
            $this->session->flash('success', 'Tarjeta eliminada exitosamente');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al eliminar la tarjeta: ' . $e->getMessage());
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        }
    }
    
    /**
     * Agregar nueva transacción
     */
    public function addTransaction(): void
    {
        $cardId = (int) ($_POST['credit_card_id'] ?? 0);
        $userId = $this->session->get('user_id');

        $card = $this->creditCardModel->findById($cardId);
        if (!$card || $card['user_id'] != $userId) {
            $this->session->flash('error', 'Tarjeta no encontrada');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        }

        $data = [
            'credit_card_id' => $cardId,
            'user_id' => $userId,
            'transaction_type' => $_POST['transaction_type'] ?? 'purchase',
            'amount' => (float) ($_POST['amount'] ?? 0),
            'description' => trim($_POST['description'] ?? ''),
            'merchant' => trim($_POST['merchant'] ?? ''),
            'category_id' => !empty($_POST['category_id']) ? (int) $_POST['category_id'] : null,
            'transaction_date' => $_POST['transaction_date'] ?? date('Y-m-d'),
            'installments' => (int) ($_POST['installments'] ?? 1),
            'notes' => trim($_POST['notes'] ?? '')
        ];

        try {
            if ($data['installments'] > 1) {
                $this->transactionModel->createInstallmentTransactions($data);
                $this->session->flash('success', "Transacción en {$data['installments']} cuotas creada exitosamente");
            } else {
                $this->transactionModel->create($data);
                $this->session->flash('success', 'Transacción agregada exitosamente');
            }

            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al agregar transacción: ' . $e->getMessage());
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        }
    }

    /**
     * Agregar nuevo pago
     */
    public function addPayment(): void
    {
        $cardId = (int) ($_POST['credit_card_id'] ?? 0);
        $userId = $this->session->get('user_id');

        $card = $this->creditCardModel->findById($cardId);
        if (!$card || $card['user_id'] != $userId) {
            $this->session->flash('error', 'Tarjeta no encontrada');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        }

        $data = [
            'credit_card_id' => $cardId,
            'user_id' => $userId,
            'payment_type' => $_POST['payment_type'] ?? 'partial',
            'amount' => (float) ($_POST['amount'] ?? 0),
            'payment_date' => $_POST['payment_date'] ?? date('Y-m-d'),
            'payment_method' => $_POST['payment_method'] ?? 'bank_transfer',
            'reference_number' => trim($_POST['reference_number'] ?? ''),
            'notes' => trim($_POST['notes'] ?? '')
        ];

        try {
            $this->paymentModel->create($data);
            $this->session->flash('success', 'Pago registrado exitosamente');
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al registrar pago: ' . $e->getMessage());
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        }
    }

    /**
     * Cambiar estado de tarjeta
     */
    public function changeStatus(): void
    {
        $cardId = (int) ($_POST['card_id'] ?? 0);
        $newStatus = trim($_POST['status'] ?? '');
        $reason = trim($_POST['reason'] ?? '');
        $userId = $this->session->get('user_id');

        $card = $this->creditCardModel->findById($cardId);
        if (!$card || $card['user_id'] != $userId) {
            $this->session->flash('error', 'Tarjeta no encontrada');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        }

        $validStatuses = ['active', 'blocked', 'cancelled'];
        if (!in_array($newStatus, $validStatuses)) {
            $this->session->flash('error', 'Estado no válido');
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        }

        try {
            $this->creditCardModel->changeStatus($cardId, $newStatus, $reason);

            $statusMessages = [
                'active' => 'Tarjeta reactivada exitosamente',
                'blocked' => 'Tarjeta bloqueada exitosamente',
                'cancelled' => 'Tarjeta cancelada exitosamente'
            ];

            $this->session->flash('success', $statusMessages[$newStatus]);
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al cambiar el estado: ' . $e->getMessage());
            header("Location: /controlGastos/public/?route=credit-cards/show&id={$cardId}");
            exit;
        }
    }

    /**
     * Verificar tarjetas vencidas
     */
    public function checkExpired(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        try {
            $updatedCount = $this->creditCardModel->checkAndUpdateExpiredCards();

            if ($updatedCount > 0) {
                $this->session->flash('success', "Se actualizaron {$updatedCount} tarjeta(s) vencida(s) automáticamente.");
            } else {
                $this->session->flash('info', 'No se encontraron tarjetas vencidas.');
            }
        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al verificar tarjetas vencidas: ' . $e->getMessage());
        }

        header('Location: /controlGastos/public/?route=credit-cards');
        exit;
    }

    /**
     * Mostrar reportes de tarjeta
     */
    public function reports(): void
    {
        $cardId = (int) ($_GET['id'] ?? 0);
        $userId = $this->session->get('user_id');

        $card = $this->creditCardModel->findById($cardId);
        if (!$card || $card['user_id'] != $userId) {
            $this->session->flash('error', 'Tarjeta no encontrada');
            header('Location: /controlGastos/public/?route=credit-cards');
            exit;
        }

        $year = (int) ($_GET['year'] ?? date('Y'));
        $month = (int) ($_GET['month'] ?? date('n'));

        $summary = $this->creditCardModel->getFinancialSummary($cardId);
        $monthlyStats = $this->transactionModel->getMonthlyStats($cardId, $year, $month);
        $paymentStats = $this->paymentModel->getMonthlyStats($cardId, $year, $month);
        $spendingByCategory = $this->creditCardModel->getSpendingByCategory($cardId, "{$year}-{$month}-01", date('Y-m-t', mktime(0, 0, 0, $month, 1, $year)));

        $this->render('credit_cards/reports', [
            'card' => $summary,
            'monthly_stats' => $monthlyStats,
            'payment_stats' => $paymentStats,
            'spending_by_category' => $spendingByCategory,
            'year' => $year,
            'month' => $month,
            'title' => 'Reportes de ' . $card['card_name']
        ]);
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): void
    {
        // Extraer datos para la vista
        extract($data);

        // Obtener información del usuario
        $userId = $this->session->get('user_id');
        $user = null;
        if ($userId) {
            $stmt = $this->db->prepare("SELECT first_name, last_name, email FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
        }

        // Renderizar la vista
        ob_start();
        ?>
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><?= $title ?? 'Tarjetas de Crédito' ?> - Control de Gastos</title>
            <link href="/controlGastos/public/assets/css/bootstrap.min.css" rel="stylesheet">
            <link href="/controlGastos/public/assets/css/fontawesome.min.css" rel="stylesheet">
            <link href="/controlGastos/public/assets/css/custom.css" rel="stylesheet">
        </head>
        <body>
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                <div class="container-fluid">
                    <a class="navbar-brand" href="/controlGastos/public/">
                        <i class="fas fa-chart-line me-2"></i>Control de Gastos
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="/controlGastos/public/">
                                    <i class="fas fa-home me-1"></i>Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link active" href="/controlGastos/public/?route=credit-cards">
                                    <i class="fas fa-credit-card me-1"></i>Tarjetas de Crédito
                                </a>
                            </li>
                        </ul>
                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i><?= htmlspecialchars(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '') ?: 'Usuario') ?>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/controlGastos/public/?route=auth/logout">
                                        <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                                    </a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Contenido principal -->
            <main class="py-4">
                <?php
                $viewPath = __DIR__ . "/../../templates/pages/{$view}.php";
                if (file_exists($viewPath)) {
                    include $viewPath;
                } else {
                    echo "<div class='container'><div class='alert alert-danger'>Vista no encontrada: {$view}</div></div>";
                }
                ?>
            </main>

            <!-- Scripts -->
            <script src="/controlGastos/public/assets/js/bootstrap.bundle.min.js"></script>
        </body>
        </html>
        <?php
        echo ob_get_clean();
    }
}
