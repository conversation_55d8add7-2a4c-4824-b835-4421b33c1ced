<?php

declare(strict_types=1);

namespace ControlGastos\Models;

/**
 * Modelo de Subcategoría
 * Representa una subcategoría para clasificación detallada de transacciones
 */
class Subcategory
{
    private ?int $id = null;
    private int $categoryId;
    private string $name;
    private ?string $description = null;
    private bool $isActive = true;
    private \DateTime $createdAt;
    private \DateTime $updatedAt;
    private ?Category $category = null;

    public function __construct(array $data = [])
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    /**
     * Llenar modelo con datos
     */
    public function fill(array $data): void
    {
        if (isset($data['id'])) {
            $this->id = (int) $data['id'];
        }
        
        if (isset($data['category_id'])) {
            $this->categoryId = (int) $data['category_id'];
        }
        
        if (isset($data['name'])) {
            $this->name = trim($data['name']);
        }
        
        if (isset($data['description'])) {
            $this->description = $data['description'] ? trim($data['description']) : null;
        }
        
        if (isset($data['is_active'])) {
            $this->isActive = (bool) $data['is_active'];
        }
        
        if (isset($data['created_at'])) {
            $this->createdAt = new \DateTime($data['created_at']);
        }
        
        if (isset($data['updated_at'])) {
            $this->updatedAt = new \DateTime($data['updated_at']);
        }
    }

    /**
     * Convertir a array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'category_id' => $this->categoryId,
            'name' => $this->name,
            'description' => $this->description,
            'is_active' => $this->isActive,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Convertir a array con información adicional
     */
    public function toDetailedArray(): array
    {
        $array = array_merge($this->toArray(), [
            'status_label' => $this->isActive ? 'Activa' : 'Inactiva'
        ]);

        if ($this->category) {
            $array['category'] = [
                'id' => $this->category->getId(),
                'name' => $this->category->getName(),
                'color' => $this->category->getColor(),
                'icon' => $this->category->getIcon()
            ];
        }

        return $array;
    }

    /**
     * Validar datos de la subcategoría
     */
    public function validate(): array
    {
        $errors = [];

        // Validar nombre
        if (empty($this->name)) {
            $errors['name'] = 'El nombre de la subcategoría es requerido';
        } elseif (strlen($this->name) < 2) {
            $errors['name'] = 'El nombre debe tener al menos 2 caracteres';
        } elseif (strlen($this->name) > 100) {
            $errors['name'] = 'El nombre no puede exceder 100 caracteres';
        }

        // Validar descripción
        if ($this->description && strlen($this->description) > 500) {
            $errors['description'] = 'La descripción no puede exceder 500 caracteres';
        }

        // Validar category_id
        if (empty($this->categoryId)) {
            $errors['category_id'] = 'El ID de categoría es requerido';
        }

        return $errors;
    }

    /**
     * Verificar si la subcategoría es válida
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }

    /**
     * Activar subcategoría
     */
    public function activate(): void
    {
        $this->isActive = true;
        $this->updatedAt = new \DateTime();
    }

    /**
     * Desactivar subcategoría
     */
    public function deactivate(): void
    {
        $this->isActive = false;
        $this->updatedAt = new \DateTime();
    }

    /**
     * Obtener nombre completo (categoría - subcategoría)
     */
    public function getFullName(): string
    {
        if ($this->category) {
            return $this->category->getName() . ' - ' . $this->name;
        }
        return $this->name;
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getCategoryId(): int { return $this->categoryId; }
    public function getName(): string { return $this->name; }
    public function getDescription(): ?string { return $this->description; }
    public function isActive(): bool { return $this->isActive; }
    public function getCreatedAt(): \DateTime { return $this->createdAt; }
    public function getUpdatedAt(): \DateTime { return $this->updatedAt; }
    public function getCategory(): ?Category { return $this->category; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setCategoryId(int $categoryId): void { $this->categoryId = $categoryId; }
    public function setName(string $name): void { $this->name = trim($name); $this->updatedAt = new \DateTime(); }
    public function setDescription(?string $description): void { $this->description = $description ? trim($description) : null; $this->updatedAt = new \DateTime(); }
    public function setIsActive(bool $isActive): void { $this->isActive = $isActive; $this->updatedAt = new \DateTime(); }
    public function setUpdatedAt(\DateTime $updatedAt): void { $this->updatedAt = $updatedAt; }
    public function setCategory(?Category $category): void { $this->category = $category; }
}
