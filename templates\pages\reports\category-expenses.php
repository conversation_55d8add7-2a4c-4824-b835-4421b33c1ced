<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Gastos por Categoría</h2>
    <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-download"></i> Exportar
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="/reports/export/category-report?format=json&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>">
                <i class="fas fa-file-code"></i> JSON
            </a></li>
            <li><a class="dropdown-item" href="/reports/export/category-report?format=csv&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>">
                <i class="fas fa-file-csv"></i> CSV
            </a></li>
        </ul>
    </div>
</div>

<!-- Navegación de Reportes -->
<div class="row mb-4">
    <div class="col-12">
        <div class="nav nav-pills justify-content-center" role="tablist">
            <a class="nav-link" href="/reports">
                <i class="fas fa-chart-pie"></i> Resumen General
            </a>
            <a class="nav-link" href="/reports/cash-flow">
                <i class="fas fa-exchange-alt"></i> Flujo de Efectivo
            </a>
            <a class="nav-link active" href="/reports/category-expenses">
                <i class="fas fa-tags"></i> Por Categorías
            </a>
            <a class="nav-link" href="/reports/trends">
                <i class="fas fa-chart-line"></i> Tendencias
            </a>
            <a class="nav-link" href="/reports/accounts">
                <i class="fas fa-university"></i> Cuentas
            </a>
            <a class="nav-link" href="/reports/compare-periods">
                <i class="fas fa-balance-scale"></i> Comparar Períodos
            </a>
        </div>
    </div>
</div>

<!-- Filtros de Período -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-calendar"></i> Período de Análisis</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="/reports/category-expenses" class="row g-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">Fecha Inicio</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">Fecha Fin</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-chart-pie"></i> Generar Reporte
                    </button>
                    <a href="/reports/category-expenses" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh"></i> Limpiar
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<?php if ($category_report): ?>
    <!-- Resumen General -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> Resumen del Período
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-danger">$ <?= number_format($category_report['total_expenses'], 2) ?></h4>
                            <p class="mb-0 text-muted">Total Gastado</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info"><?= count($category_report['categories']) ?></h4>
                            <p class="mb-0 text-muted">Categorías</p>
                        </div>
                    </div>
                    <hr>
                    <p class="text-muted mb-0">
                        <strong>Período:</strong> 
                        <?= date('d/m/Y', strtotime($category_report['period']['start_date'])) ?> - 
                        <?= date('d/m/Y', strtotime($category_report['period']['end_date'])) ?>
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i> Distribución de Gastos
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="categoryPieChart" width="300" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($category_report['categories'])): ?>
        <!-- Análisis Detallado por Categorías -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Análisis Detallado por Categorías
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Categoría</th>
                                <th class="text-end">Monto</th>
                                <th class="text-center">Porcentaje</th>
                                <th class="text-center">Transacciones</th>
                                <th>Distribución</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($category_report['categories'] as $index => $category): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <span class="badge fs-6" style="background-color: <?= htmlspecialchars($category['category_color'] ?? '#007bff') ?>;">
                                                    #<?= $index + 1 ?>
                                                </span>
                                            </div>
                                            <div>
                                                <i class="<?= htmlspecialchars($category['category_icon'] ?? 'fas fa-folder') ?> me-2" 
                                                   style="color: <?= htmlspecialchars($category['category_color'] ?? '#007bff') ?>;"></i>
                                                <strong><?= htmlspecialchars($category['category_name']) ?></strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-end">
                                        <strong class="text-danger"><?= $category['amount_formatted'] ?></strong>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary fs-6">
                                            <?= number_format($category['percentage'], 1) ?>%
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">
                                            <?= $category['transaction_count'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 25px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: <?= $category['percentage'] ?>%; background-color: <?= htmlspecialchars($category['category_color'] ?? '#007bff') ?>;"
                                                 aria-valuenow="<?= $category['percentage'] ?>" aria-valuemin="0" aria-valuemax="100">
                                                <?= number_format($category['percentage'], 1) ?>%
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            Promedio por transacción: $ <?= number_format($category['amount'] / max(1, $category['transaction_count']), 2) ?>
                                        </small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Top 5 Categorías -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy"></i> Top 5 Categorías con Mayor Gasto
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach (array_slice($category_report['categories'], 0, 5) as $index => $category): ?>
                                <div class="col-md-2 mb-3">
                                    <div class="card border-0 text-center" style="background-color: <?= htmlspecialchars($category['category_color'] ?? '#007bff') ?>20;">
                                        <div class="card-body">
                                            <div class="mb-2">
                                                <i class="<?= htmlspecialchars($category['category_icon'] ?? 'fas fa-folder') ?> fa-2x" 
                                                   style="color: <?= htmlspecialchars($category['category_color'] ?? '#007bff') ?>;"></i>
                                            </div>
                                            <h6 class="card-title"><?= htmlspecialchars($category['category_name']) ?></h6>
                                            <h5 class="text-danger"><?= $category['amount_formatted'] ?></h5>
                                            <small class="text-muted"><?= number_format($category['percentage'], 1) ?>% del total</small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráfico de Barras -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Comparación Visual de Gastos
                </h5>
            </div>
            <div class="card-body">
                <canvas id="categoryBarChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Análisis y Recomendaciones -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb"></i> Análisis y Recomendaciones
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-exclamation-triangle text-warning"></i> Categorías de Mayor Impacto</h6>
                        <ul class="list-unstyled">
                            <?php foreach (array_slice($category_report['categories'], 0, 3) as $category): ?>
                                <li class="mb-2">
                                    <span class="badge me-2" style="background-color: <?= htmlspecialchars($category['category_color'] ?? '#007bff') ?>;">
                                        <?= number_format($category['percentage'], 1) ?>%
                                    </span>
                                    <strong><?= htmlspecialchars($category['category_name']) ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <?= $category['transaction_count'] ?> transacciones - 
                                        Promedio: $ <?= number_format($category['amount'] / max(1, $category['transaction_count']), 2) ?>
                                    </small>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-line text-info"></i> Estadísticas Generales</h6>
                        <ul class="list-unstyled">
                            <li><strong>Categoría más costosa:</strong> <?= $category_report['categories'][0]['category_name'] ?? 'N/A' ?></li>
                            <li><strong>Promedio por categoría:</strong> $ <?= number_format($category_report['total_expenses'] / max(1, count($category_report['categories'])), 2) ?></li>
                            <li><strong>Concentración del gasto:</strong> 
                                <?php 
                                $top3Percentage = array_sum(array_slice(array_column($category_report['categories'], 'percentage'), 0, 3));
                                echo number_format($top3Percentage, 1) . '% en top 3 categorías';
                                ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No hay gastos en este período</h4>
            <p class="text-muted">No se encontraron transacciones de egreso en el período seleccionado</p>
        </div>
    <?php endif; ?>
<?php else: ?>
    <div class="text-center py-5">
        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">Selecciona un período</h4>
        <p class="text-muted">Configura las fechas de inicio y fin para generar el reporte de gastos por categoría</p>
    </div>
<?php endif; ?>

<!-- Scripts para gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if ($category_report && !empty($category_report['categories'])): ?>
        loadCategoryPieChart();
        loadCategoryBarChart();
    <?php endif; ?>
});

<?php if ($category_report && !empty($category_report['categories'])): ?>
function loadCategoryPieChart() {
    const categories = <?= json_encode($category_report['categories']) ?>;
    
    const ctx = document.getElementById('categoryPieChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: categories.map(cat => cat.category_name),
            datasets: [{
                data: categories.map(cat => cat.amount),
                backgroundColor: categories.map(cat => cat.category_color || '#007bff'),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        padding: 10
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const percentage = categories[context.dataIndex].percentage;
                            return context.label + ': $' + context.parsed.toLocaleString() + ' (' + percentage.toFixed(1) + '%)';
                        }
                    }
                }
            }
        }
    });
}

function loadCategoryBarChart() {
    const categories = <?= json_encode($category_report['categories']) ?>;
    
    const ctx = document.getElementById('categoryBarChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: categories.map(cat => cat.category_name),
            datasets: [{
                label: 'Monto Gastado',
                data: categories.map(cat => cat.amount),
                backgroundColor: categories.map(cat => cat.category_color || '#007bff'),
                borderColor: categories.map(cat => cat.category_color || '#007bff'),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const category = categories[context.dataIndex];
                            return [
                                'Monto: $' + context.parsed.y.toLocaleString(),
                                'Porcentaje: ' + category.percentage.toFixed(1) + '%',
                                'Transacciones: ' + category.transaction_count
                            ];
                        }
                    }
                }
            }
        }
    });
}
<?php endif; ?>
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
