<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Repositories\TransactionRepository;
use ControlGastos\Repositories\AccountRepository;
use ControlGastos\Repositories\CategoryRepository;
use ControlGastos\Models\Transaction;
use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de gestión de transacciones
 * Maneja la lógica de negocio para transacciones financieras
 */
class TransactionService
{
    private TransactionRepository $transactionRepository;
    private AccountRepository $accountRepository;
    private CategoryRepository $categoryRepository;
    private Logger $logger;

    public function __construct(
        TransactionRepository $transactionRepository,
        AccountRepository $accountRepository,
        CategoryRepository $categoryRepository,
        Logger $logger
    ) {
        $this->transactionRepository = $transactionRepository;
        $this->accountRepository = $accountRepository;
        $this->categoryRepository = $categoryRepository;
        $this->logger = $logger;
    }

    /**
     * Crear nueva transacción
     */
    public function createTransaction(array $data, int $userId): array
    {
        try {
            // Verificar que la cuenta pertenezca al usuario
            $account = $this->accountRepository->findByIdAndUser($data['account_id'], $userId);
            if (!$account) {
                return [
                    'success' => false,
                    'message' => 'Cuenta no encontrada'
                ];
            }

            // Verificar que la categoría pertenezca al usuario
            $category = $this->categoryRepository->findByIdAndUser($data['category_id'], $userId);
            if (!$category) {
                return [
                    'success' => false,
                    'message' => 'Categoría no encontrada'
                ];
            }

            // Crear instancia de transacción
            $transaction = new Transaction();
            $transaction->setUserId($userId);
            $transaction->setAccountId($data['account_id']);
            $transaction->setCategoryId($data['category_id']);
            $transaction->setSubcategoryId($data['subcategory_id'] ?? null);
            $transaction->setType($data['type']);
            $transaction->setAmount((float) $data['amount']);
            $transaction->setDescription($data['description']);
            
            if (isset($data['transaction_date'])) {
                $transaction->setTransactionDate(new \DateTime($data['transaction_date']));
            }
            
            $transaction->setReference($data['reference'] ?? null);
            $transaction->setNotes($data['notes'] ?? null);
            
            if (isset($data['tags'])) {
                $tags = is_string($data['tags']) ? 
                    array_filter(array_map('trim', explode(',', $data['tags']))) : 
                    (array) $data['tags'];
                $transaction->setTags($tags);
            }

            // Configurar recurrencia si aplica
            if (!empty($data['is_recurring'])) {
                $transaction->setIsRecurring(true);
                $transaction->setRecurringType($data['recurring_type'] ?? null);
                $transaction->setRecurringInterval($data['recurring_interval'] ?? 1);
                
                if (!empty($data['recurring_end_date'])) {
                    $transaction->setRecurringEndDate(new \DateTime($data['recurring_end_date']));
                }
            }

            // Validar datos
            $errors = $transaction->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Verificar saldo suficiente para egresos
            if ($transaction->isExpense() && !$account->hasSufficientBalance($transaction->getAmount())) {
                return [
                    'success' => false,
                    'message' => 'Saldo insuficiente en la cuenta'
                ];
            }

            // Crear transacción y actualizar balance
            $transaction = $this->transactionRepository->create($transaction);

            // Actualizar balance de la cuenta
            $newBalance = $account->getBalance() + $transaction->getSignedAmount();
            $account->setBalance($newBalance);
            $this->accountRepository->update($account);

            $this->logger->info('Transacción creada', [
                'transaction_id' => $transaction->getId(),
                'user_id' => $userId,
                'type' => $transaction->getType(),
                'amount' => $transaction->getAmount(),
                'account_id' => $transaction->getAccountId()
            ]);

            return [
                'success' => true,
                'message' => 'Transacción creada exitosamente',
                'transaction' => $transaction->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al crear transacción: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Actualizar transacción existente
     */
    public function updateTransaction(int $transactionId, array $data, int $userId): array
    {
        try {
            // Buscar transacción
            $transaction = $this->transactionRepository->findByIdAndUser($transactionId, $userId);
            if (!$transaction) {
                return [
                    'success' => false,
                    'message' => 'Transacción no encontrada'
                ];
            }

            // Obtener cuenta actual para revertir balance
            $currentAccount = $this->accountRepository->findById($transaction->getAccountId());
            $oldSignedAmount = $transaction->getSignedAmount();

            // Verificar nueva cuenta si cambió
            $newAccount = null;
            if (isset($data['account_id']) && $data['account_id'] != $transaction->getAccountId()) {
                $newAccount = $this->accountRepository->findByIdAndUser($data['account_id'], $userId);
                if (!$newAccount) {
                    return [
                        'success' => false,
                        'message' => 'Nueva cuenta no encontrada'
                    ];
                }
            }

            // Verificar nueva categoría si cambió
            if (isset($data['category_id']) && $data['category_id'] != $transaction->getCategoryId()) {
                $category = $this->categoryRepository->findByIdAndUser($data['category_id'], $userId);
                if (!$category) {
                    return [
                        'success' => false,
                        'message' => 'Nueva categoría no encontrada'
                    ];
                }
            }

            // Actualizar datos
            if (isset($data['account_id'])) {
                $transaction->setAccountId($data['account_id']);
            }
            
            if (isset($data['category_id'])) {
                $transaction->setCategoryId($data['category_id']);
            }
            
            if (isset($data['subcategory_id'])) {
                $transaction->setSubcategoryId($data['subcategory_id']);
            }
            
            if (isset($data['type'])) {
                $transaction->setType($data['type']);
            }
            
            if (isset($data['amount'])) {
                $transaction->setAmount((float) $data['amount']);
            }
            
            if (isset($data['description'])) {
                $transaction->setDescription($data['description']);
            }
            
            if (isset($data['transaction_date'])) {
                $transaction->setTransactionDate(new \DateTime($data['transaction_date']));
            }
            
            if (isset($data['reference'])) {
                $transaction->setReference($data['reference']);
            }
            
            if (isset($data['notes'])) {
                $transaction->setNotes($data['notes']);
            }
            
            if (isset($data['tags'])) {
                $tags = is_string($data['tags']) ? 
                    array_filter(array_map('trim', explode(',', $data['tags']))) : 
                    (array) $data['tags'];
                $transaction->setTags($tags);
            }

            // Actualizar recurrencia
            if (isset($data['is_recurring'])) {
                $transaction->setIsRecurring((bool) $data['is_recurring']);
                
                if ($transaction->isRecurring()) {
                    $transaction->setRecurringType($data['recurring_type'] ?? null);
                    $transaction->setRecurringInterval($data['recurring_interval'] ?? 1);
                    
                    if (!empty($data['recurring_end_date'])) {
                        $transaction->setRecurringEndDate(new \DateTime($data['recurring_end_date']));
                    } else {
                        $transaction->setRecurringEndDate(null);
                    }
                } else {
                    $transaction->setRecurringType(null);
                    $transaction->setRecurringInterval(null);
                    $transaction->setRecurringEndDate(null);
                }
            }

            // Validar datos
            $errors = $transaction->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Revertir balance en cuenta anterior
            if ($currentAccount) {
                $currentAccount->setBalance($currentAccount->getBalance() - $oldSignedAmount);
                $this->accountRepository->update($currentAccount);
            }

            // Aplicar nuevo balance
            $targetAccount = $newAccount ?: $currentAccount;
            if ($targetAccount) {
                $targetAccount->setBalance($targetAccount->getBalance() + $transaction->getSignedAmount());
                $this->accountRepository->update($targetAccount);
            }

            // Actualizar transacción
            $this->transactionRepository->update($transaction);

            $this->logger->info('Transacción actualizada', [
                'transaction_id' => $transaction->getId(),
                'user_id' => $userId,
                'type' => $transaction->getType(),
                'amount' => $transaction->getAmount()
            ]);

            return [
                'success' => true,
                'message' => 'Transacción actualizada exitosamente',
                'transaction' => $transaction->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al actualizar transacción: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Eliminar transacción
     */
    public function deleteTransaction(int $transactionId, int $userId): array
    {
        try {
            // Buscar transacción
            $transaction = $this->transactionRepository->findByIdAndUser($transactionId, $userId);
            if (!$transaction) {
                return [
                    'success' => false,
                    'message' => 'Transacción no encontrada'
                ];
            }

            // Obtener cuenta para revertir balance
            $account = $this->accountRepository->findById($transaction->getAccountId());

            // Revertir balance de la cuenta
            if ($account) {
                $account->setBalance($account->getBalance() - $transaction->getSignedAmount());
                $this->accountRepository->update($account);
            }

            // Eliminar transacción
            $this->transactionRepository->delete($transaction->getId());

            $this->logger->info('Transacción eliminada', [
                'transaction_id' => $transactionId,
                'user_id' => $userId,
                'type' => $transaction->getType(),
                'amount' => $transaction->getAmount()
            ]);

            return [
                'success' => true,
                'message' => 'Transacción eliminada exitosamente'
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al eliminar transacción: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener transacción por ID
     */
    public function getTransaction(int $transactionId, int $userId): array
    {
        try {
            $transaction = $this->transactionRepository->findByIdAndUser($transactionId, $userId);
            
            if (!$transaction) {
                return [
                    'success' => false,
                    'message' => 'Transacción no encontrada'
                ];
            }

            return [
                'success' => true,
                'transaction' => $transaction->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener transacción: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener transacciones del usuario
     */
    public function getUserTransactions(int $userId, array $filters = []): array
    {
        try {
            $page = $filters['page'] ?? 1;
            $perPage = $filters['per_page'] ?? 20;
            
            $result = $this->transactionRepository->getPaginated($userId, $page, $perPage, $filters);
            
            return [
                'success' => true,
                'transactions' => $result['data'],
                'pagination' => [
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'per_page' => $result['per_page'],
                    'total_pages' => $result['total_pages']
                ]
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener transacciones: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Buscar transacciones
     */
    public function searchTransactions(int $userId, string $search, array $filters = []): array
    {
        try {
            $transactions = $this->transactionRepository->search($userId, $search, $filters);
            
            return [
                'success' => true,
                'transactions' => $transactions,
                'count' => count($transactions)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en búsqueda de transacciones: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener estadísticas de transacciones
     */
    public function getTransactionStats(int $userId, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        try {
            $stats = $this->transactionRepository->getStats($userId, $startDate, $endDate);
            $categoryStats = $this->transactionRepository->getCategorySummary($userId, $startDate, $endDate);
            
            return [
                'success' => true,
                'stats' => [
                    'general' => $stats,
                    'by_category' => $categoryStats
                ]
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener estadísticas: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Procesar transacciones recurrentes
     */
    public function processRecurringTransactions(): array
    {
        try {
            $recurringTransactions = $this->transactionRepository->getRecurringTransactionsDue();
            $processed = 0;
            $errors = [];

            foreach ($recurringTransactions as $transaction) {
                $nextDate = $transaction->getNextRecurringDate();
                
                if ($nextDate && $nextDate <= new \DateTime()) {
                    try {
                        // Crear nueva transacción basada en la recurrente
                        $newTransaction = clone $transaction;
                        $newTransaction->setId(null);
                        $newTransaction->setTransactionDate($nextDate);
                        $newTransaction->setCreatedAt(new \DateTime());
                        $newTransaction->setUpdatedAt(new \DateTime());

                        // Crear la transacción
                        $this->transactionRepository->create($newTransaction);
                        
                        // Actualizar balance de cuenta
                        $account = $this->accountRepository->findById($newTransaction->getAccountId());
                        if ($account) {
                            $account->setBalance($account->getBalance() + $newTransaction->getSignedAmount());
                            $this->accountRepository->update($account);
                        }

                        $processed++;

                    } catch (Exception $e) {
                        $errors[] = "Error procesando transacción {$transaction->getId()}: " . $e->getMessage();
                    }
                }
            }

            $this->logger->info('Transacciones recurrentes procesadas', [
                'processed' => $processed,
                'errors' => count($errors)
            ]);

            return [
                'success' => true,
                'processed' => $processed,
                'errors' => $errors
            ];

        } catch (Exception $e) {
            $this->logger->error('Error procesando transacciones recurrentes: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener tipos de transacción disponibles
     */
    public function getTransactionTypes(): array
    {
        return Transaction::TYPES;
    }

    /**
     * Obtener tipos de recurrencia disponibles
     */
    public function getRecurringTypes(): array
    {
        return Transaction::RECURRING_TYPES;
    }
}
