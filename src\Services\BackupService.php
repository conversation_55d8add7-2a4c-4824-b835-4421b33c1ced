<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Core\Database;
use ControlGastos\Core\Logger;
use ControlGastos\Core\Config;
use Exception;
use ZipArchive;

/**
 * Servicio de backup y seguridad
 * Maneja copias de seguridad automáticas, cifrado y restauración de datos
 */
class BackupService
{
    private Database $database;
    private Logger $logger;
    private Config $config;
    private string $backupPath;
    private string $encryptionKey;

    public function __construct(Database $database, Logger $logger, Config $config)
    {
        $this->database = $database;
        $this->logger = $logger;
        $this->config = $config;
        $this->backupPath = $config->get('backup.path', __DIR__ . '/../../storage/backups');
        $this->encryptionKey = $config->get('backup.encryption_key', 'default_key_change_this');

        // Crear directorio de backups si no existe
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }

    /**
     * Crear backup completo del sistema
     */
    public function createFullBackup(int $userId = null): array
    {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $backupName = $userId ? "user_{$userId}_{$timestamp}" : "full_system_{$timestamp}";
            $backupDir = $this->backupPath . '/' . $backupName;

            // Crear directorio temporal para el backup
            if (!mkdir($backupDir, 0755, true)) {
                throw new Exception('No se pudo crear el directorio de backup');
            }

            $backupData = [
                'metadata' => [
                    'backup_date' => date('Y-m-d H:i:s'),
                    'backup_type' => $userId ? 'user' : 'full',
                    'user_id' => $userId,
                    'version' => '1.0',
                    'app_version' => $this->config->get('app.version', '1.0.0')
                ],
                'data' => []
            ];

            // Backup de usuarios
            if ($userId) {
                $backupData['data']['users'] = $this->backupUserData($userId);
            } else {
                $backupData['data']['users'] = $this->backupAllUsers();
            }

            // Backup de cuentas
            $backupData['data']['accounts'] = $this->backupAccounts($userId);

            // Backup de categorías
            $backupData['data']['categories'] = $this->backupCategories($userId);
            $backupData['data']['subcategories'] = $this->backupSubcategories($userId);

            // Backup de transacciones
            $backupData['data']['transactions'] = $this->backupTransactions($userId);

            // Backup de recordatorios
            $backupData['data']['reminders'] = $this->backupReminders($userId);

            // Guardar datos en archivo JSON
            $jsonFile = $backupDir . '/data.json';
            file_put_contents($jsonFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            // Crear archivo comprimido y cifrado
            $zipFile = $this->backupPath . '/' . $backupName . '.backup';
            $this->createEncryptedZip($backupDir, $zipFile);

            // Limpiar directorio temporal
            $this->removeDirectory($backupDir);

            // Registrar backup en base de datos
            $backupId = $this->registerBackup($backupName, $zipFile, $userId);

            $this->logger->info('Backup creado exitosamente', [
                'backup_id' => $backupId,
                'backup_name' => $backupName,
                'user_id' => $userId,
                'file_size' => filesize($zipFile)
            ]);

            return [
                'success' => true,
                'message' => 'Backup creado exitosamente',
                'backup_id' => $backupId,
                'backup_name' => $backupName,
                'file_path' => $zipFile,
                'file_size' => filesize($zipFile)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error creando backup: ' . $e->getMessage());

            // Limpiar archivos temporales en caso de error
            if (isset($backupDir) && is_dir($backupDir)) {
                $this->removeDirectory($backupDir);
            }
            if (isset($zipFile) && file_exists($zipFile)) {
                unlink($zipFile);
            }

            return [
                'success' => false,
                'message' => 'Error al crear backup: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Restaurar backup
     */
    public function restoreBackup(int $backupId, int $userId = null): array
    {
        try {
            // Obtener información del backup
            $backup = $this->getBackupInfo($backupId);
            if (!$backup) {
                return [
                    'success' => false,
                    'message' => 'Backup no encontrado'
                ];
            }

            // Verificar permisos
            if ($userId && $backup['user_id'] && $backup['user_id'] !== $userId) {
                return [
                    'success' => false,
                    'message' => 'No tienes permisos para restaurar este backup'
                ];
            }

            // Verificar que el archivo existe
            if (!file_exists($backup['file_path'])) {
                return [
                    'success' => false,
                    'message' => 'Archivo de backup no encontrado'
                ];
            }

            // Crear backup de seguridad antes de restaurar
            $preRestoreBackup = $this->createFullBackup($userId);
            if (!$preRestoreBackup['success']) {
                return [
                    'success' => false,
                    'message' => 'Error creando backup de seguridad antes de restaurar'
                ];
            }

            // Extraer y descifrar backup
            $tempDir = $this->backupPath . '/temp_restore_' . time();
            $this->extractEncryptedZip($backup['file_path'], $tempDir);

            // Leer datos del backup
            $dataFile = $tempDir . '/data.json';
            if (!file_exists($dataFile)) {
                throw new Exception('Archivo de datos no encontrado en el backup');
            }

            $backupData = json_decode(file_get_contents($dataFile), true);
            if (!$backupData) {
                throw new Exception('Error al leer datos del backup');
            }

            // Iniciar transacción para restauración
            $this->database->beginTransaction();

            try {
                // Restaurar datos según el tipo de backup
                if ($backup['user_id']) {
                    $this->restoreUserData($backupData, $backup['user_id']);
                } else {
                    $this->restoreAllData($backupData);
                }

                $this->database->commit();

                // Limpiar directorio temporal
                $this->removeDirectory($tempDir);

                $this->logger->info('Backup restaurado exitosamente', [
                    'backup_id' => $backupId,
                    'user_id' => $userId,
                    'pre_restore_backup' => $preRestoreBackup['backup_id']
                ]);

                return [
                    'success' => true,
                    'message' => 'Backup restaurado exitosamente',
                    'pre_restore_backup_id' => $preRestoreBackup['backup_id']
                ];

            } catch (Exception $e) {
                $this->database->rollback();
                throw $e;
            }

        } catch (Exception $e) {
            $this->logger->error('Error restaurando backup: ' . $e->getMessage());

            // Limpiar directorio temporal
            if (isset($tempDir) && is_dir($tempDir)) {
                $this->removeDirectory($tempDir);
            }

            return [
                'success' => false,
                'message' => 'Error al restaurar backup: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Listar backups disponibles
     */
    public function listBackups(int $userId = null): array
    {
        try {
            $query = "
                SELECT id, backup_name, backup_type, user_id, file_path, file_size,
                       created_at, status
                FROM backups
                WHERE status = 'completed'
            ";

            $params = [];

            if ($userId) {
                $query .= " AND (user_id = :user_id OR user_id IS NULL)";
                $params['user_id'] = $userId;
            }

            $query .= " ORDER BY created_at DESC LIMIT 50";

            $backups = $this->database->select($query, $params);

            // Enriquecer datos con información adicional
            foreach ($backups as &$backup) {
                $backup['file_exists'] = file_exists($backup['file_path']);
                $backup['file_size_formatted'] = $this->formatFileSize($backup['file_size']);
                $backup['created_at_formatted'] = date('d/m/Y H:i:s', strtotime($backup['created_at']));
                $backup['age_days'] = floor((time() - strtotime($backup['created_at'])) / 86400);
            }

            return [
                'success' => true,
                'backups' => $backups
            ];

        } catch (Exception $e) {
            $this->logger->error('Error listando backups: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error al listar backups'
            ];
        }
    }

    /**
     * Eliminar backup
     */
    public function deleteBackup(int $backupId, int $userId = null): array
    {
        try {
            // Obtener información del backup
            $backup = $this->getBackupInfo($backupId);
            if (!$backup) {
                return [
                    'success' => false,
                    'message' => 'Backup no encontrado'
                ];
            }

            // Verificar permisos
            if ($userId && $backup['user_id'] && $backup['user_id'] !== $userId) {
                return [
                    'success' => false,
                    'message' => 'No tienes permisos para eliminar este backup'
                ];
            }

            // Eliminar archivo físico
            if (file_exists($backup['file_path'])) {
                unlink($backup['file_path']);
            }

            // Marcar como eliminado en base de datos
            $query = "UPDATE backups SET status = 'deleted', deleted_at = NOW() WHERE id = :id";
            $this->database->update($query, ['id' => $backupId]);

            $this->logger->info('Backup eliminado', [
                'backup_id' => $backupId,
                'user_id' => $userId
            ]);

            return [
                'success' => true,
                'message' => 'Backup eliminado exitosamente'
            ];

        } catch (Exception $e) {
            $this->logger->error('Error eliminando backup: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error al eliminar backup'
            ];
        }
    }

    /**
     * Limpiar backups antiguos
     */
    public function cleanOldBackups(int $daysToKeep = 30): array
    {
        try {
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));

            // Obtener backups antiguos
            $query = "
                SELECT id, file_path
                FROM backups
                WHERE created_at < :cutoff_date
                AND status = 'completed'
            ";

            $oldBackups = $this->database->select($query, ['cutoff_date' => $cutoffDate]);

            $deletedCount = 0;
            $errors = [];

            foreach ($oldBackups as $backup) {
                try {
                    // Eliminar archivo físico
                    if (file_exists($backup['file_path'])) {
                        unlink($backup['file_path']);
                    }

                    // Marcar como eliminado en base de datos
                    $updateQuery = "UPDATE backups SET status = 'deleted', deleted_at = NOW() WHERE id = :id";
                    $this->database->update($updateQuery, ['id' => $backup['id']]);

                    $deletedCount++;

                } catch (Exception $e) {
                    $errors[] = "Error eliminando backup {$backup['id']}: " . $e->getMessage();
                }
            }

            $this->logger->info('Limpieza de backups completada', [
                'days_to_keep' => $daysToKeep,
                'deleted_count' => $deletedCount,
                'errors' => count($errors)
            ]);

            return [
                'success' => true,
                'message' => "Limpieza completada. {$deletedCount} backups eliminados.",
                'deleted_count' => $deletedCount,
                'errors' => $errors
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en limpieza de backups: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error en limpieza de backups'
            ];
        }
    }

    /**
     * Verificar integridad de backup
     */
    public function verifyBackupIntegrity(int $backupId): array
    {
        try {
            $backup = $this->getBackupInfo($backupId);
            if (!$backup) {
                return [
                    'success' => false,
                    'message' => 'Backup no encontrado'
                ];
            }

            $checks = [
                'file_exists' => file_exists($backup['file_path']),
                'file_readable' => is_readable($backup['file_path']),
                'file_size_match' => filesize($backup['file_path']) === (int) $backup['file_size'],
                'can_extract' => false,
                'data_valid' => false
            ];

            if ($checks['file_exists'] && $checks['file_readable']) {
                // Intentar extraer y verificar contenido
                $tempDir = $this->backupPath . '/temp_verify_' . time();

                try {
                    $this->extractEncryptedZip($backup['file_path'], $tempDir);
                    $checks['can_extract'] = true;

                    $dataFile = $tempDir . '/data.json';
                    if (file_exists($dataFile)) {
                        $data = json_decode(file_get_contents($dataFile), true);
                        $checks['data_valid'] = $data !== null && isset($data['metadata']);
                    }

                    $this->removeDirectory($tempDir);

                } catch (Exception $e) {
                    if (is_dir($tempDir)) {
                        $this->removeDirectory($tempDir);
                    }
                }
            }

            $isValid = array_reduce($checks, function($carry, $check) {
                return $carry && $check;
            }, true);

            return [
                'success' => true,
                'is_valid' => $isValid,
                'checks' => $checks
            ];

        } catch (Exception $e) {
            $this->logger->error('Error verificando integridad: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error al verificar integridad'
            ];
        }
    }

    /**
     * Backup de datos de usuario específico
     */
    private function backupUserData(int $userId): array
    {
        $query = "SELECT * FROM users WHERE id = :user_id";
        $users = $this->database->select($query, ['user_id' => $userId]);

        // Remover información sensible
        foreach ($users as &$user) {
            unset($user['password']);
        }

        return $users;
    }

    /**
     * Backup de todos los usuarios
     */
    private function backupAllUsers(): array
    {
        $query = "SELECT * FROM users ORDER BY id";
        $users = $this->database->select($query);

        // Remover información sensible
        foreach ($users as &$user) {
            unset($user['password']);
        }

        return $users;
    }

    /**
     * Backup de cuentas
     */
    private function backupAccounts(int $userId = null): array
    {
        $query = "SELECT * FROM accounts";
        $params = [];

        if ($userId) {
            $query .= " WHERE user_id = :user_id";
            $params['user_id'] = $userId;
        }

        $query .= " ORDER BY id";

        return $this->database->select($query, $params);
    }

    /**
     * Backup de categorías
     */
    private function backupCategories(int $userId = null): array
    {
        $query = "SELECT * FROM categories";
        $params = [];

        if ($userId) {
            $query .= " WHERE user_id = :user_id";
            $params['user_id'] = $userId;
        }

        $query .= " ORDER BY id";

        return $this->database->select($query, $params);
    }

    /**
     * Backup de subcategorías
     */
    private function backupSubcategories(int $userId = null): array
    {
        $query = "SELECT s.* FROM subcategories s";
        $params = [];

        if ($userId) {
            $query .= " INNER JOIN categories c ON s.category_id = c.id WHERE c.user_id = :user_id";
            $params['user_id'] = $userId;
        }

        $query .= " ORDER BY s.id";

        return $this->database->select($query, $params);
    }

    /**
     * Backup de transacciones
     */
    private function backupTransactions(int $userId = null): array
    {
        $query = "SELECT * FROM transactions";
        $params = [];

        if ($userId) {
            $query .= " WHERE user_id = :user_id";
            $params['user_id'] = $userId;
        }

        $query .= " ORDER BY id";

        return $this->database->select($query, $params);
    }

    /**
     * Backup de recordatorios
     */
    private function backupReminders(int $userId = null): array
    {
        $query = "SELECT * FROM reminders";
        $params = [];

        if ($userId) {
            $query .= " WHERE user_id = :user_id";
            $params['user_id'] = $userId;
        }

        $query .= " ORDER BY id";

        return $this->database->select($query, $params);
    }

    /**
     * Crear ZIP cifrado
     */
    private function createEncryptedZip(string $sourceDir, string $zipFile): void
    {
        $zip = new ZipArchive();

        if ($zip->open($zipFile, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new Exception('No se pudo crear el archivo ZIP');
        }

        // Agregar archivos al ZIP
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($sourceDir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = substr($file->getPathname(), strlen($sourceDir) + 1);

                // Cifrar contenido del archivo
                $content = file_get_contents($file->getPathname());
                $encryptedContent = $this->encrypt($content);

                $zip->addFromString($relativePath, $encryptedContent);
            }
        }

        $zip->close();
    }

    /**
     * Extraer ZIP cifrado
     */
    private function extractEncryptedZip(string $zipFile, string $destDir): void
    {
        if (!mkdir($destDir, 0755, true)) {
            throw new Exception('No se pudo crear directorio de destino');
        }

        $zip = new ZipArchive();

        if ($zip->open($zipFile) !== TRUE) {
            throw new Exception('No se pudo abrir el archivo ZIP');
        }

        for ($i = 0; $i < $zip->numFiles; $i++) {
            $filename = $zip->getNameIndex($i);
            $encryptedContent = $zip->getFromIndex($i);

            // Descifrar contenido
            $content = $this->decrypt($encryptedContent);

            $filePath = $destDir . '/' . $filename;
            $fileDir = dirname($filePath);

            if (!is_dir($fileDir)) {
                mkdir($fileDir, 0755, true);
            }

            file_put_contents($filePath, $content);
        }

        $zip->close();
    }

    /**
     * Cifrar datos
     */
    private function encrypt(string $data): string
    {
        $method = 'AES-256-CBC';
        $key = hash('sha256', $this->encryptionKey, true);
        $iv = openssl_random_pseudo_bytes(16);

        $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);

        return base64_encode($iv . $encrypted);
    }

    /**
     * Descifrar datos
     */
    private function decrypt(string $data): string
    {
        $method = 'AES-256-CBC';
        $key = hash('sha256', $this->encryptionKey, true);

        $data = base64_decode($data);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);

        return openssl_decrypt($encrypted, $method, $key, OPENSSL_RAW_DATA, $iv);
    }

    /**
     * Registrar backup en base de datos
     */
    private function registerBackup(string $backupName, string $filePath, int $userId = null): int
    {
        $query = "
            INSERT INTO backups (backup_name, backup_type, user_id, file_path, file_size, status, created_at)
            VALUES (:backup_name, :backup_type, :user_id, :file_path, :file_size, 'completed', NOW())
        ";

        $params = [
            'backup_name' => $backupName,
            'backup_type' => $userId ? 'user' : 'full',
            'user_id' => $userId,
            'file_path' => $filePath,
            'file_size' => filesize($filePath)
        ];

        return $this->database->insert($query, $params);
    }

    /**
     * Obtener información de backup
     */
    private function getBackupInfo(int $backupId): ?array
    {
        $query = "SELECT * FROM backups WHERE id = :id AND status = 'completed'";
        return $this->database->selectOne($query, ['id' => $backupId]);
    }

    /**
     * Restaurar datos de usuario
     */
    private function restoreUserData(array $backupData, int $userId): void
    {
        // Eliminar datos existentes del usuario
        $this->database->delete("DELETE FROM reminders WHERE user_id = :user_id", ['user_id' => $userId]);
        $this->database->delete("DELETE FROM transactions WHERE user_id = :user_id", ['user_id' => $userId]);
        $this->database->delete("DELETE FROM subcategories WHERE category_id IN (SELECT id FROM categories WHERE user_id = :user_id)", ['user_id' => $userId]);
        $this->database->delete("DELETE FROM categories WHERE user_id = :user_id", ['user_id' => $userId]);
        $this->database->delete("DELETE FROM accounts WHERE user_id = :user_id", ['user_id' => $userId]);

        // Restaurar datos
        $this->restoreTableData('accounts', $backupData['data']['accounts'] ?? []);
        $this->restoreTableData('categories', $backupData['data']['categories'] ?? []);
        $this->restoreTableData('subcategories', $backupData['data']['subcategories'] ?? []);
        $this->restoreTableData('transactions', $backupData['data']['transactions'] ?? []);
        $this->restoreTableData('reminders', $backupData['data']['reminders'] ?? []);
    }

    /**
     * Restaurar todos los datos
     */
    private function restoreAllData(array $backupData): void
    {
        // Limpiar todas las tablas (excepto usuarios)
        $this->database->delete("DELETE FROM reminders", []);
        $this->database->delete("DELETE FROM transactions", []);
        $this->database->delete("DELETE FROM subcategories", []);
        $this->database->delete("DELETE FROM categories", []);
        $this->database->delete("DELETE FROM accounts", []);

        // Restaurar datos
        $this->restoreTableData('accounts', $backupData['data']['accounts'] ?? []);
        $this->restoreTableData('categories', $backupData['data']['categories'] ?? []);
        $this->restoreTableData('subcategories', $backupData['data']['subcategories'] ?? []);
        $this->restoreTableData('transactions', $backupData['data']['transactions'] ?? []);
        $this->restoreTableData('reminders', $backupData['data']['reminders'] ?? []);
    }

    /**
     * Restaurar datos de tabla específica
     */
    private function restoreTableData(string $table, array $data): void
    {
        if (empty($data)) {
            return;
        }

        foreach ($data as $row) {
            $columns = array_keys($row);
            $placeholders = array_map(fn($col) => ':' . $col, $columns);

            $query = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";

            $this->database->insert($query, $row);
        }
    }

    /**
     * Eliminar directorio recursivamente
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);

        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }

        rmdir($dir);
    }

    /**
     * Formatear tamaño de archivo
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen((string) $bytes) - 1) / 3);

        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * Programar backup automático
     */
    public function scheduleAutomaticBackup(int $userId = null, string $frequency = 'daily'): array
    {
        try {
            // Verificar si ya existe un backup programado
            $query = "
                SELECT id FROM scheduled_backups
                WHERE user_id " . ($userId ? "= :user_id" : "IS NULL") . "
                AND status = 'active'
            ";

            $params = $userId ? ['user_id' => $userId] : [];
            $existing = $this->database->selectOne($query, $params);

            if ($existing) {
                return [
                    'success' => false,
                    'message' => 'Ya existe un backup automático programado'
                ];
            }

            // Calcular próxima ejecución
            $nextRun = $this->calculateNextRun($frequency);

            // Crear programación
            $insertQuery = "
                INSERT INTO scheduled_backups (user_id, frequency, next_run, status, created_at)
                VALUES (:user_id, :frequency, :next_run, 'active', NOW())
            ";

            $insertParams = [
                'user_id' => $userId,
                'frequency' => $frequency,
                'next_run' => $nextRun->format('Y-m-d H:i:s')
            ];

            $scheduleId = $this->database->insert($insertQuery, $insertParams);

            return [
                'success' => true,
                'message' => 'Backup automático programado exitosamente',
                'schedule_id' => $scheduleId,
                'next_run' => $nextRun->format('Y-m-d H:i:s')
            ];

        } catch (Exception $e) {
            $this->logger->error('Error programando backup automático: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error al programar backup automático'
            ];
        }
    }

    /**
     * Ejecutar backups programados
     */
    public function runScheduledBackups(): array
    {
        try {
            $query = "
                SELECT * FROM scheduled_backups
                WHERE status = 'active'
                AND next_run <= NOW()
            ";

            $scheduledBackups = $this->database->select($query);
            $results = [];

            foreach ($scheduledBackups as $schedule) {
                try {
                    // Crear backup
                    $result = $this->createFullBackup($schedule['user_id']);

                    if ($result['success']) {
                        // Actualizar próxima ejecución
                        $nextRun = $this->calculateNextRun($schedule['frequency']);

                        $updateQuery = "
                            UPDATE scheduled_backups
                            SET next_run = :next_run, last_run = NOW(), last_backup_id = :backup_id
                            WHERE id = :id
                        ";

                        $this->database->update($updateQuery, [
                            'next_run' => $nextRun->format('Y-m-d H:i:s'),
                            'backup_id' => $result['backup_id'],
                            'id' => $schedule['id']
                        ]);

                        $results[] = [
                            'schedule_id' => $schedule['id'],
                            'user_id' => $schedule['user_id'],
                            'success' => true,
                            'backup_id' => $result['backup_id']
                        ];
                    } else {
                        $results[] = [
                            'schedule_id' => $schedule['id'],
                            'user_id' => $schedule['user_id'],
                            'success' => false,
                            'error' => $result['message']
                        ];
                    }

                } catch (Exception $e) {
                    $results[] = [
                        'schedule_id' => $schedule['id'],
                        'user_id' => $schedule['user_id'],
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return [
                'success' => true,
                'processed' => count($scheduledBackups),
                'results' => $results
            ];

        } catch (Exception $e) {
            $this->logger->error('Error ejecutando backups programados: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error ejecutando backups programados'
            ];
        }
    }

    /**
     * Calcular próxima ejecución
     */
    private function calculateNextRun(string $frequency): \DateTime
    {
        $nextRun = new \DateTime();

        switch ($frequency) {
            case 'hourly':
                $nextRun->modify('+1 hour');
                break;
            case 'daily':
                $nextRun->modify('+1 day')->setTime(2, 0, 0); // 2 AM
                break;
            case 'weekly':
                $nextRun->modify('next sunday')->setTime(2, 0, 0);
                break;
            case 'monthly':
                $nextRun->modify('first day of next month')->setTime(2, 0, 0);
                break;
            default:
                $nextRun->modify('+1 day');
        }

        return $nextRun;
    }
}