<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\SimpleDatabase;
use ControlGastos\Core\Session;
use PDO;

/**
 * Controlador para gestión de bancos/entidades financieras
 */
class BankController
{
    private PDO $db;
    private Session $session;

    public function __construct(SimpleDatabase $database, Session $session)
    {
        $this->db = $database->getConnection();
        $this->session = $session;
    }

    /**
     * Mostrar lista de bancos
     */
    public function index(): string
    {
        try {
            $userId = $this->session->get('user_id');
            $banks = $this->getBanks($userId);
            $stats = $this->getBankStats($userId);

            $data = [
                'title' => 'Gestión de Bancos',
                'banks' => $banks,
                'stats' => $stats,
                'success' => $this->session->getFlash('success'),
                'error' => $this->session->getFlash('error')
            ];

            return $this->render('banks/index', $data);

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error cargando bancos: ' . $e->getMessage());
            return $this->render('banks/index', [
                'title' => 'Gestión de Bancos',
                'banks' => [],
                'stats' => [],
                'error' => $this->session->getFlash('error')
            ]);
        }
    }

    /**
     * Mostrar formulario de creación
     */
    public function create(): string
    {
        $data = [
            'title' => 'Nuevo Banco',
            'bank' => null,
            'action' => 'create'
        ];

        return $this->render('banks/form', $data);
    }

    /**
     * Procesar creación de banco
     */
    public function store(): void
    {
        try {
            $data = $this->validateBankData($_POST);
            $data['user_id'] = $this->session->get('user_id');

            $sql = "INSERT INTO banks (
                user_id, bank_name, bank_code, bank_type, country, 
                website, phone, email, logo_url, color, description
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $data['user_id'],
                $data['bank_name'],
                $data['bank_code'],
                $data['bank_type'],
                $data['country'],
                $data['website'],
                $data['phone'],
                $data['email'],
                $data['logo_url'],
                $data['color'],
                $data['description']
            ]);

            $this->session->flash('success', 'Banco creado exitosamente');
            $this->redirect('?route=banks');

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error creando banco: ' . $e->getMessage());
            $this->redirect('?route=banks&action=create');
        }
    }

    /**
     * Mostrar formulario de edición
     */
    public function edit(): string
    {
        try {
            $id = (int)($_GET['id'] ?? 0);
            $userId = $this->session->get('user_id');
            
            $bank = $this->getBankById($id, $userId);
            
            if (!$bank) {
                $this->session->flash('error', 'Banco no encontrado');
                $this->redirect('?route=banks');
                return '';
            }

            $data = [
                'title' => 'Editar Banco',
                'bank' => $bank,
                'action' => 'edit'
            ];

            return $this->render('banks/form', $data);

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error cargando banco: ' . $e->getMessage());
            $this->redirect('?route=banks');
            return '';
        }
    }

    /**
     * Procesar actualización de banco
     */
    public function update(): void
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            $userId = $this->session->get('user_id');
            
            $bank = $this->getBankById($id, $userId);
            if (!$bank) {
                $this->session->flash('error', 'Banco no encontrado');
                $this->redirect('?route=banks');
                return;
            }

            $data = $this->validateBankData($_POST);

            $sql = "UPDATE banks SET 
                bank_name = ?, bank_code = ?, bank_type = ?, country = ?,
                website = ?, phone = ?, email = ?, logo_url = ?, color = ?, 
                description = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND user_id = ?";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $data['bank_name'],
                $data['bank_code'],
                $data['bank_type'],
                $data['country'],
                $data['website'],
                $data['phone'],
                $data['email'],
                $data['logo_url'],
                $data['color'],
                $data['description'],
                $id,
                $userId
            ]);

            $this->session->flash('success', 'Banco actualizado exitosamente');
            $this->redirect('?route=banks');

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error actualizando banco: ' . $e->getMessage());
            $this->redirect('?route=banks&action=edit&id=' . ($id ?? 0));
        }
    }

    /**
     * Eliminar banco
     */
    public function delete(): void
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            $userId = $this->session->get('user_id');
            
            $bank = $this->getBankById($id, $userId);
            if (!$bank) {
                $this->session->flash('error', 'Banco no encontrado');
                $this->redirect('?route=banks');
                return;
            }

            // Verificar si el banco tiene cuentas o tarjetas asociadas
            $hasAccounts = $this->bankHasAccounts($id);
            $hasCards = $this->bankHasCards($id);

            if ($hasAccounts || $hasCards) {
                $this->session->flash('error', 'No se puede eliminar el banco porque tiene cuentas o tarjetas asociadas');
                $this->redirect('?route=banks');
                return;
            }

            $sql = "DELETE FROM banks WHERE id = ? AND user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id, $userId]);

            $this->session->flash('success', 'Banco eliminado exitosamente');
            $this->redirect('?route=banks');

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error eliminando banco: ' . $e->getMessage());
            $this->redirect('?route=banks');
        }
    }

    /**
     * Activar/desactivar banco
     */
    public function toggleStatus(): void
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            $userId = $this->session->get('user_id');
            
            $bank = $this->getBankById($id, $userId);
            if (!$bank) {
                $this->jsonResponse(['success' => false, 'message' => 'Banco no encontrado']);
                return;
            }

            $newStatus = $bank['is_active'] ? 0 : 1;
            
            $sql = "UPDATE banks SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$newStatus, $id, $userId]);

            $statusText = $newStatus ? 'activado' : 'desactivado';
            $this->jsonResponse([
                'success' => true, 
                'message' => "Banco {$statusText} exitosamente",
                'new_status' => $newStatus
            ]);

        } catch (\Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => 'Error cambiando estado: ' . $e->getMessage()]);
        }
    }

    /**
     * Obtener bancos del usuario
     */
    private function getBanks(int $userId): array
    {
        $sql = "SELECT b.*,
                (SELECT COUNT(*) FROM bank_accounts ba WHERE ba.bank_id = b.id) as accounts_count,
                (SELECT COUNT(*) FROM credit_cards cc WHERE cc.bank_id = b.id) as cards_count
                FROM banks b
                WHERE b.user_id = ?
                ORDER BY b.is_active DESC, b.bank_name ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);

        return $stmt->fetchAll();
    }

    /**
     * Obtener banco por ID
     */
    private function getBankById(int $id, int $userId): ?array
    {
        $sql = "SELECT * FROM banks WHERE id = ? AND user_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id, $userId]);

        $result = $stmt->fetch();
        return $result ?: null;
    }

    /**
     * Obtener estadísticas de bancos
     */
    private function getBankStats(int $userId): array
    {
        $sql = "SELECT
                COUNT(*) as total_banks,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_banks,
                COUNT(DISTINCT bank_type) as bank_types
                FROM banks
                WHERE user_id = ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);

        return $stmt->fetch() ?: ['total_banks' => 0, 'active_banks' => 0, 'bank_types' => 0];
    }

    /**
     * Verificar si el banco tiene cuentas asociadas
     */
    private function bankHasAccounts(int $bankId): bool
    {
        $sql = "SELECT COUNT(*) FROM bank_accounts WHERE bank_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$bankId]);

        return (int)$stmt->fetchColumn() > 0;
    }

    /**
     * Verificar si el banco tiene tarjetas asociadas
     */
    private function bankHasCards(int $bankId): bool
    {
        $sql = "SELECT COUNT(*) FROM credit_cards WHERE bank_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$bankId]);

        return (int)$stmt->fetchColumn() > 0;
    }

    /**
     * Validar datos del banco
     */
    private function validateBankData(array $data): array
    {
        $errors = [];

        if (empty($data['bank_name'])) {
            $errors[] = 'El nombre del banco es requerido';
        }

        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'El email no es válido';
        }

        if (!empty($data['website']) && !filter_var($data['website'], FILTER_VALIDATE_URL)) {
            $errors[] = 'La URL del sitio web no es válida';
        }

        if (!empty($data['color']) && !preg_match('/^#[0-9A-Fa-f]{6}$/', $data['color'])) {
            $errors[] = 'El color debe ser un código hexadecimal válido';
        }

        if (!empty($errors)) {
            throw new \Exception(implode(', ', $errors));
        }

        return [
            'bank_name' => trim($data['bank_name']),
            'bank_code' => trim($data['bank_code'] ?? ''),
            'bank_type' => $data['bank_type'] ?? 'commercial',
            'country' => $data['country'] ?? 'COL',
            'website' => trim($data['website'] ?? ''),
            'phone' => trim($data['phone'] ?? ''),
            'email' => trim($data['email'] ?? ''),
            'logo_url' => trim($data['logo_url'] ?? ''),
            'color' => $data['color'] ?? '#007bff',
            'description' => trim($data['description'] ?? '')
        ];
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';

        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Redireccionar
     */
    private function redirect(string $url): void
    {
        header("Location: {$url}");
        exit;
    }

    /**
     * Respuesta JSON
     */
    private function jsonResponse(array $data): void
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}
