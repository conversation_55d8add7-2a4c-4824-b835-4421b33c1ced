<?php
// La autenticación ya se verifica en el controlador
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0"><i class="fas fa-plus-circle me-2"></i>Nueva Tarjeta de Crédito</h1>
                    <p class="text-muted">Agrega una nueva tarjeta de crédito a tu cartera</p>
                </div>
                <a href="/controlGastos/public/?route=credit-cards" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Volver
                </a>
            </div>
        </div>
    </div>

    <!-- Formulario -->
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i>Información de la Tarjeta
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/controlGastos/public/?route=credit-cards/store" id="creditCardForm">
                        <!-- Información Básica -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="card_name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Nombre de la Tarjeta *
                                </label>
                                <input type="text" class="form-control" id="card_name" name="card_name"
                                       placeholder="Ej: Visa Principal" required maxlength="100">
                                <div class="form-text">Nombre personalizado para identificar tu tarjeta</div>
                            </div>
                            <div class="col-md-6">
                                <label for="bank_id" class="form-label">
                                    <span class="icon-emoji me-1">🏛️</span>Banco Emisor *
                                </label>
                                <select class="form-select" id="bank_id" name="bank_id" required>
                                    <option value="">Seleccionar banco...</option>
                                    <?php if (!empty($banks)): ?>
                                        <?php foreach ($banks as $bank): ?>
                                            <option value="<?= $bank['id'] ?>" data-color="<?= htmlspecialchars($bank['color'] ?? '') ?>">
                                                <?= htmlspecialchars($bank['bank_name']) ?>
                                                <?php if (!empty($bank['bank_code'])): ?>
                                                    (<?= htmlspecialchars($bank['bank_code']) ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <option value="" disabled>No hay bancos disponibles</option>
                                    <?php endif; ?>
                                </select>
                                <div class="form-text">
                                    Selecciona el banco emisor.
                                    <a href="?route=banks&action=create" target="_blank" class="text-primary">
                                        <span class="icon-emoji">➕</span> Agregar nuevo banco
                                    </a>
                                    <br>
                                    <small class="text-muted">
                                        💡 También puedes gestionar bancos desde:
                                        <strong>Menú → Gestión Financiera → Bancos</strong>
                                    </small>
                                </div>
                            </div>
                        </div>


                        <!-- Información de la Tarjeta -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="card_type" class="form-label">
                                    <i class="fas fa-credit-card me-1"></i>Tipo de Tarjeta *
                                </label>
                                <select class="form-select" id="card_type" name="card_type" required>
                                    <option value="">Seleccionar tipo...</option>
                                    <option value="visa">Visa</option>
                                    <option value="mastercard">Mastercard</option>
                                    <option value="american_express">American Express</option>
                                    <option value="diners">Diners Club</option>
                                    <option value="discover">Discover</option>
                                    <option value="other">Otra</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="card_number_last4" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Últimos 4 Dígitos
                                </label>
                                <input type="text" class="form-control" id="card_number_last4" name="card_number_last4"
                                       placeholder="1234" maxlength="4" pattern="[0-9]{4}">
                                <div class="form-text">Para identificación</div>
                            </div>
                            <div class="col-md-4">
                                <label for="credit_limit" class="form-label">
                                    <i class="fas fa-wallet me-1"></i>Cupo Total *
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit"
                                           placeholder="50000" required min="1000" step="1000" max="********">
                                </div>
                            </div>
                        </div>

                        <!-- Cuota de Manejo -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="management_fee" class="form-label">
                                    <i class="fas fa-money-bill me-1"></i>Cuota de Manejo Mensual
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="management_fee" name="management_fee"
                                           placeholder="15000" min="0" step="1000" max="100000">
                                </div>
                                <div class="form-text">Cuota que cobra el banco mensualmente (opcional)</div>
                            </div>
                            <div class="col-md-6">
                                <!-- Espacio para futuros campos -->
                            </div>
                        </div>

                        <!-- Fechas -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="expiry_date" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>Fecha de Expiración *
                                </label>
                                <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cut_off_day" class="form-label">
                                    <i class="fas fa-calendar-check me-1"></i>Día de Corte *
                                </label>
                                <select class="form-select" id="cut_off_day" name="cut_off_day" required>
                                    <?php for ($i = 1; $i <= 31; $i++): ?>
                                    <option value="<?= $i ?>" <?= $i == 15 ? 'selected' : '' ?>><?= $i ?></option>
                                    <?php endfor; ?>
                                </select>
                                <div class="form-text">Día del mes en que se genera el estado de cuenta</div>
                            </div>
                        </div>

                        <!-- Configuración de Pagos -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="payment_due_days" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Días para Pagar
                                </label>
                                <input type="number" class="form-control" id="payment_due_days" name="payment_due_days" 
                                       value="20" min="1" max="60" required>
                                <div class="form-text">Días después del corte para realizar el pago</div>
                            </div>
                            <div class="col-md-6">
                                <label for="cvv" class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>Código CCV
                                </label>
                                <input type="password" class="form-control" id="cvv" name="cvv" 
                                       placeholder="123" maxlength="4" pattern="[0-9]{3,4}">
                                <div class="form-text">Código de seguridad (opcional)</div>
                            </div>
                        </div>

                        <!-- Descripción -->
                        <div class="mb-4">
                            <label for="description" class="form-label">
                                <i class="fas fa-comment me-1"></i>Descripción
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Descripción adicional de la tarjeta (opcional)"></textarea>
                        </div>

                        <!-- Vista Previa -->
                        <div class="card bg-light mb-4" id="cardPreview">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Vista Previa</h6>
                            </div>
                            <div class="card-body">
                                <div class="card shadow-sm" style="max-width: 350px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                    <div class="card-body text-white">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div>
                                                <h6 class="mb-0" id="preview_card_name">Nombre de la Tarjeta</h6>
                                                <small id="preview_bank_name">Banco Emisor</small>
                                            </div>
                                            <span class="icon-emoji" style="font-size: 2rem; opacity: 0.75;">💳</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-end">
                                            <div>
                                                <small class="opacity-75">Cupo</small>
                                                <div id="preview_credit_limit">$0</div>
                                            </div>
                                            <div class="text-center">
                                                <small class="opacity-75">Cuota</small>
                                                <div id="preview_management_fee">$0</div>
                                            </div>
                                            <div class="text-end">
                                                <small class="opacity-75">Vence</small>
                                                <div id="preview_expiry_date">--/--</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Botones -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary flex-fill">
                                <i class="fas fa-save me-2"></i>Crear Tarjeta
                            </button>
                            <a href="/controlGastos/public/?route=credit-cards" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('creditCardForm');
    const previewElements = {
        card_name: document.getElementById('preview_card_name'),
        bank_name: document.getElementById('preview_bank_name'),
        credit_limit: document.getElementById('preview_credit_limit'),
        management_fee: document.getElementById('preview_management_fee'),
        expiry_date: document.getElementById('preview_expiry_date')
    };

    // Gestión de banco seleccionado
    form.bank_id.addEventListener('change', function() {
        updatePreview();
    });

    // Actualizar vista previa en tiempo real
    function updatePreview() {
        const cardName = form.card_name.value || 'Nombre de la Tarjeta';

        // Obtener nombre del banco seleccionado
        let bankName = 'Banco Emisor';
        const bankSelect = form.bank_id;
        if (bankSelect.value && bankSelect.selectedOptions.length > 0) {
            bankName = bankSelect.selectedOptions[0].textContent.trim();
        }

        const creditLimit = form.credit_limit.value ? '$' + parseInt(form.credit_limit.value).toLocaleString() : '$0';
        const managementFee = form.management_fee.value ? '$' + parseInt(form.management_fee.value).toLocaleString() : '$0';
        const expiryDate = form.expiry_date.value ? new Date(form.expiry_date.value).toLocaleDateString('es-ES', {month: '2-digit', year: '2-digit'}) : '--/--';

        previewElements.card_name.textContent = cardName;
        previewElements.bank_name.textContent = bankName;
        previewElements.credit_limit.textContent = creditLimit;
        previewElements.management_fee.textContent = managementFee;
        previewElements.expiry_date.textContent = expiryDate;
    }

    // Event listeners para vista previa
    ['card_name', 'bank_id', 'credit_limit', 'management_fee', 'expiry_date'].forEach(field => {
        form[field].addEventListener('input', updatePreview);
        form[field].addEventListener('change', updatePreview);
    });

    // Validaciones
    form.expiry_date.addEventListener('change', function() {
        const selectedDate = new Date(this.value);
        const today = new Date();
        const minDate = new Date();
        minDate.setMonth(minDate.getMonth() + 1); // Al menos 1 mes en el futuro

        if (selectedDate <= today) {
            alert('La fecha de expiración debe ser futura');
            this.value = '';
            return;
        }

        if (selectedDate < minDate) {
            if (!confirm('La tarjeta expira muy pronto. ¿Está seguro de la fecha?')) {
                this.value = '';
                return;
            }
        }
    });

    // Formatear número de tarjeta
    form.card_number_last4.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').slice(0, 4);

        // Validar que sean exactamente 4 dígitos si no está vacío
        if (this.value.length > 0 && this.value.length < 4) {
            this.setCustomValidity('Debe ingresar exactamente 4 dígitos');
        } else {
            this.setCustomValidity('');
        }
    });

    // Formatear CVV
    form.cvv.addEventListener('input', function() {
        const cardType = form.card_type.value;
        const maxLength = cardType === 'american_express' ? 4 : 3;

        this.value = this.value.replace(/\D/g, '').slice(0, maxLength);
        this.maxLength = maxLength;

        if (this.value.length > 0 && this.value.length < (cardType === 'american_express' ? 4 : 3)) {
            this.setCustomValidity(`El CVV debe tener ${maxLength} dígitos`);
        } else {
            this.setCustomValidity('');
        }
    });

    // Validar cupo
    form.credit_limit.addEventListener('input', function() {
        const value = parseInt(this.value);
        if (value < 1000) {
            this.setCustomValidity('El cupo mínimo es $1,000');
        } else if (value > ********) {
            this.setCustomValidity('El cupo máximo es $10,000,000');
        } else {
            this.setCustomValidity('');
        }
    });

    // Validación antes de enviar
    form.addEventListener('submit', function(e) {
        if (!form.bank_id.value) {
            e.preventDefault();
            alert('Por favor seleccione un banco emisor');
            form.bank_id.focus();
            return;
        }
    });

    // Inicializar vista previa
    updatePreview();
});
</script>
