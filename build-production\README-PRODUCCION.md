# 🚀 Control de Gastos - Instalación en Producción

## 📋 Requisitos del Servidor

- PHP 8.0 o superior
- MySQL 5.7 o superior
- Extensiones PHP: PDO, PDO_MySQL, OpenSSL, mbstring, JSON
- Espacio en disco: mínimo 100MB
- Memoria PHP: mínimo 128MB

## 🔧 Pasos de Instalación

### 1. Subir Archivos
- Sube todos los archivos a tu hosting
- El contenido de `public/` debe ir en `public_html/`
- Los demás directorios van en el directorio raíz de tu cuenta

### 2. Configurar Base de Datos
- Crea una base de datos MySQL en cPanel
- Anota: nombre de BD, usuario, contraseña

### 3. Configurar Aplicación
- Renombra `.env.production` a `.env`
- Edita `.env` con tus datos reales
- Genera claves seguras para APP_KEY y ENCRYPTION_KEY

### 4. Ejecutar Instalación
- Visita: `tudominio.com/install-cpanel.php`
- Sigue las instrucciones
- Ejecuta migraciones: `tudominio.com/database/run-migrations.php`
- Crea admin: `tudominio.com/scripts/create-admin.php`

### 5. Seguridad Post-Instalación
- Elimina `install-cpanel.php`
- Elimina `database/run-migrations.php`
- Elimina `scripts/create-admin.php`
- Verifica permisos de directorios

## 🔒 Configuración de Seguridad

### Permisos de Archivos
```
Directorios: 755
Archivos PHP: 644
.env: 600
logs/: 755 (writable)
storage/: 755 (writable)
```

### Variables de Entorno Importantes
- `APP_KEY`: Clave de 32 caracteres para cifrado
- `DB_*`: Credenciales de base de datos
- `SMTP_*`: Configuración de email

## 📧 Configuración de Email

Para que funcionen los emails:
1. Configura SMTP en cPanel
2. Actualiza variables SMTP_* en .env
3. Verifica que el puerto 587 esté abierto

## 🔄 Mantenimiento

### Backup Automático
- Configura cron jobs para backup
- Ruta: `/scripts/cron-setup.sh`

### Logs
- Revisa logs en `/logs/`
- Configura rotación de logs

### Actualizaciones
- Siempre haz backup antes de actualizar
- Prueba en staging primero

## 🆘 Solución de Problemas

### Error 500
- Revisa logs de PHP en cPanel
- Verifica permisos de archivos
- Comprueba configuración .env

### Error de Base de Datos
- Verifica credenciales en .env
- Comprueba que la BD existe
- Revisa permisos del usuario de BD

### Emails no se envían
- Verifica configuración SMTP
- Comprueba logs de email
- Revisa firewall del servidor

## 📞 Soporte

Si necesitas ayuda:
1. Revisa los logs de error
2. Verifica la configuración
3. Consulta la documentación
