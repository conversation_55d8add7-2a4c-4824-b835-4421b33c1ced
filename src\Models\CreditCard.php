<?php

declare(strict_types=1);

namespace ControlGastos\Models;

use PDO;
use DateTime;
use Exception;

/**
 * Modelo para gestión de tarjetas de crédito
 */
class CreditCard
{
    private PDO $db;
    
    public function __construct(PDO $db)
    {
        $this->db = $db;
    }
    
    /**
     * Crear una nueva tarjeta de crédito
     */
    public function create(array $data): int
    {
        $sql = "INSERT INTO credit_cards (
            user_id, card_name, bank_id, card_type, card_number_last4, credit_limit,
            management_fee, expiry_date, cut_off_day, payment_due_days, cvv, description, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['user_id'],
            $data['card_name'],
            $data['bank_id'],
            $data['card_type'] ?? 'visa',
            $data['card_number_last4'] ?? null,
            $data['credit_limit'],
            $data['management_fee'] ?? 0.00,
            $data['expiry_date'],
            $data['cut_off_day'],
            $data['payment_due_days'],
            $data['cvv'] ?? null,
            $data['description'] ?? null,
            $data['status'] ?? 'active'
        ]);

        return (int) $this->db->lastInsertId();
    }
    
    /**
     * Obtener tarjeta por ID
     */
    public function findById(int $id): ?array
    {
        $sql = "SELECT cc.*, b.bank_name, b.bank_code, b.color as bank_color
                FROM credit_cards cc
                LEFT JOIN banks b ON cc.bank_id = b.id
                WHERE cc.id = ? AND cc.is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);

        $card = $stmt->fetch();
        return $card ?: null;
    }
    
    /**
     * Obtener todas las tarjetas de un usuario
     */
    public function findByUserId(int $userId, bool $includeInactive = false): array
    {
        if ($includeInactive) {
            $sql = "SELECT cc.*, b.bank_name, b.bank_code, b.color as bank_color
                    FROM credit_cards cc
                    LEFT JOIN banks b ON cc.bank_id = b.id
                    WHERE cc.user_id = ?
                    ORDER BY cc.status, cc.card_name";
        } else {
            $sql = "SELECT cc.*, b.bank_name, b.bank_code, b.color as bank_color
                    FROM credit_cards cc
                    LEFT JOIN banks b ON cc.bank_id = b.id
                    WHERE cc.user_id = ? AND cc.status IN ('active', 'blocked')
                    ORDER BY cc.card_name";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);

        return $stmt->fetchAll();
    }
    
    /**
     * Actualizar tarjeta de crédito
     */
    public function update(int $id, array $data): bool
    {
        $sql = "UPDATE credit_cards SET
            card_name = ?, bank_id = ?, card_type = ?, card_number_last4 = ?,
            credit_limit = ?, management_fee = ?, expiry_date = ?, cut_off_day = ?,
            payment_due_days = ?, cvv = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['card_name'],
            $data['bank_id'],
            $data['card_type'] ?? 'visa',
            $data['card_number_last4'] ?? null,
            $data['credit_limit'],
            $data['management_fee'] ?? 0.00,
            $data['expiry_date'],
            $data['cut_off_day'],
            $data['payment_due_days'],
            $data['cvv'] ?? null,
            $data['description'] ?? null,
            $id
        ]);
    }
    
    /**
     * Cambiar estado de tarjeta
     */
    public function changeStatus(int $id, string $status, string $reason = null): bool
    {
        $sql = "UPDATE credit_cards SET
            status = ?,
            status_changed_at = CURRENT_TIMESTAMP,
            status_reason = ?,
            is_active = CASE WHEN ? IN ('active', 'blocked') THEN 1 ELSE 0 END,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$status, $reason, $status, $id]);
    }

    /**
     * Cancelar tarjeta
     */
    public function cancel(int $id, string $reason = 'Cancelada por el usuario'): bool
    {
        return $this->changeStatus($id, 'cancelled', $reason);
    }

    /**
     * Bloquear tarjeta
     */
    public function block(int $id, string $reason = 'Bloqueada por seguridad'): bool
    {
        return $this->changeStatus($id, 'blocked', $reason);
    }

    /**
     * Activar tarjeta
     */
    public function activate(int $id, string $reason = 'Reactivada'): bool
    {
        return $this->changeStatus($id, 'active', $reason);
    }

    /**
     * Marcar como vencida
     */
    public function markAsExpired(int $id): bool
    {
        return $this->changeStatus($id, 'expired', 'Tarjeta vencida automáticamente');
    }

    /**
     * Eliminar tarjeta (mantener por compatibilidad)
     */
    public function delete(int $id): bool
    {
        return $this->cancel($id, 'Eliminada por el usuario');
    }
    
    /**
     * Calcular saldo actual de la tarjeta
     */
    public function getCurrentBalance(int $cardId): float
    {
        // Sumar todas las transacciones (cargos positivos)
        $sql = "SELECT COALESCE(SUM(amount), 0) as total_charges 
                FROM credit_card_transactions 
                WHERE credit_card_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId]);
        $charges = $stmt->fetch()['total_charges'];
        
        // Restar todos los pagos
        $sql = "SELECT COALESCE(SUM(amount), 0) as total_payments 
                FROM credit_card_payments 
                WHERE credit_card_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId]);
        $payments = $stmt->fetch()['total_payments'];
        
        return (float) ($charges - $payments);
    }
    
    /**
     * Obtener cupo disponible
     */
    public function getAvailableCredit(int $cardId): float
    {
        $card = $this->findById($cardId);
        if (!$card) {
            return 0.0;
        }
        
        $currentBalance = $this->getCurrentBalance($cardId);
        return (float) ($card['credit_limit'] - $currentBalance);
    }
    
    /**
     * Obtener próxima fecha de corte
     */
    public function getNextCutOffDate(int $cardId): ?string
    {
        $card = $this->findById($cardId);
        if (!$card) {
            return null;
        }
        
        $cutOffDay = (int) $card['cut_off_day'];
        $today = new DateTime();
        $currentMonth = $today->format('Y-m');
        
        // Crear fecha de corte para el mes actual
        $cutOffDate = new DateTime($currentMonth . '-' . str_pad((string)$cutOffDay, 2, '0', STR_PAD_LEFT));
        
        // Si ya pasó la fecha de corte de este mes, usar el próximo mes
        if ($cutOffDate <= $today) {
            $cutOffDate->modify('+1 month');
        }
        
        return $cutOffDate->format('Y-m-d');
    }
    
    /**
     * Obtener próxima fecha de pago
     */
    public function getNextPaymentDate(int $cardId): ?string
    {
        $card = $this->findById($cardId);
        if (!$card) {
            return null;
        }
        
        $nextCutOff = $this->getNextCutOffDate($cardId);
        if (!$nextCutOff) {
            return null;
        }
        
        $cutOffDate = new DateTime($nextCutOff);
        $paymentDueDays = (int) $card['payment_due_days'];
        $cutOffDate->modify("+{$paymentDueDays} days");
        
        return $cutOffDate->format('Y-m-d');
    }
    
    /**
     * Obtener resumen financiero de la tarjeta
     */
    public function getFinancialSummary(int $cardId): array
    {
        $card = $this->findById($cardId);
        if (!$card) {
            return [];
        }
        
        $currentBalance = $this->getCurrentBalance($cardId);
        $availableCredit = $this->getAvailableCredit($cardId);
        $nextCutOff = $this->getNextCutOffDate($cardId);
        $nextPayment = $this->getNextPaymentDate($cardId);
        
        // Gastos del mes actual
        $sql = "SELECT COALESCE(SUM(amount), 0) as monthly_spending 
                FROM credit_card_transactions 
                WHERE credit_card_id = ? 
                AND YEAR(transaction_date) = YEAR(CURDATE()) 
                AND MONTH(transaction_date) = MONTH(CURDATE())";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId]);
        $monthlySpending = $stmt->fetch()['monthly_spending'];
        
        return [
            'card' => $card,
            'current_balance' => $currentBalance,
            'available_credit' => $availableCredit,
            'credit_utilization' => $card['credit_limit'] > 0 ? ($currentBalance / $card['credit_limit']) * 100 : 0,
            'monthly_spending' => (float) $monthlySpending,
            'next_cut_off_date' => $nextCutOff,
            'next_payment_date' => $nextPayment
        ];
    }
    
    /**
     * Verificar si la tarjeta está próxima a vencer
     */
    public function isExpiringSoon(int $cardId, int $monthsAhead = 3): bool
    {
        $card = $this->findById($cardId);
        if (!$card) {
            return false;
        }
        
        $expiryDate = new DateTime($card['expiry_date']);
        $checkDate = new DateTime();
        $checkDate->modify("+{$monthsAhead} months");
        
        return $expiryDate <= $checkDate;
    }
    
    /**
     * Obtener estadísticas de uso por categoría
     */
    public function getSpendingByCategory(int $cardId, string $startDate = null, string $endDate = null): array
    {
        $whereClause = "WHERE cct.credit_card_id = ?";
        $params = [$cardId];
        
        if ($startDate) {
            $whereClause .= " AND cct.transaction_date >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $whereClause .= " AND cct.transaction_date <= ?";
            $params[] = $endDate;
        }
        
        $sql = "SELECT 
            c.name as category_name,
            COALESCE(SUM(cct.amount), 0) as total_amount,
            COUNT(cct.id) as transaction_count
        FROM credit_card_transactions cct
        LEFT JOIN categories c ON cct.category_id = c.id
        {$whereClause}
        GROUP BY c.id, c.name
        ORDER BY total_amount DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }

    /**
     * Verificar y actualizar tarjetas vencidas
     */
    public function checkAndUpdateExpiredCards(): int
    {
        $sql = "UPDATE credit_cards
                SET status = 'expired',
                    status_changed_at = CURRENT_TIMESTAMP,
                    status_reason = 'Tarjeta vencida automáticamente',
                    is_active = 0,
                    updated_at = CURRENT_TIMESTAMP
                WHERE expiry_date <= CURDATE()
                AND status = 'active'";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();

        return $stmt->rowCount();
    }

    /**
     * Obtener tarjetas por estado
     */
    public function findByStatus(int $userId, string $status): array
    {
        $sql = "SELECT * FROM credit_cards WHERE user_id = ? AND status = ? ORDER BY card_name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $status]);

        return $stmt->fetchAll();
    }

    /**
     * Obtener tarjetas próximas a vencer
     */
    public function getExpiringCards(int $userId, int $daysAhead = 30): array
    {
        $sql = "SELECT * FROM credit_cards
                WHERE user_id = ?
                AND status = 'active'
                AND expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
                ORDER BY expiry_date ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $daysAhead]);

        return $stmt->fetchAll();
    }

    /**
     * Obtener estadísticas de estados
     */
    public function getStatusStats(int $userId): array
    {
        $sql = "SELECT
                    status,
                    COUNT(*) as count,
                    SUM(credit_limit) as total_limit
                FROM credit_cards
                WHERE user_id = ?
                GROUP BY status
                ORDER BY status";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);

        return $stmt->fetchAll();
    }
}
