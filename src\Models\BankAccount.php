<?php

namespace ControlGastos\Models;

use PDO;

class BankAccount
{
    private $db;

    public function __construct(PDO $db)
    {
        $this->db = $db;
    }

    /**
     * Crear una nueva cuenta bancaria
     */
    public function create(array $data): int
    {
        $sql = "INSERT INTO bank_accounts (
            user_id, account_name, bank_id, account_number, debit_card_number,
            account_type, initial_balance, current_balance, description
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['user_id'],
            $data['account_name'],
            $data['bank_id'],
            $data['account_number'] ?? null,
            $data['debit_card_number'] ?? null,
            $data['account_type'] ?? 'savings',
            $data['initial_balance'] ?? 0.00,
            $data['initial_balance'] ?? 0.00, // current_balance = initial_balance al crear
            $data['description'] ?? null
        ]);
        
        return (int) $this->db->lastInsertId();
    }

    /**
     * Obtener cuenta por ID
     */
    public function findById(int $id): ?array
    {
        $sql = "SELECT * FROM bank_accounts WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        
        $result = $stmt->fetch();
        return $result ?: null;
    }

    /**
     * Obtener cuentas por usuario
     */
    public function findByUserId(int $userId, bool $includeInactive = false): array
    {
        if ($includeInactive) {
            $sql = "SELECT * FROM bank_accounts WHERE user_id = ? ORDER BY status, account_name";
        } else {
            $sql = "SELECT * FROM bank_accounts WHERE user_id = ? AND status = 'active' ORDER BY account_name";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        
        return $stmt->fetchAll();
    }

    /**
     * Actualizar cuenta bancaria
     */
    public function update(int $id, array $data): bool
    {
        $sql = "UPDATE bank_accounts SET
            account_name = ?, bank_id = ?, account_number = ?, debit_card_number = ?,
            account_type = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['account_name'],
            $data['bank_id'],
            $data['account_number'] ?? null,
            $data['debit_card_number'] ?? null,
            $data['account_type'] ?? 'savings',
            $data['description'] ?? null,
            $id
        ]);
    }

    /**
     * Cambiar estado de cuenta
     */
    public function changeStatus(int $id, string $status): bool
    {
        $sql = "UPDATE bank_accounts SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$status, $id]);
    }

    /**
     * Agregar movimiento a la cuenta
     */
    public function addMovement(array $data): int
    {
        // Obtener saldo actual
        $account = $this->findById($data['bank_account_id']);
        if (!$account) {
            throw new \Exception('Cuenta bancaria no encontrada');
        }

        $currentBalance = (float) $account['current_balance'];
        
        // Calcular nuevo saldo
        if (in_array($data['movement_type'], ['deposit', 'transfer_in', 'interest'])) {
            $newBalance = $currentBalance + $data['amount'];
        } else {
            $newBalance = $currentBalance - $data['amount'];
            
            // Verificar fondos suficientes para retiros
            if ($newBalance < 0 && $data['movement_type'] === 'withdrawal') {
                throw new \Exception('Fondos insuficientes');
            }
        }

        // Insertar movimiento
        $sql = "INSERT INTO bank_account_movements (
            bank_account_id, user_id, movement_type, amount, description,
            reference, movement_date, balance_after
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['bank_account_id'],
            $data['user_id'],
            $data['movement_type'],
            $data['amount'],
            $data['description'],
            $data['reference'] ?? null,
            $data['movement_date'] ?? date('Y-m-d'),
            $newBalance
        ]);

        $movementId = (int) $this->db->lastInsertId();

        // Actualizar saldo de la cuenta
        $this->updateBalance($data['bank_account_id'], $newBalance);

        return $movementId;
    }

    /**
     * Actualizar saldo de cuenta
     */
    private function updateBalance(int $accountId, float $newBalance): bool
    {
        $sql = "UPDATE bank_accounts SET current_balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$newBalance, $accountId]);
    }

    /**
     * Obtener movimientos de una cuenta
     */
    public function getMovements(int $accountId, int $limit = 50, int $offset = 0): array
    {
        $sql = "SELECT * FROM bank_account_movements 
                WHERE bank_account_id = ? 
                ORDER BY movement_date DESC, id DESC 
                LIMIT ? OFFSET ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$accountId, $limit, $offset]);
        
        return $stmt->fetchAll();
    }

    /**
     * Obtener resumen financiero de una cuenta
     */
    public function getFinancialSummary(int $accountId): array
    {
        $account = $this->findById($accountId);
        if (!$account) {
            throw new \Exception('Cuenta no encontrada');
        }

        // Calcular totales del mes actual
        $sql = "SELECT 
            SUM(CASE WHEN movement_type IN ('deposit', 'transfer_in', 'interest') THEN amount ELSE 0 END) as total_deposits,
            SUM(CASE WHEN movement_type IN ('withdrawal', 'transfer_out', 'fee') THEN amount ELSE 0 END) as total_withdrawals,
            COUNT(*) as total_movements
        FROM bank_account_movements 
        WHERE bank_account_id = ? 
        AND MONTH(movement_date) = MONTH(CURDATE()) 
        AND YEAR(movement_date) = YEAR(CURDATE())";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$accountId]);
        $monthlyStats = $stmt->fetch();

        return [
            'account' => $account,
            'current_balance' => (float) $account['current_balance'],
            'initial_balance' => (float) $account['initial_balance'],
            'monthly_deposits' => (float) ($monthlyStats['total_deposits'] ?? 0),
            'monthly_withdrawals' => (float) ($monthlyStats['total_withdrawals'] ?? 0),
            'monthly_movements' => (int) ($monthlyStats['total_movements'] ?? 0),
            'net_change' => (float) $account['current_balance'] - (float) $account['initial_balance']
        ];
    }

    /**
     * Obtener estadísticas de cuentas por usuario
     */
    public function getUserAccountsStats(int $userId): array
    {
        $sql = "SELECT 
            COUNT(*) as total_accounts,
            SUM(current_balance) as total_balance,
            SUM(CASE WHEN status = 'active' THEN current_balance ELSE 0 END) as active_balance,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_accounts
        FROM bank_accounts 
        WHERE user_id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        
        return $stmt->fetch();
    }

    /**
     * Transferir entre cuentas
     */
    public function transfer(int $fromAccountId, int $toAccountId, float $amount, string $description, int $userId): bool
    {
        $this->db->beginTransaction();
        
        try {
            // Movimiento de salida
            $this->addMovement([
                'bank_account_id' => $fromAccountId,
                'user_id' => $userId,
                'movement_type' => 'transfer_out',
                'amount' => $amount,
                'description' => $description . ' (Transferencia enviada)',
                'related_account_id' => $toAccountId
            ]);

            // Movimiento de entrada
            $this->addMovement([
                'bank_account_id' => $toAccountId,
                'user_id' => $userId,
                'movement_type' => 'transfer_in',
                'amount' => $amount,
                'description' => $description . ' (Transferencia recibida)',
                'related_account_id' => $fromAccountId
            ]);

            $this->db->commit();
            return true;
            
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
}
