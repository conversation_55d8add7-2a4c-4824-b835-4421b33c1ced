# 🧪 **Guía de Pruebas Locales con XAMPP**

## 📋 **REQUISITOS PREVIOS**

### ✅ **Verificar XAMPP**
1. **Iniciar XAMPP Control Panel**
2. **Activar servicios:**
   - ✅ Apache (puerto 80)
   - ✅ MySQL (puerto 3306)
3. **Verificar estado:** Ambos deben mostrar "Running" en verde

### ✅ **Verificar PHP**
- **Versión mínima:** PHP 8.0+
- **Extensiones requeridas:** PDO, PDO_MySQL, OpenSSL, mbstring, JSON

---

## 🚀 **PASO A PASO PARA PRUEBAS**

### **PASO 1: Verificar Estructura de Archivos**

Asegúrate de que tienes esta estructura en `C:\xampp\htdocs\controlGastos\`:

```
controlGastos/
├── .env                     ✅ (creado)
├── setup-local.php         ✅ (creado)
├── create-admin-local.php   ✅ (creado)
├── src/
│   ├── bootstrap.php        ✅ (creado)
│   ├── Controllers/
│   ├── Services/
│   ├── Core/
│   └── Models/
├── public/
│   ├── index.php           ✅ (actualizado)
│   └── assets/
├── templates/
├── database/
│   └── migrations/
├── logs/                   ✅ (creado)
└── storage/                ✅ (creado)
```

### **PASO 2: Configuración Inicial**

#### **2.1 Abrir navegador y ir a:**
```
http://localhost/controlGastos/setup-local.php
```

#### **2.2 Seguir los pasos en pantalla:**
1. ✅ **Verificar XAMPP** - Debe mostrar todo en verde
2. 🔧 **Crear Base de Datos** - Clic en "Crear Base de Datos"
3. 📊 **Ejecutar Migraciones** - Clic en "Ejecutar Migraciones"
4. 👤 **Crear Usuario Admin** - Clic en "Crear Usuario Admin"

### **PASO 3: Crear Usuario Administrador**

#### **3.1 Ir a:**
```
http://localhost/controlGastos/create-admin-local.php
```

#### **3.2 Completar formulario:**
- **Nombre:** Admin
- **Apellido:** Local
- **Email:** admin@localhost
- **Contraseña:** admin123 (o la que prefieras, mínimo 8 caracteres)

#### **3.3 Hacer clic en "Crear Usuario Administrador"**

### **PASO 4: Acceder a la Aplicación**

#### **4.1 Ir a la aplicación:**
```
http://localhost/controlGastos/public/
```

#### **4.2 Iniciar sesión:**
- **Email:** admin@localhost
- **Contraseña:** admin123

---

## 🧪 **PRUEBAS FUNCIONALES**

### **PRUEBA 1: Login y Dashboard**

1. **Acceder a:** `http://localhost/controlGastos/public/`
2. **Iniciar sesión** con las credenciales creadas
3. **Verificar:** Debe redirigir al dashboard
4. **Comprobar:** Dashboard muestra información básica

### **PRUEBA 2: Gestión de Cuentas**

1. **Ir a:** `http://localhost/controlGastos/public/?route=accounts`
2. **Verificar:** Debe mostrar cuenta "Efectivo" creada por defecto
3. **Crear nueva cuenta:**
   - Nombre: "Banco Principal"
   - Tipo: "Cuenta Bancaria"
   - Balance inicial: 10000
4. **Verificar:** Cuenta aparece en la lista

### **PRUEBA 3: Gestión de Categorías**

1. **Ir a:** `http://localhost/controlGastos/public/?route=categories`
2. **Verificar:** Debe mostrar categorías por defecto creadas
3. **Crear nueva categoría:**
   - Nombre: "Prueba Local"
   - Color: #FF5733
   - Icono: fas fa-test
4. **Verificar:** Categoría aparece en la lista

### **PRUEBA 4: Gestión de Transacciones**

1. **Ir a:** `http://localhost/controlGastos/public/?route=transactions`
2. **Crear nueva transacción:**
   - Tipo: Gasto
   - Monto: 500
   - Categoría: Alimentación
   - Cuenta: Efectivo
   - Descripción: "Compra de prueba"
3. **Verificar:** Transacción aparece en la lista

### **PRUEBA 5: Reportes**

1. **Ir a:** `http://localhost/controlGastos/public/?route=reports`
2. **Verificar:** Página de reportes carga correctamente
3. **Comprobar:** Gráficos y estadísticas se muestran

---

## 🔧 **SOLUCIÓN DE PROBLEMAS**

### **Error: "Base de datos no encontrada"**
```bash
Solución:
1. Verificar que MySQL esté corriendo en XAMPP
2. Ir a http://localhost/phpmyadmin
3. Crear base de datos 'control_gastos' manualmente
4. Ejecutar setup-local.php nuevamente
```

### **Error: "Clase no encontrada"**
```bash
Solución:
1. Verificar que todos los archivos estén en su lugar
2. Comprobar que src/bootstrap.php existe
3. Revisar permisos de archivos
```

### **Error: "No se puede escribir en logs/"**
```bash
Solución:
1. Crear directorio: mkdir logs storage/cache storage/sessions
2. Dar permisos de escritura (en Windows no es necesario)
```

### **Error 500 - Internal Server Error**
```bash
Solución:
1. Verificar logs en: logs/app.log
2. Comprobar versión de PHP (debe ser 8.0+)
3. Verificar que todas las extensiones estén instaladas
```

---

## 📊 **VERIFICACIÓN DE FUNCIONALIDADES**

### **Checklist de Pruebas:**

#### **✅ Autenticación:**
- [ ] Login funciona
- [ ] Logout funciona
- [ ] Sesión se mantiene
- [ ] Redirección correcta

#### **✅ Dashboard:**
- [ ] Carga sin errores
- [ ] Muestra resumen financiero
- [ ] Gráficos se renderizan
- [ ] Navegación funciona

#### **✅ Cuentas:**
- [ ] Lista de cuentas
- [ ] Crear cuenta
- [ ] Editar cuenta
- [ ] Balance se actualiza

#### **✅ Categorías:**
- [ ] Lista de categorías
- [ ] Crear categoría
- [ ] Editar categoría
- [ ] Colores e iconos

#### **✅ Transacciones:**
- [ ] Lista de transacciones
- [ ] Crear transacción
- [ ] Editar transacción
- [ ] Filtros funcionan

#### **✅ Reportes:**
- [ ] Página carga
- [ ] Gráficos se muestran
- [ ] Datos son correctos
- [ ] Filtros funcionan

---

## 🌐 **URLs de Prueba**

### **Principales:**
- **Inicio:** `http://localhost/controlGastos/public/`
- **Login:** `http://localhost/controlGastos/public/?route=auth/login`
- **Dashboard:** `http://localhost/controlGastos/public/?route=dashboard`

### **Módulos:**
- **Cuentas:** `http://localhost/controlGastos/public/?route=accounts`
- **Categorías:** `http://localhost/controlGastos/public/?route=categories`
- **Transacciones:** `http://localhost/controlGastos/public/?route=transactions`
- **Reportes:** `http://localhost/controlGastos/public/?route=reports`

### **Herramientas:**
- **phpMyAdmin:** `http://localhost/phpmyadmin`
- **XAMPP Panel:** `http://localhost/xampp`
- **Configuración:** `http://localhost/controlGastos/setup-local.php`

---

## 📝 **DATOS DE PRUEBA**

### **Usuario Administrador:**
- **Email:** admin@localhost
- **Contraseña:** admin123
- **Rol:** Administrador

### **Base de Datos:**
- **Host:** localhost
- **Usuario:** root
- **Contraseña:** (vacía)
- **Base de Datos:** control_gastos

### **Categorías por Defecto:**
- Alimentación (🍽️ #FF6B6B)
- Transporte (🚗 #4ECDC4)
- Entretenimiento (🎮 #45B7D1)
- Salud (❤️ #96CEB4)
- Educación (🎓 #FFEAA7)
- Hogar (🏠 #DDA0DD)
- Trabajo (💼 #98D8C8)
- Otros (⋯ #F7DC6F)

---

## 🎯 **PRÓXIMOS PASOS**

Una vez que todas las pruebas funcionen correctamente:

1. **✅ Explorar todas las funcionalidades**
2. **📊 Crear datos de prueba realistas**
3. **🔧 Personalizar configuraciones**
4. **📱 Probar en diferentes dispositivos**
5. **🚀 Preparar para producción**

---

## 📞 **Soporte**

Si encuentras problemas:

1. **Revisar logs:** `logs/app.log`
2. **Verificar configuración:** `.env`
3. **Comprobar base de datos:** phpMyAdmin
4. **Reiniciar servicios:** XAMPP Control Panel

¡Disfruta probando tu aplicación de Control de Gastos! 🎉
