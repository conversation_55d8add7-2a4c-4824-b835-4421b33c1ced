<?php

declare(strict_types=1);

namespace ControlGastos\Models;

/**
 * Modelo de Categoría
 * Representa una categoría principal para clasificación de transacciones
 */
class Category
{
    private ?int $id = null;
    private int $userId;
    private string $name;
    private ?string $description = null;
    private string $color = '#007bff';
    private string $icon = 'fas fa-folder';
    private bool $isActive = true;
    private \DateTime $createdAt;
    private \DateTime $updatedAt;
    private array $subcategories = [];

    // Colores predefinidos para categorías
    public const COLORS = [
        '#007bff' => 'Azul',
        '#28a745' => 'Verde',
        '#dc3545' => 'Rojo',
        '#ffc107' => 'Amarillo',
        '#17a2b8' => 'Cian',
        '#6f42c1' => 'Púrpura',
        '#fd7e14' => 'Naranja',
        '#20c997' => 'Verde Azulado',
        '#6c757d' => 'Gris',
        '#343a40' => '<PERSON><PERSON> Oscuro'
    ];

    // Iconos predefinidos para categorías
    public const ICONS = [
        'fas fa-home' => 'Hogar',
        'fas fa-utensils' => 'Alimentación',
        'fas fa-car' => 'Transporte',
        'fas fa-heartbeat' => 'Salud',
        'fas fa-gamepad' => 'Entretenimiento',
        'fas fa-graduation-cap' => 'Educación',
        'fas fa-shopping-cart' => 'Compras',
        'fas fa-plane' => 'Viajes',
        'fas fa-money-bill-wave' => 'Ingresos',
        'fas fa-credit-card' => 'Servicios',
        'fas fa-tools' => 'Mantenimiento',
        'fas fa-gift' => 'Regalos',
        'fas fa-dumbbell' => 'Deportes',
        'fas fa-book' => 'Libros',
        'fas fa-music' => 'Música',
        'fas fa-camera' => 'Fotografía',
        'fas fa-laptop' => 'Tecnología',
        'fas fa-tshirt' => 'Ropa',
        'fas fa-paw' => 'Mascotas',
        'fas fa-folder' => 'General'
    ];

    public function __construct(array $data = [])
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    /**
     * Llenar modelo con datos
     */
    public function fill(array $data): void
    {
        if (isset($data['id'])) {
            $this->id = (int) $data['id'];
        }
        
        if (isset($data['user_id'])) {
            $this->userId = (int) $data['user_id'];
        }
        
        if (isset($data['name'])) {
            $this->name = trim($data['name']);
        }
        
        if (isset($data['description'])) {
            $this->description = $data['description'] ? trim($data['description']) : null;
        }
        
        if (isset($data['color'])) {
            $this->color = $data['color'];
        }
        
        if (isset($data['icon'])) {
            $this->icon = $data['icon'];
        }
        
        if (isset($data['is_active'])) {
            $this->isActive = (bool) $data['is_active'];
        }
        
        if (isset($data['created_at'])) {
            $this->createdAt = new \DateTime($data['created_at']);
        }
        
        if (isset($data['updated_at'])) {
            $this->updatedAt = new \DateTime($data['updated_at']);
        }
    }

    /**
     * Convertir a array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->userId,
            'name' => $this->name,
            'description' => $this->description,
            'color' => $this->color,
            'icon' => $this->icon,
            'is_active' => $this->isActive,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Convertir a array con información adicional
     */
    public function toDetailedArray(): array
    {
        return array_merge($this->toArray(), [
            'color_label' => self::COLORS[$this->color] ?? 'Personalizado',
            'icon_label' => self::ICONS[$this->icon] ?? 'Personalizado',
            'status_label' => $this->isActive ? 'Activa' : 'Inactiva',
            'subcategories_count' => count($this->subcategories),
            'subcategories' => array_map(fn($sub) => $sub->toArray(), $this->subcategories)
        ]);
    }

    /**
     * Validar datos de la categoría
     */
    public function validate(): array
    {
        $errors = [];

        // Validar nombre
        if (empty($this->name)) {
            $errors['name'] = 'El nombre de la categoría es requerido';
        } elseif (strlen($this->name) < 2) {
            $errors['name'] = 'El nombre debe tener al menos 2 caracteres';
        } elseif (strlen($this->name) > 100) {
            $errors['name'] = 'El nombre no puede exceder 100 caracteres';
        }

        // Validar color
        if (empty($this->color)) {
            $errors['color'] = 'El color es requerido';
        } elseif (!preg_match('/^#[0-9A-Fa-f]{6}$/', $this->color)) {
            $errors['color'] = 'El color debe ser un código hexadecimal válido';
        }

        // Validar icono
        if (empty($this->icon)) {
            $errors['icon'] = 'El icono es requerido';
        }

        // Validar descripción
        if ($this->description && strlen($this->description) > 500) {
            $errors['description'] = 'La descripción no puede exceder 500 caracteres';
        }

        // Validar user_id
        if (empty($this->userId)) {
            $errors['user_id'] = 'El ID de usuario es requerido';
        }

        return $errors;
    }

    /**
     * Verificar si la categoría es válida
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }

    /**
     * Activar categoría
     */
    public function activate(): void
    {
        $this->isActive = true;
        $this->updatedAt = new \DateTime();
    }

    /**
     * Desactivar categoría
     */
    public function deactivate(): void
    {
        $this->isActive = false;
        $this->updatedAt = new \DateTime();
    }

    /**
     * Agregar subcategoría
     */
    public function addSubcategory(Subcategory $subcategory): void
    {
        $this->subcategories[] = $subcategory;
    }

    /**
     * Establecer subcategorías
     */
    public function setSubcategories(array $subcategories): void
    {
        $this->subcategories = $subcategories;
    }

    /**
     * Obtener subcategorías activas
     */
    public function getActiveSubcategories(): array
    {
        return array_filter($this->subcategories, fn($sub) => $sub->isActive());
    }

    /**
     * Verificar si tiene subcategorías
     */
    public function hasSubcategories(): bool
    {
        return !empty($this->subcategories);
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getUserId(): int { return $this->userId; }
    public function getName(): string { return $this->name; }
    public function getDescription(): ?string { return $this->description; }
    public function getColor(): string { return $this->color; }
    public function getIcon(): string { return $this->icon; }
    public function isActive(): bool { return $this->isActive; }
    public function getCreatedAt(): \DateTime { return $this->createdAt; }
    public function getUpdatedAt(): \DateTime { return $this->updatedAt; }
    public function getSubcategories(): array { return $this->subcategories; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setUserId(int $userId): void { $this->userId = $userId; }
    public function setName(string $name): void { $this->name = trim($name); $this->updatedAt = new \DateTime(); }
    public function setDescription(?string $description): void { $this->description = $description ? trim($description) : null; $this->updatedAt = new \DateTime(); }
    public function setColor(string $color): void { $this->color = $color; $this->updatedAt = new \DateTime(); }
    public function setIcon(string $icon): void { $this->icon = $icon; $this->updatedAt = new \DateTime(); }
    public function setIsActive(bool $isActive): void { $this->isActive = $isActive; $this->updatedAt = new \DateTime(); }
    public function setUpdatedAt(\DateTime $updatedAt): void { $this->updatedAt = $updatedAt; }

    /**
     * Obtener colores disponibles
     */
    public static function getAvailableColors(): array
    {
        return self::COLORS;
    }

    /**
     * Obtener iconos disponibles
     */
    public static function getAvailableIcons(): array
    {
        return self::ICONS;
    }
}
