<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Services\ReportService;
use ControlGastos\Core\Session;

/**
 * Controlador de reportes
 * Maneja todas las rutas relacionadas con reportes y análisis
 */
class ReportController
{
    private Container $container;
    private ReportService $reportService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->reportService = $container->get('reportService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Dashboard principal de reportes
     */
    public function index(): string
    {
        // Obtener parámetros de fecha
        $startDate = !empty($_GET['start_date']) ? new \DateTime($_GET['start_date']) : null;
        $endDate = !empty($_GET['end_date']) ? new \DateTime($_GET['end_date']) : null;

        // Generar reporte financiero general
        $financialSummary = $this->reportService->generateFinancialSummary($this->userId, $startDate, $endDate);
        
        // Generar reporte de categorías
        $categoryReport = $this->reportService->generateCategoryExpenseReport($this->userId, $startDate, $endDate);
        
        // Generar reporte de cuentas
        $accountReport = $this->reportService->generateAccountReport($this->userId);
        
        // Generar reporte de tendencias
        $trendReport = $this->reportService->generateTrendReport($this->userId, 'monthly', 6);

        $data = [
            'title' => 'Reportes y Análisis',
            'financial_summary' => $financialSummary['success'] ? $financialSummary : null,
            'category_report' => $categoryReport['success'] ? $categoryReport : null,
            'account_report' => $accountReport['success'] ? $accountReport : null,
            'trend_report' => $trendReport['success'] ? $trendReport : null,
            'start_date' => $startDate?->format('Y-m-d') ?? '',
            'end_date' => $endDate?->format('Y-m-d') ?? '',
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reports/index', $data);
    }

    /**
     * Reporte de flujo de efectivo
     */
    public function cashFlow(): string
    {
        $year = (int) ($_GET['year'] ?? date('Y'));
        $month = !empty($_GET['month']) ? (int) $_GET['month'] : null;

        $cashFlowReport = $this->reportService->generateCashFlowReport($this->userId, $year, $month);

        $data = [
            'title' => 'Flujo de Efectivo',
            'cash_flow_report' => $cashFlowReport['success'] ? $cashFlowReport : null,
            'year' => $year,
            'month' => $month,
            'available_years' => range(date('Y') - 5, date('Y')),
            'months' => [
                1 => 'Enero', 2 => 'Febrero', 3 => 'Marzo', 4 => 'Abril',
                5 => 'Mayo', 6 => 'Junio', 7 => 'Julio', 8 => 'Agosto',
                9 => 'Septiembre', 10 => 'Octubre', 11 => 'Noviembre', 12 => 'Diciembre'
            ],
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reports/cash-flow', $data);
    }

    /**
     * Reporte de gastos por categoría
     */
    public function categoryExpenses(): string
    {
        $startDate = !empty($_GET['start_date']) ? new \DateTime($_GET['start_date']) : null;
        $endDate = !empty($_GET['end_date']) ? new \DateTime($_GET['end_date']) : null;

        $categoryReport = $this->reportService->generateCategoryExpenseReport($this->userId, $startDate, $endDate);

        $data = [
            'title' => 'Gastos por Categoría',
            'category_report' => $categoryReport['success'] ? $categoryReport : null,
            'start_date' => $startDate?->format('Y-m-d') ?? '',
            'end_date' => $endDate?->format('Y-m-d') ?? '',
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reports/category-expenses', $data);
    }

    /**
     * Reporte de tendencias
     */
    public function trends(): string
    {
        $period = $_GET['period'] ?? 'monthly';
        $periods = (int) ($_GET['periods'] ?? 12);

        $trendReport = $this->reportService->generateTrendReport($this->userId, $period, $periods);

        $data = [
            'title' => 'Análisis de Tendencias',
            'trend_report' => $trendReport['success'] ? $trendReport : null,
            'period' => $period,
            'periods' => $periods,
            'period_options' => [
                'daily' => 'Diario',
                'weekly' => 'Semanal',
                'monthly' => 'Mensual'
            ],
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reports/trends', $data);
    }

    /**
     * Reporte de presupuesto
     */
    public function budget(): string
    {
        $startDate = !empty($_GET['start_date']) ? new \DateTime($_GET['start_date']) : null;
        $endDate = !empty($_GET['end_date']) ? new \DateTime($_GET['end_date']) : null;

        $budgetReport = $this->reportService->generateBudgetReport($this->userId, $startDate, $endDate);

        $data = [
            'title' => 'Análisis de Presupuesto',
            'budget_report' => $budgetReport['success'] ? $budgetReport : null,
            'start_date' => $startDate?->format('Y-m-d') ?? '',
            'end_date' => $endDate?->format('Y-m-d') ?? '',
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reports/budget', $data);
    }

    /**
     * Reporte de cuentas
     */
    public function accounts(): string
    {
        $accountReport = $this->reportService->generateAccountReport($this->userId);

        $data = [
            'title' => 'Reporte de Cuentas',
            'account_report' => $accountReport['success'] ? $accountReport : null,
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reports/accounts', $data);
    }

    /**
     * Obtener datos para gráficos (AJAX)
     */
    public function getChartData(): void
    {
        $chartType = $_GET['type'] ?? '';
        $params = $_GET;
        unset($params['type']);

        $result = $this->reportService->getChartData($this->userId, $chartType, $params);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Exportar reporte financiero general
     */
    public function exportFinancialSummary(): void
    {
        $startDate = !empty($_GET['start_date']) ? new \DateTime($_GET['start_date']) : null;
        $endDate = !empty($_GET['end_date']) ? new \DateTime($_GET['end_date']) : null;
        $format = $_GET['format'] ?? 'json';

        $report = $this->reportService->generateFinancialSummary($this->userId, $startDate, $endDate);

        if (!$report['success']) {
            $this->session->flash('error', 'Error al generar el reporte');
            header('Location: /reports');
            exit;
        }

        $filename = 'reporte_financiero_' . date('Y-m-d_H-i-s');

        switch ($format) {
            case 'csv':
                $this->exportToCsv($report, $filename);
                break;
            case 'json':
            default:
                $this->exportToJson($report, $filename);
                break;
        }
    }

    /**
     * Exportar reporte de categorías
     */
    public function exportCategoryReport(): void
    {
        $startDate = !empty($_GET['start_date']) ? new \DateTime($_GET['start_date']) : null;
        $endDate = !empty($_GET['end_date']) ? new \DateTime($_GET['end_date']) : null;
        $format = $_GET['format'] ?? 'json';

        $report = $this->reportService->generateCategoryExpenseReport($this->userId, $startDate, $endDate);

        if (!$report['success']) {
            $this->session->flash('error', 'Error al generar el reporte');
            header('Location: /reports/category-expenses');
            exit;
        }

        $filename = 'reporte_categorias_' . date('Y-m-d_H-i-s');

        switch ($format) {
            case 'csv':
                $csvData = array_map(function($category) {
                    return [
                        'Categoría' => $category['category_name'],
                        'Monto' => $category['amount'],
                        'Porcentaje' => round($category['percentage'], 2) . '%',
                        'Transacciones' => $category['transaction_count']
                    ];
                }, $report['categories']);
                
                $csvContent = $this->reportService->exportToCsv($csvData, $filename);
                
                header('Content-Type: text/csv; charset=utf-8');
                header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
                echo "\xEF\xBB\xBF"; // BOM para UTF-8
                echo $csvContent;
                break;
            case 'json':
            default:
                $this->exportToJson($report, $filename);
                break;
        }
        exit;
    }

    /**
     * Exportar flujo de efectivo
     */
    public function exportCashFlow(): void
    {
        $year = (int) ($_GET['year'] ?? date('Y'));
        $month = !empty($_GET['month']) ? (int) $_GET['month'] : null;
        $format = $_GET['format'] ?? 'json';

        $report = $this->reportService->generateCashFlowReport($this->userId, $year, $month);

        if (!$report['success']) {
            $this->session->flash('error', 'Error al generar el reporte');
            header('Location: /reports/cash-flow');
            exit;
        }

        $filename = 'flujo_efectivo_' . $year . ($month ? '_' . str_pad($month, 2, '0', STR_PAD_LEFT) : '') . '_' . date('Y-m-d_H-i-s');

        switch ($format) {
            case 'csv':
                if ($month) {
                    // Reporte diario
                    $csvData = array_map(function($day) {
                        return [
                            'Fecha' => $day['date'],
                            'Día' => $day['day_name'],
                            'Ingresos' => $day['income'],
                            'Egresos' => $day['expenses'],
                            'Neto' => $day['net'],
                            'Transacciones' => $day['transactions']
                        ];
                    }, $report['data']);
                } else {
                    // Reporte mensual
                    $csvData = array_map(function($month) {
                        return [
                            'Mes' => $month['month_name'],
                            'Año' => $month['year'],
                            'Ingresos' => $month['income'],
                            'Egresos' => $month['expenses'],
                            'Neto' => $month['net'],
                            'Transacciones' => $month['transactions']
                        ];
                    }, $report['data']);
                }
                
                $csvContent = $this->reportService->exportToCsv($csvData, $filename);
                
                header('Content-Type: text/csv; charset=utf-8');
                header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
                echo "\xEF\xBB\xBF"; // BOM para UTF-8
                echo $csvContent;
                break;
            case 'json':
            default:
                $this->exportToJson($report, $filename);
                break;
        }
        exit;
    }

    /**
     * Comparar períodos
     */
    public function comparePeriods(): string
    {
        $period1Start = !empty($_GET['period1_start']) ? new \DateTime($_GET['period1_start']) : new \DateTime('first day of last month');
        $period1End = !empty($_GET['period1_end']) ? new \DateTime($_GET['period1_end']) : new \DateTime('last day of last month');
        $period2Start = !empty($_GET['period2_start']) ? new \DateTime($_GET['period2_start']) : new \DateTime('first day of this month');
        $period2End = !empty($_GET['period2_end']) ? new \DateTime($_GET['period2_end']) : new \DateTime('last day of this month');

        $period1Report = $this->reportService->generateFinancialSummary($this->userId, $period1Start, $period1End);
        $period2Report = $this->reportService->generateFinancialSummary($this->userId, $period2Start, $period2End);

        // Calcular comparaciones
        $comparison = null;
        if ($period1Report['success'] && $period2Report['success']) {
            $p1 = $period1Report['summary'];
            $p2 = $period2Report['summary'];
            
            $comparison = [
                'income_change' => $p1['total_income'] > 0 ? (($p2['total_income'] - $p1['total_income']) / $p1['total_income']) * 100 : 0,
                'expense_change' => $p1['total_expenses'] > 0 ? (($p2['total_expenses'] - $p1['total_expenses']) / $p1['total_expenses']) * 100 : 0,
                'net_change' => $p2['net_balance'] - $p1['net_balance'],
                'transaction_change' => $p2['total_transactions'] - $p1['total_transactions']
            ];
        }

        $data = [
            'title' => 'Comparación de Períodos',
            'period1_report' => $period1Report['success'] ? $period1Report : null,
            'period2_report' => $period2Report['success'] ? $period2Report : null,
            'comparison' => $comparison,
            'period1_start' => $period1Start->format('Y-m-d'),
            'period1_end' => $period1End->format('Y-m-d'),
            'period2_start' => $period2Start->format('Y-m-d'),
            'period2_end' => $period2End->format('Y-m-d'),
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reports/compare-periods', $data);
    }

    /**
     * Exportar a JSON
     */
    private function exportToJson(array $data, string $filename): void
    {
        header('Content-Type: application/json; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.json"');
        
        echo json_encode([
            'export_date' => date('Y-m-d H:i:s'),
            'user_id' => $this->userId,
            'report_data' => $data
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Exportar a CSV
     */
    private function exportToCsv(array $data, string $filename): void
    {
        // Aplanar datos para CSV
        $flatData = [];
        
        if (isset($data['summary'])) {
            $flatData[] = [
                'Métrica' => 'Total Ingresos',
                'Valor' => $data['summary']['total_income']
            ];
            $flatData[] = [
                'Métrica' => 'Total Egresos',
                'Valor' => $data['summary']['total_expenses']
            ];
            $flatData[] = [
                'Métrica' => 'Balance Neto',
                'Valor' => $data['summary']['net_balance']
            ];
            $flatData[] = [
                'Métrica' => 'Total Transacciones',
                'Valor' => $data['summary']['total_transactions']
            ];
        }
        
        $csvContent = $this->reportService->exportToCsv($flatData, $filename);
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        echo "\xEF\xBB\xBF"; // BOM para UTF-8
        echo $csvContent;
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }
}
