<?php
/**
 * Test de Interfaz Web para Tarjetas de Crédito
 * Verifica que las páginas web se carguen correctamente
 */

class WebInterfaceTest
{
    private $baseUrl = 'http://localhost/controlGastos/public/';
    private $testResults = [];
    
    public function __construct()
    {
        echo "🌐 TESTING DE INTERFAZ WEB - TARJETAS DE CRÉDITO\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
    }
    
    public function runAllTests()
    {
        $this->testMainPages();
        $this->testPageContent();
        $this->testJavaScriptFunctionality();
        $this->testFormValidation();
        $this->generateReport();
    }
    
    /**
     * Test páginas principales
     */
    public function testMainPages()
    {
        echo "📄 TEST 1: PÁGINAS PRINCIPALES\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $pages = [
            '' => 'Dashboard principal',
            '?route=credit-cards' => 'Dashboard de tarjetas',
            '?route=credit-cards/create' => 'Crear nueva tarjeta'
        ];
        
        foreach ($pages as $route => $description) {
            $url = $this->baseUrl . $route;
            $this->testPageLoad($url, $description);
        }
        
        echo "\n";
    }
    
    /**
     * Test contenido de páginas
     */
    public function testPageContent()
    {
        echo "📝 TEST 2: CONTENIDO DE PÁGINAS\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        // Test dashboard de tarjetas
        $content = $this->getPageContent($this->baseUrl . '?route=credit-cards');
        if ($content) {
            $this->checkContent($content, 'Mis Tarjetas de Crédito', 'Título principal');
            $this->checkContent($content, 'Nueva Tarjeta', 'Botón de nueva tarjeta');
            $this->checkContent($content, 'fas fa-credit-card', 'Iconos FontAwesome');
            $this->checkContent($content, 'bootstrap.min.css', 'CSS Bootstrap');
        }
        
        // Test formulario de creación
        $content = $this->getPageContent($this->baseUrl . '?route=credit-cards/create');
        if ($content) {
            $this->checkContent($content, 'Nueva Tarjeta de Crédito', 'Título de creación');
            $this->checkContent($content, 'card_name', 'Campo nombre de tarjeta');
            $this->checkContent($content, 'bank_name', 'Campo banco');
            $this->checkContent($content, 'credit_limit', 'Campo cupo');
            $this->checkContent($content, 'Vista Previa', 'Sección de vista previa');
        }
        
        echo "\n";
    }
    
    /**
     * Test funcionalidad JavaScript
     */
    public function testJavaScriptFunctionality()
    {
        echo "⚡ TEST 3: FUNCIONALIDAD JAVASCRIPT\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $content = $this->getPageContent($this->baseUrl . '?route=credit-cards/create');
        if ($content) {
            $this->checkContent($content, 'updatePreview', 'Función de vista previa');
            $this->checkContent($content, 'addEventListener', 'Event listeners');
            $this->checkContent($content, 'creditCardForm', 'Referencia al formulario');
        }
        
        $content = $this->getPageContent($this->baseUrl . '?route=credit-cards');
        if ($content) {
            $this->checkContent($content, 'statusFilter', 'Filtro de estado');
            $this->checkContent($content, 'checkExpiredCards', 'Función verificar vencidas');
        }
        
        echo "\n";
    }
    
    /**
     * Test validación de formularios
     */
    public function testFormValidation()
    {
        echo "✅ TEST 4: VALIDACIÓN DE FORMULARIOS\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $content = $this->getPageContent($this->baseUrl . '?route=credit-cards/create');
        if ($content) {
            $this->checkContent($content, 'required', 'Campos obligatorios');
            $this->checkContent($content, 'pattern=', 'Patrones de validación');
            $this->checkContent($content, 'min=', 'Validación de valores mínimos');
            $this->checkContent($content, 'maxlength=', 'Validación de longitud máxima');
        }
        
        echo "\n";
    }
    
    /**
     * Verificar carga de página
     */
    private function testPageLoad($url, $description)
    {
        $startTime = microtime(true);
        $content = $this->getPageContent($url);
        $loadTime = round((microtime(true) - $startTime) * 1000, 2);
        
        if ($content !== false) {
            if (strpos($content, 'Fatal error') === false && 
                strpos($content, 'Parse error') === false &&
                strpos($content, 'Warning') === false) {
                $this->addResult("✅ {$description} carga correctamente ({$loadTime}ms)", true);
            } else {
                $this->addResult("❌ {$description} tiene errores PHP", false);
            }
        } else {
            $this->addResult("❌ {$description} no responde", false);
        }
    }
    
    /**
     * Verificar contenido específico
     */
    private function checkContent($content, $needle, $description)
    {
        if (strpos($content, $needle) !== false) {
            $this->addResult("✅ {$description} presente", true);
        } else {
            $this->addResult("❌ {$description} faltante", false);
        }
    }
    
    /**
     * Obtener contenido de página
     */
    private function getPageContent($url)
    {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET',
                'header' => "User-Agent: CreditCardTestSuite/1.0\r\n"
            ]
        ]);
        
        return @file_get_contents($url, false, $context);
    }
    
    /**
     * Agregar resultado
     */
    private function addResult($message, $success)
    {
        $this->testResults[] = ['message' => $message, 'success' => $success];
        echo $message . "\n";
    }
    
    /**
     * Generar reporte
     */
    public function generateReport()
    {
        echo "📋 REPORTE DE INTERFAZ WEB\n";
        echo "=" . str_repeat("=", 60) . "\n";
        
        $total = count($this->testResults);
        $passed = count(array_filter($this->testResults, fn($r) => $r['success']));
        $failed = $total - $passed;
        
        echo "Total de tests: {$total}\n";
        echo "✅ Exitosos: {$passed}\n";
        echo "❌ Fallidos: {$failed}\n";
        echo "📊 Porcentaje de éxito: " . round(($passed / $total) * 100, 2) . "%\n\n";
        
        if ($failed > 0) {
            echo "❌ TESTS FALLIDOS:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "   • " . $result['message'] . "\n";
                }
            }
        }
        
        echo "\n🎉 TESTING DE INTERFAZ COMPLETADO\n";
    }
}

// Ejecutar tests
try {
    $webTest = new WebInterfaceTest();
    $webTest->runAllTests();
} catch (Exception $e) {
    echo "❌ ERROR EN TESTING WEB: " . $e->getMessage() . "\n";
}
