<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Core\SimpleDatabase;
use ControlGastos\Core\Session;

/**
 * Controlador de dashboard simplificado para pruebas
 */
class SimpleDashboardController
{
    private SimpleDatabase $database;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->database = $container->get('database');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Mostrar dashboard principal
     */
    public function index(): string
    {
        try {
            // Obtener información del usuario
            $user = $this->database->selectOne(
                "SELECT * FROM users WHERE id = ?",
                [$this->userId]
            );

            // Obtener resumen básico
            $summary = $this->getFinancialSummary();
            
            // Obtener transacciones recientes si la tabla existe
            $recentTransactions = $this->getRecentTransactions();
            
            // Obtener cuentas si la tabla existe
            $accounts = $this->getAccounts();

            return $this->renderDashboard($user, $summary, $recentTransactions, $accounts);

        } catch (\Exception $e) {
            return $this->renderError('Error cargando el dashboard: ' . $e->getMessage());
        }
    }

    /**
     * Obtener resumen financiero básico
     */
    private function getFinancialSummary(): array
    {
        $summary = [
            'total_accounts' => 0,
            'total_balance' => 0,
            'total_transactions' => 0,
            'monthly_income' => 0,
            'monthly_expenses' => 0
        ];

        try {
            // Verificar si existe la tabla accounts
            $stmt = $this->database->query("SHOW TABLES LIKE 'accounts'");
            if ($stmt->rowCount() > 0) {
                $accountData = $this->database->selectOne(
                    "SELECT COUNT(*) as count, COALESCE(SUM(balance), 0) as total_balance 
                     FROM accounts WHERE user_id = ? AND is_active = 1",
                    [$this->userId]
                );
                $summary['total_accounts'] = (int) ($accountData['count'] ?? 0);
                $summary['total_balance'] = (float) ($accountData['total_balance'] ?? 0);
            }

            // Verificar si existe la tabla transactions
            $stmt = $this->database->query("SHOW TABLES LIKE 'transactions'");
            if ($stmt->rowCount() > 0) {
                $transactionData = $this->database->selectOne(
                    "SELECT COUNT(*) as count FROM transactions WHERE user_id = ?",
                    [$this->userId]
                );
                $summary['total_transactions'] = (int) ($transactionData['count'] ?? 0);

                // Obtener ingresos y gastos del mes actual
                $monthlyData = $this->database->select(
                    "SELECT type, COALESCE(SUM(amount), 0) as total 
                     FROM transactions 
                     WHERE user_id = ? AND MONTH(transaction_date) = MONTH(NOW()) AND YEAR(transaction_date) = YEAR(NOW())
                     GROUP BY type",
                    [$this->userId]
                );

                foreach ($monthlyData as $row) {
                    if ($row['type'] === 'income') {
                        $summary['monthly_income'] = (float) ($row['total'] ?? 0);
                    } elseif ($row['type'] === 'expense') {
                        $summary['monthly_expenses'] = (float) ($row['total'] ?? 0);
                    }
                }
            }

        } catch (\Exception $e) {
            // Si hay error, devolver valores por defecto
        }

        return $summary;
    }

    /**
     * Obtener transacciones recientes
     */
    private function getRecentTransactions(): array
    {
        try {
            $stmt = $this->database->query("SHOW TABLES LIKE 'transactions'");
            if ($stmt->rowCount() > 0) {
                return $this->database->select(
                    "SELECT t.*, 
                            COALESCE(c.name, 'Sin categoría') as category_name,
                            COALESCE(a.name, 'Sin cuenta') as account_name
                     FROM transactions t
                     LEFT JOIN categories c ON t.category_id = c.id
                     LEFT JOIN accounts a ON t.account_id = a.id
                     WHERE t.user_id = ?
                     ORDER BY t.created_at DESC
                     LIMIT 10",
                    [$this->userId]
                );
            }
        } catch (\Exception $e) {
            // Si hay error, devolver array vacío
        }

        return [];
    }

    /**
     * Obtener cuentas del usuario
     */
    private function getAccounts(): array
    {
        try {
            $stmt = $this->database->query("SHOW TABLES LIKE 'accounts'");
            if ($stmt->rowCount() > 0) {
                return $this->database->select(
                    "SELECT * FROM accounts WHERE user_id = ? AND is_active = 1 ORDER BY name",
                    [$this->userId]
                );
            }
        } catch (\Exception $e) {
            // Si hay error, devolver array vacío
        }

        return [];
    }

    /**
     * Renderizar dashboard
     */
    private function renderDashboard(array $user, array $summary, array $transactions, array $accounts): string
    {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Dashboard - Control de Gastos</title>
            <?= includeBasicCSS() ?>
            <style>
                body { background-color: #f8f9fa; }
                .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                .card { border: none; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
            </style>
        </head>
        <body>
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-dark">
                <div class="container">
                    <a class="navbar-brand" href="?route=dashboard">
                        <i class="fas fa-chart-line me-2"></i>Control de Gastos
                    </a>
                    <div class="navbar-nav ms-auto">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i><?= htmlspecialchars($user['first_name'] ?? 'Usuario') ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/controlGastos/public/?route=credit-cards"><i class="fas fa-credit-card me-2"></i>Tarjetas de Crédito</a></li>
                                <li><a class="dropdown-item" href="/controlGastos/public/?route=bank-accounts"><i class="fas fa-university me-2"></i>Cuentas Bancarias</a></li>
                                <li><a class="dropdown-item" href="?route=accounts"><i class="fas fa-wallet me-2"></i>Cuentas</a></li>
                                <li><a class="dropdown-item" href="?route=transactions"><i class="fas fa-exchange-alt me-2"></i>Transacciones</a></li>
                                <li><a class="dropdown-item" href="?route=categories"><i class="fas fa-tags me-2"></i>Categorías</a></li>
                                <li><a class="dropdown-item" href="?route=reports"><i class="fas fa-chart-bar me-2"></i>Reportes</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="?route=auth/logout"><i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <div class="container mt-4">
                <!-- Bienvenida -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3">¡Bienvenido, <?= htmlspecialchars($user['first_name'] ?? 'Usuario') ?>!</h1>
                        <p class="text-muted">Aquí tienes un resumen de tus finanzas</p>
                    </div>
                </div>

                <!-- Estadísticas -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-wallet fa-2x mb-2"></i>
                                <h5>Cuentas</h5>
                                <h3><?= $summary['total_accounts'] ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                                <h5>Balance Total</h5>
                                <h3>$<?= number_format((float) $summary['total_balance'], 2) ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-up fa-2x mb-2 text-success"></i>
                                <h5>Ingresos del Mes</h5>
                                <h3>$<?= number_format((float) $summary['monthly_income'], 2) ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-down fa-2x mb-2 text-danger"></i>
                                <h5>Gastos del Mes</h5>
                                <h3>$<?= number_format((float) $summary['monthly_expenses'], 2) ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- Transacciones Recientes -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Transacciones Recientes</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($transactions)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Fecha</th>
                                                    <th>Descripción</th>
                                                    <th>Categoría</th>
                                                    <th>Cuenta</th>
                                                    <th>Monto</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($transactions as $transaction): ?>
                                                    <tr>
                                                        <td><?= date('d/m/Y', strtotime($transaction['transaction_date'])) ?></td>
                                                        <td><?= htmlspecialchars($transaction['description'] ?? 'Sin descripción') ?></td>
                                                        <td><?= htmlspecialchars($transaction['category_name']) ?></td>
                                                        <td><?= htmlspecialchars($transaction['account_name']) ?></td>
                                                        <td>
                                                            <span class="badge bg-<?= $transaction['type'] === 'income' ? 'success' : 'danger' ?>">
                                                                <?= $transaction['type'] === 'income' ? '+' : '-' ?>$<?= number_format((float) $transaction['amount'], 2) ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">No hay transacciones registradas</h6>
                                        <p class="text-muted">Comienza registrando tu primera transacción</p>
                                        <a href="?route=transactions" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Nueva Transacción
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Cuentas -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-wallet me-2"></i>Mis Cuentas</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($accounts)): ?>
                                    <?php foreach ($accounts as $account): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-0"><?= htmlspecialchars($account['name']) ?></h6>
                                                <small class="text-muted"><?= ucfirst($account['type']) ?></small>
                                            </div>
                                            <div class="text-end">
                                                <span class="fw-bold">$<?= number_format((float) $account['balance'], 2) ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-wallet fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">No hay cuentas registradas</h6>
                                        <a href="?route=accounts" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus me-2"></i>Crear Cuenta
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enlaces rápidos -->
                <div class="row g-4 mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="mb-3"><i class="fas fa-bolt me-2"></i>Acciones Rápidas</h5>
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <a href="/controlGastos/public/?route=credit-cards" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-credit-card me-2"></i>Tarjetas de Crédito
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a href="/controlGastos/public/?route=bank-accounts" class="btn btn-outline-success w-100">
                                            <i class="fas fa-university me-2"></i>Cuentas Bancarias
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="?route=transactions" class="btn btn-outline-success w-100">
                                            <i class="fas fa-plus me-2"></i>Nueva Transacción
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="?route=accounts" class="btn btn-outline-warning w-100">
                                            <i class="fas fa-wallet me-2"></i>Gestionar Cuentas
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="?route=reports" class="btn btn-outline-info w-100">
                                            <i class="fas fa-chart-bar me-2"></i>Ver Reportes
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?= includeBasicJS() ?>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Renderizar página de error
     */
    private function renderError(string $message): string
    {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Error - Control de Gastos</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-5">
                <div class="alert alert-danger">
                    <h4>Error en el Dashboard</h4>
                    <p><?= htmlspecialchars($message) ?></p>
                    <a href="?route=auth/logout" class="btn btn-primary">Volver al Login</a>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }
}
