<?php
$content = ob_start();

// Calcular métricas
$net_income = ($total_income ?? 0) - ($total_expenses ?? 0);
$savings_rate = $total_income > 0 ? (($net_income / $total_income) * 100) : 0;
$expense_growth = isset($previous_expenses) && $previous_expenses > 0 
    ? ((($total_expenses - $previous_expenses) / $previous_expenses) * 100) 
    : 0;
?>

<h2>📊 Tu Reporte Financiero de <?= htmlspecialchars($month_year) ?></h2>

<p>Hola <strong><?= htmlspecialchars($user_name) ?></strong>,</p>

<p>
    Aquí tienes tu resumen financiero completo del mes de <strong><?= htmlspecialchars($month_year) ?></strong>. 
    ¡Veamos cómo te fue con tus finanzas!
</p>

<!-- Resumen Principal -->
<div class="card <?= $net_income >= 0 ? 'card-success' : 'card-warning' ?>">
    <h3>💰 Resumen del Mes</h3>
    
    <table class="table">
        <tr>
            <td><strong>💚 Total Ingresos:</strong></td>
            <td class="amount-positive"><strong>$<?= number_format($total_income ?? 0, 2) ?></strong></td>
        </tr>
        <tr>
            <td><strong>💸 Total Gastos:</strong></td>
            <td class="amount-negative"><strong>$<?= number_format($total_expenses ?? 0, 2) ?></strong></td>
        </tr>
        <tr style="border-top: 2px solid #dee2e6;">
            <td><strong>📈 Balance Neto:</strong></td>
            <td class="<?= $net_income >= 0 ? 'amount-positive' : 'amount-negative' ?>">
                <strong style="font-size: 18px;">
                    <?= $net_income >= 0 ? '+' : '' ?>$<?= number_format($net_income, 2) ?>
                </strong>
            </td>
        </tr>
        <tr>
            <td><strong>🎯 Tasa de Ahorro:</strong></td>
            <td>
                <strong style="color: <?= $savings_rate >= 20 ? '#28a745' : ($savings_rate >= 10 ? '#fd7e14' : '#dc3545') ?>;">
                    <?= number_format($savings_rate, 1) ?>%
                </strong>
                <?php if ($savings_rate >= 20): ?>
                    <span style="color: #28a745;">¡Excelente! 🎉</span>
                <?php elseif ($savings_rate >= 10): ?>
                    <span style="color: #fd7e14;">Bien, pero puedes mejorar 👍</span>
                <?php else: ?>
                    <span style="color: #dc3545;">Necesitas ahorrar más 💪</span>
                <?php endif; ?>
            </td>
        </tr>
    </table>
</div>

<!-- Análisis de Gastos por Categoría -->
<?php if (!empty($category_expenses)): ?>
<div class="card">
    <h3>🏷️ Gastos por Categoría</h3>
    
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Categoría</th>
                <th style="text-align: right;">Monto</th>
                <th style="text-align: right;">% del Total</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach (array_slice($category_expenses, 0, 8) as $category): ?>
            <tr>
                <td>
                    <span style="color: <?= htmlspecialchars($category['color'] ?? '#007bff') ?>;">●</span>
                    <?= htmlspecialchars($category['name']) ?>
                </td>
                <td style="text-align: right;" class="amount-negative">
                    $<?= number_format($category['amount'], 2) ?>
                </td>
                <td style="text-align: right;">
                    <?= number_format(($category['amount'] / max($total_expenses, 1)) * 100, 1) ?>%
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    
    <?php if (count($category_expenses) > 8): ?>
    <p style="font-size: 14px; color: #6c757d; text-align: center;">
        Y <?= count($category_expenses) - 8 ?> categoría<?= count($category_expenses) - 8 !== 1 ? 's' : '' ?> más...
    </p>
    <?php endif; ?>
</div>
<?php endif; ?>

<!-- Comparación con el mes anterior -->
<?php if (isset($previous_income) && isset($previous_expenses)): ?>
<div class="card card-info">
    <h3>📈 Comparación con el Mes Anterior</h3>
    
    <table class="table">
        <tr>
            <td><strong>Ingresos:</strong></td>
            <td>
                <?php 
                $income_change = $previous_income > 0 ? ((($total_income - $previous_income) / $previous_income) * 100) : 0;
                $income_change_class = $income_change >= 0 ? 'amount-positive' : 'amount-negative';
                ?>
                <span class="<?= $income_change_class ?>">
                    <?= $income_change >= 0 ? '+' : '' ?><?= number_format($income_change, 1) ?>%
                </span>
                <?= $income_change >= 0 ? '📈' : '📉' ?>
            </td>
        </tr>
        <tr>
            <td><strong>Gastos:</strong></td>
            <td>
                <span class="<?= $expense_growth <= 0 ? 'amount-positive' : 'amount-negative' ?>">
                    <?= $expense_growth >= 0 ? '+' : '' ?><?= number_format($expense_growth, 1) ?>%
                </span>
                <?= $expense_growth <= 0 ? '📉' : '📈' ?>
            </td>
        </tr>
    </table>
</div>
<?php endif; ?>

<!-- Estadísticas Adicionales -->
<div class="card">
    <h3>📊 Estadísticas del Mes</h3>
    
    <table class="table">
        <tr>
            <td><strong>📝 Total de Transacciones:</strong></td>
            <td><?= number_format($total_transactions ?? 0) ?></td>
        </tr>
        <tr>
            <td><strong>💳 Transacciones de Ingresos:</strong></td>
            <td><?= number_format($income_transactions ?? 0) ?></td>
        </tr>
        <tr>
            <td><strong>💸 Transacciones de Gastos:</strong></td>
            <td><?= number_format($expense_transactions ?? 0) ?></td>
        </tr>
        <tr>
            <td><strong>💰 Gasto Promedio por Transacción:</strong></td>
            <td>$<?= number_format($expense_transactions > 0 ? ($total_expenses / $expense_transactions) : 0, 2) ?></td>
        </tr>
        <tr>
            <td><strong>📅 Días con Actividad:</strong></td>
            <td><?= $active_days ?? 0 ?> de <?= date('t', strtotime($period_start)) ?> días</td>
        </tr>
    </table>
</div>

<!-- Recomendaciones Personalizadas -->
<div class="card card-warning">
    <h3>💡 Recomendaciones para el Próximo Mes</h3>
    
    <ul>
        <?php if ($savings_rate < 10): ?>
        <li><strong>🎯 Aumenta tu tasa de ahorro:</strong> Intenta ahorrar al menos el 10% de tus ingresos</li>
        <?php endif; ?>
        
        <?php if ($expense_growth > 10): ?>
        <li><strong>⚠️ Controla el crecimiento de gastos:</strong> Tus gastos aumentaron <?= number_format($expense_growth, 1) ?>% este mes</li>
        <?php endif; ?>
        
        <?php if (!empty($category_expenses) && count($category_expenses) > 0): ?>
            <?php $top_category = $category_expenses[0]; ?>
            <li><strong>🏷️ Revisa tu categoría principal:</strong> <?= htmlspecialchars($top_category['name']) ?> representa el <?= number_format(($top_category['amount'] / max($total_expenses, 1)) * 100, 1) ?>% de tus gastos</li>
        <?php endif; ?>
        
        <li><strong>📱 Usa más la app:</strong> Registra tus transacciones diariamente para mejor control</li>
        <li><strong>🔔 Configura recordatorios:</strong> No olvides pagos importantes</li>
    </ul>
</div>

<!-- Metas para el próximo mes -->
<div class="card card-success">
    <h3>🎯 Metas Sugeridas para el Próximo Mes</h3>
    
    <ul>
        <li>💰 <strong>Meta de ahorro:</strong> $<?= number_format(max($total_income * 0.15, $net_income * 1.1), 2) ?></li>
        <li>💸 <strong>Límite de gastos:</strong> $<?= number_format($total_expenses * 0.95, 2) ?> (5% menos que este mes)</li>
        <li>📝 <strong>Transacciones objetivo:</strong> Registrar al menos <?= max(20, ($total_transactions ?? 0) + 5) ?> transacciones</li>
        <li>📊 <strong>Categorías a revisar:</strong> Optimizar gastos en las 3 categorías principales</li>
    </ul>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?= htmlspecialchars($dashboard_url) ?>" class="btn">
        📊 Ver Dashboard Completo
    </a>
    <a href="<?= htmlspecialchars($report_url) ?>" class="btn btn-success">
        📈 Ver Reporte Detallado
    </a>
</div>

<p>
    ¡Sigue así! Cada mes que mantienes el control de tus finanzas es un paso más hacia tus metas financieras. 
    Recuerda que la constancia es clave para el éxito financiero.
</p>

<p>
    Si tienes alguna pregunta sobre este reporte o quieres sugerencias personalizadas, 
    no dudes en contactarnos.
</p>

<p>
    ¡Que tengas un excelente mes financiero!<br>
    <strong>El equipo de <?= htmlspecialchars($app_name) ?></strong>
</p>

<hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">

<p style="font-size: 14px; color: #6c757d;">
    <strong>📧 Configuración de reportes:</strong> Puedes cambiar la frecuencia de estos reportes 
    o desactivarlos desde tu dashboard en Configuración > Notificaciones.
</p>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.html.php';
?>
