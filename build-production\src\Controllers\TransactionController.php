<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Services\TransactionService;
use ControlGastos\Services\AccountService;
use ControlGastos\Services\CategoryService;
use ControlGastos\Core\Session;

/**
 * Controlador de transacciones
 * Maneja todas las rutas relacionadas con gestión de transacciones
 */
class TransactionController
{
    private Container $container;
    private TransactionService $transactionService;
    private AccountService $accountService;
    private CategoryService $categoryService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->transactionService = $container->get('transactionService');
        $this->accountService = $container->get('accountService');
        $this->categoryService = $container->get('categoryService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Mostrar lista de transacciones
     */
    public function index(): string
    {
        $filters = [
            'page' => (int) ($_GET['page'] ?? 1),
            'per_page' => (int) ($_GET['per_page'] ?? 20),
            'type' => $_GET['type'] ?? '',
            'account_id' => $_GET['account_id'] ?? '',
            'category_id' => $_GET['category_id'] ?? '',
            'search' => $_GET['search'] ?? '',
            'start_date' => $_GET['start_date'] ?? '',
            'end_date' => $_GET['end_date'] ?? ''
        ];

        $transactionsResult = $this->transactionService->getUserTransactions($this->userId, $filters);
        $accountsResult = $this->accountService->getUserAccounts($this->userId);
        $categoriesResult = $this->categoryService->getUserCategories($this->userId);
        $statsResult = $this->transactionService->getTransactionStats($this->userId);

        $data = [
            'title' => 'Transacciones',
            'transactions' => $transactionsResult['success'] ? $transactionsResult['transactions'] : [],
            'pagination' => $transactionsResult['success'] ? $transactionsResult['pagination'] : [],
            'accounts' => $accountsResult['success'] ? $accountsResult['accounts'] : [],
            'categories' => $categoriesResult['success'] ? $categoriesResult['categories'] : [],
            'stats' => $statsResult['success'] ? $statsResult['stats'] : [],
            'filters' => $filters,
            'transaction_types' => $this->transactionService->getTransactionTypes(),
            'recurring_types' => $this->transactionService->getRecurringTypes(),
            'csrf_token' => $this->session->getCsrfToken(),
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('transactions/index', $data);
    }

    /**
     * Mostrar formulario de creación
     */
    public function create(): string
    {
        $accountsResult = $this->accountService->getUserAccounts($this->userId);
        $categoriesResult = $this->categoryService->getUserCategories($this->userId);

        $data = [
            'title' => 'Nueva Transacción',
            'accounts' => $accountsResult['success'] ? $accountsResult['accounts'] : [],
            'categories' => $categoriesResult['success'] ? $categoriesResult['categories'] : [],
            'transaction_types' => $this->transactionService->getTransactionTypes(),
            'recurring_types' => $this->transactionService->getRecurringTypes(),
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error'),
            'validation_errors' => $this->session->getFlash('validation_errors', [])
        ];

        return $this->render('transactions/create', $data);
    }

    /**
     * Procesar creación de transacción
     */
    public function store(): void
    {
        $data = [
            'account_id' => (int) ($_POST['account_id'] ?? 0),
            'category_id' => (int) ($_POST['category_id'] ?? 0),
            'subcategory_id' => !empty($_POST['subcategory_id']) ? (int) $_POST['subcategory_id'] : null,
            'type' => $_POST['type'] ?? '',
            'amount' => $_POST['amount'] ?? '',
            'description' => $_POST['description'] ?? '',
            'transaction_date' => $_POST['transaction_date'] ?? date('Y-m-d H:i:s'),
            'reference' => $_POST['reference'] ?? '',
            'notes' => $_POST['notes'] ?? '',
            'tags' => $_POST['tags'] ?? '',
            'is_recurring' => isset($_POST['is_recurring']),
            'recurring_type' => $_POST['recurring_type'] ?? '',
            'recurring_interval' => $_POST['recurring_interval'] ?? 1,
            'recurring_end_date' => $_POST['recurring_end_date'] ?? ''
        ];

        $result = $this->transactionService->createTransaction($data, $this->userId);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
            header('Location: /transactions');
            exit;
        }

        if (isset($result['errors'])) {
            $this->session->flash('error', 'Errores de validación');
            $this->session->flash('validation_errors', $result['errors']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /transactions/create');
        exit;
    }

    /**
     * Mostrar detalles de transacción
     */
    public function show(int $id): string
    {
        $result = $this->transactionService->getTransaction($id, $this->userId);

        if (!$result['success']) {
            $this->session->flash('error', $result['message']);
            header('Location: /transactions');
            exit;
        }

        $data = [
            'title' => 'Detalles de Transacción',
            'transaction' => $result['transaction'],
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('transactions/show', $data);
    }

    /**
     * Mostrar formulario de edición
     */
    public function edit(int $id): string
    {
        $transactionResult = $this->transactionService->getTransaction($id, $this->userId);

        if (!$transactionResult['success']) {
            $this->session->flash('error', $transactionResult['message']);
            header('Location: /transactions');
            exit;
        }

        $accountsResult = $this->accountService->getUserAccounts($this->userId);
        $categoriesResult = $this->categoryService->getUserCategories($this->userId);

        $data = [
            'title' => 'Editar Transacción',
            'transaction' => $transactionResult['transaction'],
            'accounts' => $accountsResult['success'] ? $accountsResult['accounts'] : [],
            'categories' => $categoriesResult['success'] ? $categoriesResult['categories'] : [],
            'transaction_types' => $this->transactionService->getTransactionTypes(),
            'recurring_types' => $this->transactionService->getRecurringTypes(),
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error'),
            'validation_errors' => $this->session->getFlash('validation_errors', [])
        ];

        return $this->render('transactions/edit', $data);
    }

    /**
     * Procesar actualización de transacción
     */
    public function update(int $id): void
    {
        $data = [
            'account_id' => (int) ($_POST['account_id'] ?? 0),
            'category_id' => (int) ($_POST['category_id'] ?? 0),
            'subcategory_id' => !empty($_POST['subcategory_id']) ? (int) $_POST['subcategory_id'] : null,
            'type' => $_POST['type'] ?? '',
            'amount' => $_POST['amount'] ?? '',
            'description' => $_POST['description'] ?? '',
            'transaction_date' => $_POST['transaction_date'] ?? '',
            'reference' => $_POST['reference'] ?? '',
            'notes' => $_POST['notes'] ?? '',
            'tags' => $_POST['tags'] ?? '',
            'is_recurring' => isset($_POST['is_recurring']),
            'recurring_type' => $_POST['recurring_type'] ?? '',
            'recurring_interval' => $_POST['recurring_interval'] ?? 1,
            'recurring_end_date' => $_POST['recurring_end_date'] ?? ''
        ];

        $result = $this->transactionService->updateTransaction($id, $data, $this->userId);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
            header('Location: /transactions/' . $id);
            exit;
        }

        if (isset($result['errors'])) {
            $this->session->flash('error', 'Errores de validación');
            $this->session->flash('validation_errors', $result['errors']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /transactions/' . $id . '/edit');
        exit;
    }

    /**
     * Eliminar transacción
     */
    public function delete(int $id): void
    {
        $result = $this->transactionService->deleteTransaction($id, $this->userId);

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /transactions');
        exit;
    }

    /**
     * Buscar transacciones (AJAX)
     */
    public function search(): void
    {
        $search = $_GET['q'] ?? '';
        $filters = [
            'type' => $_GET['type'] ?? '',
            'account_id' => $_GET['account_id'] ?? '',
            'category_id' => $_GET['category_id'] ?? '',
            'start_date' => $_GET['start_date'] ?? '',
            'end_date' => $_GET['end_date'] ?? ''
        ];

        $result = $this->transactionService->searchTransactions($this->userId, $search, $filters);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Obtener estadísticas (AJAX)
     */
    public function getStats(): void
    {
        $startDate = !empty($_GET['start_date']) ? new \DateTime($_GET['start_date']) : null;
        $endDate = !empty($_GET['end_date']) ? new \DateTime($_GET['end_date']) : null;

        $result = $this->transactionService->getTransactionStats($this->userId, $startDate, $endDate);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Obtener subcategorías por categoría (AJAX)
     */
    public function getSubcategories(int $categoryId): void
    {
        $result = $this->categoryService->getSubcategoriesByCategory($categoryId, $this->userId);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Exportar transacciones
     */
    public function export(): void
    {
        $filters = [
            'type' => $_GET['type'] ?? '',
            'account_id' => $_GET['account_id'] ?? '',
            'category_id' => $_GET['category_id'] ?? '',
            'start_date' => $_GET['start_date'] ?? '',
            'end_date' => $_GET['end_date'] ?? ''
        ];

        $result = $this->transactionService->getUserTransactions($this->userId, $filters);
        
        if ($result['success']) {
            $filename = 'transacciones_' . date('Y-m-d_H-i-s') . '.json';
            
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            echo json_encode([
                'export_date' => date('Y-m-d H:i:s'),
                'user_id' => $this->userId,
                'filters' => $filters,
                'transactions' => $result['transactions']
            ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        } else {
            $this->session->flash('error', 'Error al exportar transacciones');
            header('Location: /transactions');
        }
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Verificar si es una request AJAX
     */
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
