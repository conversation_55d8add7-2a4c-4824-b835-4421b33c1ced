<?php
// La autenticación ya se verifica en el controlador
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-university me-2"></i>Mis Cuentas Bancarias
                    </h1>
                    <p class="text-muted">Gestiona tus cuentas de ahorro, corriente y débito</p>
                </div>
                <div>
                    <a href="/controlGastos/public/?route=bank-accounts/create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nueva Cuenta
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas Generales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Cuentas</h6>
                            <h3 class="mb-0"><?= $stats['total_accounts'] ?? 0 ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-university fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Saldo Total</h6>
                            <h3 class="mb-0">$<?= number_format($stats['total_balance'] ?? 0, 0) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Cuentas Activas</h6>
                            <h3 class="mb-0"><?= $stats['active_accounts'] ?? 0 ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Saldo Activo</h6>
                            <h3 class="mb-0">$<?= number_format($stats['active_balance'] ?? 0, 0) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Lista de Cuentas -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Mis Cuentas
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($accounts)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-university fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No tienes cuentas bancarias registradas</h5>
                            <p class="text-muted">Comienza agregando tu primera cuenta bancaria</p>
                            <a href="/controlGastos/public/?route=bank-accounts/create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Crear Primera Cuenta
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($accounts as $accountData): ?>
                            <?php $account = $accountData['account']; ?>
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 shadow-sm">
                                    <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                        <div class="d-flex justify-content-between align-items-center text-white">
                                            <div>
                                                <h6 class="mb-0"><?= htmlspecialchars($account['account_name']) ?></h6>
                                                <small><?= htmlspecialchars($account['bank_name']) ?></small>
                                            </div>
                                            <div>
                                                <?php if ($account['account_number']): ?>
                                                <small>**** <?= htmlspecialchars(substr($account['account_number'], -4)) ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!-- Información financiera -->
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="text-muted">Saldo Actual:</span>
                                                <strong class="text-success">
                                                    $<?= number_format($accountData['current_balance'], 0) ?>
                                                </strong>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="text-muted">Saldo Inicial:</span>
                                                <span>$<?= number_format($accountData['initial_balance'], 0) ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="text-muted">Cambio Neto:</span>
                                                <span class="<?= $accountData['net_change'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                                    <?= $accountData['net_change'] >= 0 ? '+' : '' ?>$<?= number_format($accountData['net_change'], 0) ?>
                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span class="text-muted">Tipo:</span>
                                                <span class="badge bg-secondary">
                                                    <?php
                                                    $types = [
                                                        'savings' => 'Ahorros',
                                                        'checking' => 'Corriente',
                                                        'business' => 'Empresarial'
                                                    ];
                                                    echo $types[$account['account_type']] ?? ucfirst($account['account_type']);
                                                    ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Estadísticas del mes -->
                                        <div class="mb-3">
                                            <small class="text-muted">Este mes:</small>
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <small class="text-success">Depósitos</small>
                                                    <div class="fw-bold text-success">$<?= number_format($accountData['monthly_deposits'], 0) ?></div>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-danger">Retiros</small>
                                                    <div class="fw-bold text-danger">$<?= number_format($accountData['monthly_withdrawals'], 0) ?></div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Acciones -->
                                        <div class="d-flex gap-2">
                                            <a href="/controlGastos/public/?route=bank-accounts/show&id=<?= $account['id'] ?>" 
                                               class="btn btn-outline-primary btn-sm flex-fill">
                                                <i class="fas fa-eye me-1"></i>Ver
                                            </a>
                                            <a href="/controlGastos/public/?route=bank-accounts/edit&id=<?= $account['id'] ?>" 
                                               class="btn btn-outline-secondary btn-sm flex-fill">
                                                <i class="fas fa-edit me-1"></i>Editar
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Movimientos Recientes -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Movimientos Recientes
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_movements)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No hay movimientos recientes</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_movements as $movement): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="fas fa-<?= in_array($movement['movement_type'], ['deposit', 'transfer_in', 'interest']) ? 'arrow-down text-success' : 'arrow-up text-danger' ?> me-2"></i>
                                            <strong class="<?= in_array($movement['movement_type'], ['deposit', 'transfer_in', 'interest']) ? 'text-success' : 'text-danger' ?>">
                                                <?= in_array($movement['movement_type'], ['deposit', 'transfer_in', 'interest']) ? '+' : '-' ?>$<?= number_format($movement['amount'], 0) ?>
                                            </strong>
                                        </div>
                                        <div class="small text-muted"><?= htmlspecialchars($movement['description']) ?></div>
                                        <div class="small text-muted">
                                            <?= htmlspecialchars($movement['account_name']) ?> • 
                                            <?= date('d/m/Y', strtotime($movement['movement_date'])) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}
</style>
