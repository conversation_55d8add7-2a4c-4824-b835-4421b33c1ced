<?php

namespace ControlGastos\Controllers;

use ControlGastos\Models\BankAccount;
use ControlGastos\Core\Session;
use ControlGastos\Core\SimpleDatabase;
use PDO;

class BankAccountController
{
    private $bankAccountModel;
    private $session;
    private $db;

    public function __construct(SimpleDatabase $database, Session $session)
    {
        $this->db = $database->getConnection();
        $this->session = $session;
        $this->bankAccountModel = new BankAccount($this->db);
    }

    /**
     * Dashboard de cuentas bancarias
     */
    public function index(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        try {
            // Obtener cuentas del usuario
            $accounts = $this->bankAccountModel->findByUserId($userId);
            
            // Obtener estadísticas generales
            $stats = $this->bankAccountModel->getUserAccountsStats($userId);
            
            // Obtener resúmenes financieros de cada cuenta
            $accountsWithSummary = [];
            foreach ($accounts as $account) {
                $summary = $this->bankAccountModel->getFinancialSummary($account['id']);
                $accountsWithSummary[] = $summary;
            }

            // Obtener movimientos recientes (últimos 10)
            $recentMovements = [];
            foreach ($accounts as $account) {
                $movements = $this->bankAccountModel->getMovements($account['id'], 5);
                foreach ($movements as $movement) {
                    $movement['account_name'] = $account['account_name'];
                    $recentMovements[] = $movement;
                }
            }
            
            // Ordenar movimientos por fecha
            usort($recentMovements, function($a, $b) {
                return strtotime($b['movement_date']) - strtotime($a['movement_date']);
            });
            
            $recentMovements = array_slice($recentMovements, 0, 10);

            $this->render('bank_accounts/index', [
                'title' => 'Mis Cuentas Bancarias',
                'accounts' => $accountsWithSummary,
                'stats' => $stats,
                'recent_movements' => $recentMovements
            ]);

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al cargar cuentas: ' . $e->getMessage());
            $this->render('bank_accounts/index', [
                'title' => 'Mis Cuentas Bancarias',
                'accounts' => [],
                'stats' => [],
                'recent_movements' => []
            ]);
        }
    }

    /**
     * Mostrar formulario de creación
     */
    public function create(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        // Obtener bancos activos del usuario
        $sql = "SELECT id, bank_name, bank_code, color FROM banks WHERE user_id = ? AND is_active = 1 ORDER BY bank_name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $banks = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $this->render('bank_accounts/create', [
            'title' => 'Nueva Cuenta Bancaria',
            'banks' => $banks
        ]);
    }

    /**
     * Procesar creación de cuenta
     */
    public function store(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /controlGastos/public/?route=bank-accounts/create');
            exit;
        }

        $data = [
            'user_id' => $userId,
            'account_name' => trim($_POST['account_name'] ?? ''),
            'bank_id' => (int) ($_POST['bank_id'] ?? 0),
            'account_number' => trim($_POST['account_number'] ?? ''),
            'debit_card_number' => trim($_POST['debit_card_number'] ?? ''),
            'account_type' => trim($_POST['account_type'] ?? 'savings'),
            'initial_balance' => (float) ($_POST['initial_balance'] ?? 0),
            'description' => trim($_POST['description'] ?? '')
        ];

        // Validaciones básicas
        if (empty($data['account_name']) || empty($data['bank_id'])) {
            $this->session->flash('error', 'El nombre de la cuenta y el banco son obligatorios');
            header('Location: /controlGastos/public/?route=bank-accounts/create');
            exit;
        }

        try {
            $accountId = $this->bankAccountModel->create($data);
            
            // Si hay saldo inicial, crear movimiento inicial
            if ($data['initial_balance'] > 0) {
                $this->bankAccountModel->addMovement([
                    'bank_account_id' => $accountId,
                    'user_id' => $userId,
                    'movement_type' => 'deposit',
                    'amount' => $data['initial_balance'],
                    'description' => 'Saldo inicial de la cuenta',
                    'movement_date' => date('Y-m-d')
                ]);
            }

            $this->session->flash('success', 'Cuenta bancaria creada exitosamente');
            header("Location: /controlGastos/public/?route=bank-accounts/show&id={$accountId}");
            exit;

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al crear cuenta: ' . $e->getMessage());
            header('Location: /controlGastos/public/?route=bank-accounts/create');
            exit;
        }
    }

    /**
     * Mostrar detalles de cuenta
     */
    public function show(): void
    {
        $userId = $this->session->get('user_id');
        $accountId = (int) ($_GET['id'] ?? 0);

        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        try {
            $account = $this->bankAccountModel->findById($accountId);
            
            if (!$account || $account['user_id'] != $userId) {
                $this->session->flash('error', 'Cuenta no encontrada');
                header('Location: /controlGastos/public/?route=bank-accounts');
                exit;
            }

            // Obtener resumen financiero
            $summary = $this->bankAccountModel->getFinancialSummary($accountId);
            
            // Obtener movimientos recientes
            $movements = $this->bankAccountModel->getMovements($accountId, 20);

            $this->render('bank_accounts/show', [
                'title' => 'Detalles de Cuenta - ' . $account['account_name'],
                'account' => $summary,
                'movements' => $movements
            ]);

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al cargar cuenta: ' . $e->getMessage());
            header('Location: /controlGastos/public/?route=bank-accounts');
            exit;
        }
    }

    /**
     * Agregar movimiento a cuenta
     */
    public function addMovement(): void
    {
        $userId = $this->session->get('user_id');
        $accountId = (int) ($_POST['bank_account_id'] ?? 0);

        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header("Location: /controlGastos/public/?route=bank-accounts/show&id={$accountId}");
            exit;
        }

        $data = [
            'bank_account_id' => $accountId,
            'user_id' => $userId,
            'movement_type' => trim($_POST['movement_type'] ?? ''),
            'amount' => (float) ($_POST['amount'] ?? 0),
            'description' => trim($_POST['description'] ?? ''),
            'reference' => trim($_POST['reference'] ?? ''),
            'movement_date' => $_POST['movement_date'] ?? date('Y-m-d')
        ];

        // Validaciones
        if (empty($data['movement_type']) || $data['amount'] <= 0 || empty($data['description'])) {
            $this->session->flash('error', 'Todos los campos son obligatorios y el monto debe ser mayor a 0');
            header("Location: /controlGastos/public/?route=bank-accounts/show&id={$accountId}");
            exit;
        }

        try {
            $this->bankAccountModel->addMovement($data);
            
            $movementLabels = [
                'deposit' => 'Depósito registrado',
                'withdrawal' => 'Retiro registrado',
                'fee' => 'Comisión registrada',
                'interest' => 'Interés registrado'
            ];
            
            $message = $movementLabels[$data['movement_type']] ?? 'Movimiento registrado';
            $this->session->flash('success', $message . ' exitosamente');

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al registrar movimiento: ' . $e->getMessage());
        }

        header("Location: /controlGastos/public/?route=bank-accounts/show&id={$accountId}");
        exit;
    }

    /**
     * Renderizar vista con layout
     */
    private function render(string $view, array $data = []): void
    {
        // Extraer variables para la vista
        extract($data);
        
        // Obtener información del usuario
        $userId = $this->session->get('user_id');
        $user = null;
        if ($userId) {
            $stmt = $this->db->prepare("SELECT first_name, last_name, email FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
        }

        // Renderizar con layout completo
        echo '<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . ($title ?? 'Control de Gastos') . '</title>
    <link href="/controlGastos/public/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="/controlGastos/public/assets/css/fontawesome.min.css" rel="stylesheet">
    <link href="/controlGastos/public/assets/css/custom.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="/controlGastos/public/">
                    <i class="fas fa-chart-line me-2"></i>Control de Gastos
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/controlGastos/public/">
                                <i class="fas fa-home me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/controlGastos/public/?route=credit-cards">
                                <i class="fas fa-credit-card me-1"></i>Tarjetas de Crédito
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/controlGastos/public/?route=bank-accounts">
                                <i class="fas fa-university me-1"></i>Cuentas Bancarias
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>' . htmlspecialchars(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '') ?: 'Usuario') . '
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/controlGastos/public/?route=auth/logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Contenido principal -->
        <div class="container-fluid py-4">';
        
        // Mostrar mensajes flash
        if ($this->session->hasFlash('success')) {
            echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>' . $this->session->getFlash('success') . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
        }
        
        if ($this->session->hasFlash('error')) {
            echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>' . $this->session->getFlash('error') . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
        }

        // Incluir la vista específica
        $viewPath = __DIR__ . "/../../templates/pages/{$view}.php";
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            echo "<div class='container'><div class='alert alert-danger'>Vista no encontrada: {$view}</div></div>";
        }

        echo '
        </div>
    </div>

    <script src="/controlGastos/public/assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
    }
}
