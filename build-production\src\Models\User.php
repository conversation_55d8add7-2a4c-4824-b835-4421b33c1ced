<?php

declare(strict_types=1);

namespace ControlGastos\Models;

/**
 * Modelo de Usuario
 * Representa un usuario del sistema
 */
class User
{
    private ?int $id = null;
    private string $email;
    private string $passwordHash;
    private string $firstName;
    private string $lastName;
    private ?string $phone = null;
    private bool $twoFactorEnabled = false;
    private ?string $twoFactorSecret = null;
    private bool $emailVerified = false;
    private ?string $emailVerificationToken = null;
    private ?string $passwordResetToken = null;
    private ?\DateTime $passwordResetExpires = null;
    private string $status = 'active';
    private \DateTime $createdAt;
    private \DateTime $updatedAt;

    public function __construct(array $data = [])
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    /**
     * Llenar modelo con datos
     */
    public function fill(array $data): void
    {
        if (isset($data['id'])) {
            $this->id = (int) $data['id'];
        }
        
        if (isset($data['email'])) {
            $this->email = $data['email'];
        }
        
        if (isset($data['password_hash'])) {
            $this->passwordHash = $data['password_hash'];
        }
        
        if (isset($data['first_name'])) {
            $this->firstName = $data['first_name'];
        }
        
        if (isset($data['last_name'])) {
            $this->lastName = $data['last_name'];
        }
        
        if (isset($data['phone'])) {
            $this->phone = $data['phone'];
        }
        
        if (isset($data['two_factor_enabled'])) {
            $this->twoFactorEnabled = (bool) $data['two_factor_enabled'];
        }
        
        if (isset($data['two_factor_secret'])) {
            $this->twoFactorSecret = $data['two_factor_secret'];
        }
        
        if (isset($data['email_verified'])) {
            $this->emailVerified = (bool) $data['email_verified'];
        }
        
        if (isset($data['email_verification_token'])) {
            $this->emailVerificationToken = $data['email_verification_token'];
        }
        
        if (isset($data['password_reset_token'])) {
            $this->passwordResetToken = $data['password_reset_token'];
        }
        
        if (isset($data['password_reset_expires'])) {
            $this->passwordResetExpires = new \DateTime($data['password_reset_expires']);
        }
        
        if (isset($data['status'])) {
            $this->status = $data['status'];
        }
        
        if (isset($data['created_at'])) {
            $this->createdAt = new \DateTime($data['created_at']);
        }
        
        if (isset($data['updated_at'])) {
            $this->updatedAt = new \DateTime($data['updated_at']);
        }
    }

    /**
     * Convertir a array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'password_hash' => $this->passwordHash,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'phone' => $this->phone,
            'two_factor_enabled' => $this->twoFactorEnabled,
            'two_factor_secret' => $this->twoFactorSecret,
            'email_verified' => $this->emailVerified,
            'email_verification_token' => $this->emailVerificationToken,
            'password_reset_token' => $this->passwordResetToken,
            'password_reset_expires' => $this->passwordResetExpires?->format('Y-m-d H:i:s'),
            'status' => $this->status,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Convertir a array público (sin datos sensibles)
     */
    public function toPublicArray(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'phone' => $this->phone,
            'two_factor_enabled' => $this->twoFactorEnabled,
            'email_verified' => $this->emailVerified,
            'status' => $this->status,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s')
        ];
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getEmail(): string { return $this->email; }
    public function getPasswordHash(): string { return $this->passwordHash; }
    public function getFirstName(): string { return $this->firstName; }
    public function getLastName(): string { return $this->lastName; }
    public function getPhone(): ?string { return $this->phone; }
    public function isTwoFactorEnabled(): bool { return $this->twoFactorEnabled; }
    public function getTwoFactorSecret(): ?string { return $this->twoFactorSecret; }
    public function isEmailVerified(): bool { return $this->emailVerified; }
    public function getEmailVerificationToken(): ?string { return $this->emailVerificationToken; }
    public function getPasswordResetToken(): ?string { return $this->passwordResetToken; }
    public function getPasswordResetExpires(): ?\DateTime { return $this->passwordResetExpires; }
    public function getStatus(): string { return $this->status; }
    public function getCreatedAt(): \DateTime { return $this->createdAt; }
    public function getUpdatedAt(): \DateTime { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setEmail(string $email): void { $this->email = $email; }
    public function setPasswordHash(string $passwordHash): void { $this->passwordHash = $passwordHash; }
    public function setFirstName(string $firstName): void { $this->firstName = $firstName; }
    public function setLastName(string $lastName): void { $this->lastName = $lastName; }
    public function setPhone(?string $phone): void { $this->phone = $phone; }
    public function setTwoFactorEnabled(bool $enabled): void { $this->twoFactorEnabled = $enabled; }
    public function setTwoFactorSecret(?string $secret): void { $this->twoFactorSecret = $secret; }
    public function setEmailVerified(bool $verified): void { $this->emailVerified = $verified; }
    public function setEmailVerificationToken(?string $token): void { $this->emailVerificationToken = $token; }
    public function setPasswordResetToken(?string $token): void { $this->passwordResetToken = $token; }
    public function setPasswordResetExpires(?\DateTime $expires): void { $this->passwordResetExpires = $expires; }
    public function setStatus(string $status): void { $this->status = $status; }
    public function setUpdatedAt(\DateTime $updatedAt): void { $this->updatedAt = $updatedAt; }

    /**
     * Obtener nombre completo
     */
    public function getFullName(): string
    {
        return trim($this->firstName . ' ' . $this->lastName);
    }

    /**
     * Verificar contraseña
     */
    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->passwordHash);
    }

    /**
     * Establecer contraseña
     */
    public function setPassword(string $password): void
    {
        $this->passwordHash = password_hash($password, PASSWORD_DEFAULT);
        $this->updatedAt = new \DateTime();
    }

    /**
     * Generar token de verificación de email
     */
    public function generateEmailVerificationToken(): string
    {
        $this->emailVerificationToken = bin2hex(random_bytes(32));
        $this->updatedAt = new \DateTime();
        return $this->emailVerificationToken;
    }

    /**
     * Generar token de reset de contraseña
     */
    public function generatePasswordResetToken(): string
    {
        $this->passwordResetToken = bin2hex(random_bytes(32));
        $this->passwordResetExpires = new \DateTime('+1 hour');
        $this->updatedAt = new \DateTime();
        return $this->passwordResetToken;
    }

    /**
     * Verificar si el token de reset es válido
     */
    public function isPasswordResetTokenValid(string $token): bool
    {
        return $this->passwordResetToken === $token && 
               $this->passwordResetExpires && 
               $this->passwordResetExpires > new \DateTime();
    }

    /**
     * Limpiar token de reset de contraseña
     */
    public function clearPasswordResetToken(): void
    {
        $this->passwordResetToken = null;
        $this->passwordResetExpires = null;
        $this->updatedAt = new \DateTime();
    }

    /**
     * Verificar si el usuario está activo
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }
}
