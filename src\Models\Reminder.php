<?php

declare(strict_types=1);

namespace ControlGastos\Models;

/**
 * Modelo de Recordatorio
 * Representa un recordatorio de pago o compromiso financiero
 */
class Reminder
{
    private ?int $id = null;
    private int $userId;
    private string $title;
    private string $description;
    private string $type;
    private float $amount;
    private int $categoryId;
    private ?int $subcategoryId = null;
    private ?int $accountId = null;
    private \DateTime $dueDate;
    private string $priority;
    private string $status;
    private bool $isRecurring = false;
    private ?string $recurringType = null;
    private ?int $recurringInterval = null;
    private ?\DateTime $recurringEndDate = null;
    private bool $autoCreateTransaction = false;
    private bool $notificationEnabled = true;
    private int $notificationDaysBefore = 1;
    private ?\DateTime $lastNotificationSent = null;
    private ?\DateTime $completedAt = null;
    private ?string $notes = null;
    private \DateTime $createdAt;
    private \DateTime $updatedAt;

    // Tipos de recordatorio
    public const TYPES = [
        'payment' => 'Pago',
        'income' => 'Ingreso Esperado',
        'bill' => 'Factura',
        'subscription' => 'Suscripción',
        'loan' => 'Préstamo',
        'investment' => 'Inversión',
        'tax' => 'Impuesto',
        'insurance' => 'Seguro',
        'maintenance' => 'Mantenimiento',
        'other' => 'Otro'
    ];

    // Prioridades
    public const PRIORITIES = [
        'low' => 'Baja',
        'medium' => 'Media',
        'high' => 'Alta',
        'urgent' => 'Urgente'
    ];

    // Estados
    public const STATUSES = [
        'pending' => 'Pendiente',
        'completed' => 'Completado',
        'overdue' => 'Vencido',
        'cancelled' => 'Cancelado'
    ];

    // Tipos de recurrencia
    public const RECURRING_TYPES = [
        'daily' => 'Diario',
        'weekly' => 'Semanal',
        'monthly' => 'Mensual',
        'quarterly' => 'Trimestral',
        'yearly' => 'Anual'
    ];

    public function __construct(array $data = [])
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        $this->dueDate = new \DateTime();
        
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    /**
     * Llenar modelo con datos
     */
    public function fill(array $data): void
    {
        if (isset($data['id'])) {
            $this->id = (int) $data['id'];
        }
        
        if (isset($data['user_id'])) {
            $this->userId = (int) $data['user_id'];
        }
        
        if (isset($data['title'])) {
            $this->title = trim($data['title']);
        }
        
        if (isset($data['description'])) {
            $this->description = trim($data['description']);
        }
        
        if (isset($data['type'])) {
            $this->type = $data['type'];
        }
        
        if (isset($data['amount'])) {
            $this->amount = (float) $data['amount'];
        }
        
        if (isset($data['category_id'])) {
            $this->categoryId = (int) $data['category_id'];
        }
        
        if (isset($data['subcategory_id'])) {
            $this->subcategoryId = $data['subcategory_id'] ? (int) $data['subcategory_id'] : null;
        }
        
        if (isset($data['account_id'])) {
            $this->accountId = $data['account_id'] ? (int) $data['account_id'] : null;
        }
        
        if (isset($data['due_date'])) {
            $this->dueDate = new \DateTime($data['due_date']);
        }
        
        if (isset($data['priority'])) {
            $this->priority = $data['priority'];
        }
        
        if (isset($data['status'])) {
            $this->status = $data['status'];
        }
        
        if (isset($data['is_recurring'])) {
            $this->isRecurring = (bool) $data['is_recurring'];
        }
        
        if (isset($data['recurring_type'])) {
            $this->recurringType = $data['recurring_type'];
        }
        
        if (isset($data['recurring_interval'])) {
            $this->recurringInterval = $data['recurring_interval'] ? (int) $data['recurring_interval'] : null;
        }
        
        if (isset($data['recurring_end_date'])) {
            $this->recurringEndDate = $data['recurring_end_date'] ? new \DateTime($data['recurring_end_date']) : null;
        }
        
        if (isset($data['auto_create_transaction'])) {
            $this->autoCreateTransaction = (bool) $data['auto_create_transaction'];
        }
        
        if (isset($data['notification_enabled'])) {
            $this->notificationEnabled = (bool) $data['notification_enabled'];
        }
        
        if (isset($data['notification_days_before'])) {
            $this->notificationDaysBefore = (int) $data['notification_days_before'];
        }
        
        if (isset($data['last_notification_sent'])) {
            $this->lastNotificationSent = $data['last_notification_sent'] ? new \DateTime($data['last_notification_sent']) : null;
        }
        
        if (isset($data['completed_at'])) {
            $this->completedAt = $data['completed_at'] ? new \DateTime($data['completed_at']) : null;
        }
        
        if (isset($data['notes'])) {
            $this->notes = $data['notes'] ? trim($data['notes']) : null;
        }
        
        if (isset($data['created_at'])) {
            $this->createdAt = new \DateTime($data['created_at']);
        }
        
        if (isset($data['updated_at'])) {
            $this->updatedAt = new \DateTime($data['updated_at']);
        }
    }

    /**
     * Convertir a array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->userId,
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'amount' => $this->amount,
            'category_id' => $this->categoryId,
            'subcategory_id' => $this->subcategoryId,
            'account_id' => $this->accountId,
            'due_date' => $this->dueDate->format('Y-m-d H:i:s'),
            'priority' => $this->priority,
            'status' => $this->status,
            'is_recurring' => $this->isRecurring,
            'recurring_type' => $this->recurringType,
            'recurring_interval' => $this->recurringInterval,
            'recurring_end_date' => $this->recurringEndDate?->format('Y-m-d H:i:s'),
            'auto_create_transaction' => $this->autoCreateTransaction,
            'notification_enabled' => $this->notificationEnabled,
            'notification_days_before' => $this->notificationDaysBefore,
            'last_notification_sent' => $this->lastNotificationSent?->format('Y-m-d H:i:s'),
            'completed_at' => $this->completedAt?->format('Y-m-d H:i:s'),
            'notes' => $this->notes,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Convertir a array con información adicional
     */
    public function toDetailedArray(): array
    {
        return array_merge($this->toArray(), [
            'type_label' => self::TYPES[$this->type] ?? $this->type,
            'priority_label' => self::PRIORITIES[$this->priority] ?? $this->priority,
            'status_label' => self::STATUSES[$this->status] ?? $this->status,
            'recurring_type_label' => $this->recurringType ? (self::RECURRING_TYPES[$this->recurringType] ?? $this->recurringType) : null,
            'amount_formatted' => '$ ' . number_format($this->amount, 2, '.', ','),
            'due_date_formatted' => $this->dueDate->format('d/m/Y H:i'),
            'days_until_due' => $this->getDaysUntilDue(),
            'is_overdue' => $this->isOverdue(),
            'is_due_soon' => $this->isDueSoon(),
            'priority_color' => $this->getPriorityColor(),
            'status_color' => $this->getStatusColor()
        ]);
    }

    /**
     * Validar datos del recordatorio
     */
    public function validate(): array
    {
        $errors = [];

        // Validar user_id
        if (empty($this->userId)) {
            $errors['user_id'] = 'El ID de usuario es requerido';
        }

        // Validar título
        if (empty($this->title)) {
            $errors['title'] = 'El título es requerido';
        } elseif (strlen($this->title) < 3) {
            $errors['title'] = 'El título debe tener al menos 3 caracteres';
        } elseif (strlen($this->title) > 255) {
            $errors['title'] = 'El título no puede exceder 255 caracteres';
        }

        // Validar descripción
        if (empty($this->description)) {
            $errors['description'] = 'La descripción es requerida';
        } elseif (strlen($this->description) < 3) {
            $errors['description'] = 'La descripción debe tener al menos 3 caracteres';
        } elseif (strlen($this->description) > 1000) {
            $errors['description'] = 'La descripción no puede exceder 1000 caracteres';
        }

        // Validar tipo
        if (empty($this->type)) {
            $errors['type'] = 'El tipo es requerido';
        } elseif (!array_key_exists($this->type, self::TYPES)) {
            $errors['type'] = 'Tipo inválido';
        }

        // Validar monto
        if (!isset($this->amount)) {
            $errors['amount'] = 'El monto es requerido';
        } elseif (!is_numeric($this->amount)) {
            $errors['amount'] = 'El monto debe ser un número válido';
        } elseif ($this->amount <= 0) {
            $errors['amount'] = 'El monto debe ser mayor a cero';
        }

        // Validar categoría
        if (empty($this->categoryId)) {
            $errors['category_id'] = 'La categoría es requerida';
        }

        // Validar fecha de vencimiento
        if (!$this->dueDate) {
            $errors['due_date'] = 'La fecha de vencimiento es requerida';
        }

        // Validar prioridad
        if (empty($this->priority)) {
            $errors['priority'] = 'La prioridad es requerida';
        } elseif (!array_key_exists($this->priority, self::PRIORITIES)) {
            $errors['priority'] = 'Prioridad inválida';
        }

        // Validar estado
        if (empty($this->status)) {
            $errors['status'] = 'El estado es requerido';
        } elseif (!array_key_exists($this->status, self::STATUSES)) {
            $errors['status'] = 'Estado inválido';
        }

        // Validar recurrencia
        if ($this->isRecurring) {
            if (empty($this->recurringType)) {
                $errors['recurring_type'] = 'El tipo de recurrencia es requerido';
            } elseif (!array_key_exists($this->recurringType, self::RECURRING_TYPES)) {
                $errors['recurring_type'] = 'Tipo de recurrencia inválido';
            }

            if ($this->recurringInterval && $this->recurringInterval < 1) {
                $errors['recurring_interval'] = 'El intervalo debe ser mayor a cero';
            }
        }

        // Validar días de notificación
        if ($this->notificationDaysBefore < 0 || $this->notificationDaysBefore > 365) {
            $errors['notification_days_before'] = 'Los días de notificación deben estar entre 0 y 365';
        }

        // Validar notas
        if ($this->notes && strlen($this->notes) > 2000) {
            $errors['notes'] = 'Las notas no pueden exceder 2000 caracteres';
        }

        return $errors;
    }

    /**
     * Verificar si el recordatorio es válido
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }

    /**
     * Obtener días hasta vencimiento
     */
    public function getDaysUntilDue(): int
    {
        $now = new \DateTime();
        $interval = $now->diff($this->dueDate);
        
        if ($this->dueDate < $now) {
            return -$interval->days; // Negativo si está vencido
        }
        
        return $interval->days;
    }

    /**
     * Verificar si está vencido
     */
    public function isOverdue(): bool
    {
        return $this->dueDate < new \DateTime() && $this->status !== 'completed';
    }

    /**
     * Verificar si vence pronto
     */
    public function isDueSoon(): bool
    {
        $daysUntil = $this->getDaysUntilDue();
        return $daysUntil >= 0 && $daysUntil <= $this->notificationDaysBefore;
    }

    /**
     * Marcar como completado
     */
    public function markAsCompleted(): void
    {
        $this->status = 'completed';
        $this->completedAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    /**
     * Marcar como cancelado
     */
    public function markAsCancelled(): void
    {
        $this->status = 'cancelled';
        $this->updatedAt = new \DateTime();
    }

    /**
     * Actualizar estado automáticamente
     */
    public function updateStatus(): void
    {
        if ($this->status === 'completed' || $this->status === 'cancelled') {
            return; // No cambiar estados finales
        }

        if ($this->isOverdue()) {
            $this->status = 'overdue';
        } else {
            $this->status = 'pending';
        }
        
        $this->updatedAt = new \DateTime();
    }

    /**
     * Calcular próxima fecha de recurrencia
     */
    public function getNextRecurringDate(): ?\DateTime
    {
        if (!$this->isRecurring || !$this->recurringType) {
            return null;
        }

        $interval = $this->recurringInterval ?: 1;
        $nextDate = clone $this->dueDate;

        switch ($this->recurringType) {
            case 'daily':
                $nextDate->add(new \DateInterval("P{$interval}D"));
                break;
            case 'weekly':
                $nextDate->add(new \DateInterval("P" . ($interval * 7) . "D"));
                break;
            case 'monthly':
                $nextDate->add(new \DateInterval("P{$interval}M"));
                break;
            case 'quarterly':
                $nextDate->add(new \DateInterval("P" . ($interval * 3) . "M"));
                break;
            case 'yearly':
                $nextDate->add(new \DateInterval("P{$interval}Y"));
                break;
            default:
                return null;
        }

        // Verificar si excede la fecha de fin
        if ($this->recurringEndDate && $nextDate > $this->recurringEndDate) {
            return null;
        }

        return $nextDate;
    }

    /**
     * Obtener color de prioridad
     */
    public function getPriorityColor(): string
    {
        return match($this->priority) {
            'low' => '#28a745',
            'medium' => '#ffc107',
            'high' => '#fd7e14',
            'urgent' => '#dc3545',
            default => '#6c757d'
        };
    }

    /**
     * Obtener color de estado
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            'pending' => '#007bff',
            'completed' => '#28a745',
            'overdue' => '#dc3545',
            'cancelled' => '#6c757d',
            default => '#6c757d'
        };
    }

    /**
     * Verificar si necesita notificación
     */
    public function needsNotification(): bool
    {
        if (!$this->notificationEnabled || $this->status === 'completed' || $this->status === 'cancelled') {
            return false;
        }

        // Verificar si ya se envió notificación hoy
        if ($this->lastNotificationSent && 
            $this->lastNotificationSent->format('Y-m-d') === (new \DateTime())->format('Y-m-d')) {
            return false;
        }

        return $this->isDueSoon() || $this->isOverdue();
    }

    /**
     * Marcar notificación como enviada
     */
    public function markNotificationSent(): void
    {
        $this->lastNotificationSent = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getUserId(): int { return $this->userId; }
    public function getTitle(): string { return $this->title; }
    public function getDescription(): string { return $this->description; }
    public function getType(): string { return $this->type; }
    public function getAmount(): float { return $this->amount; }
    public function getCategoryId(): int { return $this->categoryId; }
    public function getSubcategoryId(): ?int { return $this->subcategoryId; }
    public function getAccountId(): ?int { return $this->accountId; }
    public function getDueDate(): \DateTime { return $this->dueDate; }
    public function getPriority(): string { return $this->priority; }
    public function getStatus(): string { return $this->status; }
    public function isRecurring(): bool { return $this->isRecurring; }
    public function getRecurringType(): ?string { return $this->recurringType; }
    public function getRecurringInterval(): ?int { return $this->recurringInterval; }
    public function getRecurringEndDate(): ?\DateTime { return $this->recurringEndDate; }
    public function getAutoCreateTransaction(): bool { return $this->autoCreateTransaction; }
    public function isNotificationEnabled(): bool { return $this->notificationEnabled; }
    public function getNotificationDaysBefore(): int { return $this->notificationDaysBefore; }
    public function getLastNotificationSent(): ?\DateTime { return $this->lastNotificationSent; }
    public function getCompletedAt(): ?\DateTime { return $this->completedAt; }
    public function getNotes(): ?string { return $this->notes; }
    public function getCreatedAt(): \DateTime { return $this->createdAt; }
    public function getUpdatedAt(): \DateTime { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setUserId(int $userId): void { $this->userId = $userId; }
    public function setTitle(string $title): void { $this->title = trim($title); $this->updatedAt = new \DateTime(); }
    public function setDescription(string $description): void { $this->description = trim($description); $this->updatedAt = new \DateTime(); }
    public function setType(string $type): void { $this->type = $type; $this->updatedAt = new \DateTime(); }
    public function setAmount(float $amount): void { $this->amount = $amount; $this->updatedAt = new \DateTime(); }
    public function setCategoryId(int $categoryId): void { $this->categoryId = $categoryId; $this->updatedAt = new \DateTime(); }
    public function setSubcategoryId(?int $subcategoryId): void { $this->subcategoryId = $subcategoryId; $this->updatedAt = new \DateTime(); }
    public function setAccountId(?int $accountId): void { $this->accountId = $accountId; $this->updatedAt = new \DateTime(); }
    public function setDueDate(\DateTime $dueDate): void { $this->dueDate = $dueDate; $this->updatedAt = new \DateTime(); }
    public function setPriority(string $priority): void { $this->priority = $priority; $this->updatedAt = new \DateTime(); }
    public function setStatus(string $status): void { $this->status = $status; $this->updatedAt = new \DateTime(); }
    public function setIsRecurring(bool $isRecurring): void { $this->isRecurring = $isRecurring; $this->updatedAt = new \DateTime(); }
    public function setRecurringType(?string $recurringType): void { $this->recurringType = $recurringType; $this->updatedAt = new \DateTime(); }
    public function setRecurringInterval(?int $recurringInterval): void { $this->recurringInterval = $recurringInterval; $this->updatedAt = new \DateTime(); }
    public function setRecurringEndDate(?\DateTime $recurringEndDate): void { $this->recurringEndDate = $recurringEndDate; $this->updatedAt = new \DateTime(); }
    public function setAutoCreateTransaction(bool $autoCreateTransaction): void { $this->autoCreateTransaction = $autoCreateTransaction; $this->updatedAt = new \DateTime(); }
    public function setNotificationEnabled(bool $notificationEnabled): void { $this->notificationEnabled = $notificationEnabled; $this->updatedAt = new \DateTime(); }
    public function setNotificationDaysBefore(int $notificationDaysBefore): void { $this->notificationDaysBefore = $notificationDaysBefore; $this->updatedAt = new \DateTime(); }
    public function setLastNotificationSent(?\DateTime $lastNotificationSent): void { $this->lastNotificationSent = $lastNotificationSent; }
    public function setCompletedAt(?\DateTime $completedAt): void { $this->completedAt = $completedAt; }
    public function setNotes(?string $notes): void { $this->notes = $notes ? trim($notes) : null; $this->updatedAt = new \DateTime(); }
    public function setUpdatedAt(\DateTime $updatedAt): void { $this->updatedAt = $updatedAt; }
}
