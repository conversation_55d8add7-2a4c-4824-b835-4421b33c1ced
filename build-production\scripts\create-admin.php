<?php
/**
 * Crear usuario administrador
 */

require_once '../src/bootstrap.php';

echo "<h1>👤 Crear Usuario Administrador</h1>";

if ($_POST) {
    try {
        $userService = $container->get('userService');
        
        $userData = [
            'first_name' => $_POST['first_name'],
            'last_name' => $_POST['last_name'],
            'email' => $_POST['email'],
            'password' => $_POST['password'],
            'role' => 'admin',
            'is_verified' => true
        ];
        
        $userId = $userService->createUser($userData);
        
        echo "<h2>✅ Usuario administrador creado exitosamente</h2>";
        echo "<p>ID: $userId</p>";
        echo "<p>Email: {$userData['email']}</p>";
        echo "<p><a href='../public/'>Ir a la aplicación</a></p>";
        
    } catch (Exception $e) {
        echo "<h2>❌ Error:</h2>";
        echo "<p>" . $e->getMessage() . "</p>";
    }
} else {
?>
<form method="POST">
    <table>
        <tr>
            <td>Nombre:</td>
            <td><input type="text" name="first_name" required></td>
        </tr>
        <tr>
            <td>Apellido:</td>
            <td><input type="text" name="last_name" required></td>
        </tr>
        <tr>
            <td>Email:</td>
            <td><input type="email" name="email" required></td>
        </tr>
        <tr>
            <td>Contraseña:</td>
            <td><input type="password" name="password" required minlength="8"></td>
        </tr>
        <tr>
            <td colspan="2">
                <button type="submit">Crear Administrador</button>
            </td>
        </tr>
    </table>
</form>
<?php } ?>
