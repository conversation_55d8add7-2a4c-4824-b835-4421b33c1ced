<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Mis Cuentas</h2>
    <div>
        <a href="/accounts/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nueva Cuenta
        </a>
        <a href="/accounts/transfer" class="btn btn-outline-secondary">
            <i class="fas fa-exchange-alt"></i> Transferir
        </a>
    </div>
</div>

<!-- Resumen Financiero -->
<?php if (!empty($summary)): ?>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">Total de Cuentas</h5>
                <h3><?= $summary['total_accounts'] ?></h3>
                <small class="text-muted"><?= $summary['active_accounts'] ?> activas</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">Balance Total</h5>
                <h3 class="<?= $summary['total_balance'] >= 0 ? 'balance-positive' : 'balance-negative' ?>">
                    $ <?= number_format($summary['total_balance'], 2) ?>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Efectivo</h5>
                <h3>$ <?= number_format($summary['cash_balance'], 2) ?></h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">Bancos</h5>
                <h3>$ <?= number_format($summary['bank_balance'], 2) ?></h3>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Lista de Cuentas -->
<?php if (empty($accounts)): ?>
    <div class="text-center py-5">
        <i class="fas fa-university fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No tienes cuentas registradas</h4>
        <p class="text-muted">Crea tu primera cuenta para comenzar a gestionar tus finanzas</p>
        <a href="/accounts/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Crear Primera Cuenta
        </a>
    </div>
<?php else: ?>
    <div class="row">
        <?php foreach ($accounts as $account): ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card account-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="<?= getAccountIcon($account['type']) ?> me-2"></i>
                            <strong><?= htmlspecialchars($account['name']) ?></strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/accounts/<?= $account['id'] ?>">
                                    <i class="fas fa-eye"></i> Ver Detalles
                                </a></li>
                                <li><a class="dropdown-item" href="/accounts/<?= $account['id'] ?>/edit">
                                    <i class="fas fa-edit"></i> Editar
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteAccount(<?= $account['id'] ?>)">
                                    <i class="fas fa-trash"></i> Eliminar
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary"><?= htmlspecialchars($account['type_label']) ?></span>
                            <span class="badge <?= $account['is_active'] ? 'bg-success' : 'bg-danger' ?>">
                                <?= $account['status_label'] ?>
                            </span>
                        </div>
                        
                        <h4 class="<?= getBalanceClass($account['balance']) ?>">
                            <?= htmlspecialchars($account['balance_formatted']) ?>
                        </h4>
                        
                        <?php if ($account['description']): ?>
                            <p class="card-text text-muted small">
                                <?= htmlspecialchars($account['description']) ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <?= htmlspecialchars($account['currency_label']) ?>
                            </small>
                            <small class="text-muted">
                                Creada: <?= date('d/m/Y', strtotime($account['created_at'])) ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<script>
function deleteAccount(accountId) {
    if (confirm('¿Está seguro de que desea eliminar esta cuenta?')) {
        fetch(`/accounts/${accountId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-Token': '<?= $csrf_token ?? '' ?>'
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error al eliminar la cuenta');
            }
        })
        .catch(error => {
            alert('Error al eliminar la cuenta');
        });
    }
}
</script>

<?php
$content = ob_get_clean();

// Funciones auxiliares
function getAccountIcon($type) {
    $icons = [
        'cash' => 'fas fa-money-bill-wave',
        'bank' => 'fas fa-university',
        'credit_card' => 'fas fa-credit-card',
        'debit_card' => 'fas fa-credit-card',
        'savings' => 'fas fa-piggy-bank',
        'investment' => 'fas fa-chart-line'
    ];
    return $icons[$type] ?? 'fas fa-wallet';
}

function getBalanceClass($balance) {
    if ($balance > 0) return 'balance-positive';
    if ($balance < 0) return 'balance-negative';
    return 'balance-zero';
}

include __DIR__ . '/../../layouts/main.php';
?>
