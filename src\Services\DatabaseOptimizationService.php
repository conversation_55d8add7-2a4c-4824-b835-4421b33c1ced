<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Core\Database;
use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de optimización de base de datos
 * Maneja la optimización de consultas, índices y rendimiento de la base de datos
 */
class DatabaseOptimizationService
{
    private Database $database;
    private Logger $logger;
    private CacheService $cache;
    private array $queryLog;
    private float $slowQueryThreshold;

    public function __construct(Database $database, Logger $logger, CacheService $cache)
    {
        $this->database = $database;
        $this->logger = $logger;
        $this->cache = $cache;
        $this->queryLog = [];
        $this->slowQueryThreshold = 1.0; // 1 segundo
    }

    /**
     * Ejecutar consulta con cache y optimización
     */
    public function executeOptimizedQuery(string $query, array $params = [], int $cacheTtl = 300): array
    {
        $startTime = microtime(true);
        
        try {
            // Generar clave de cache
            $cacheKey = $this->cache->generateQueryKey($query, $params);
            
            // Intentar obtener del cache primero
            $cachedResult = $this->cache->get($cacheKey);
            if ($cachedResult !== null) {
                $this->logQuery($query, $params, microtime(true) - $startTime, true);
                return $cachedResult;
            }

            // Ejecutar consulta
            $result = $this->database->select($query, $params);
            
            // Guardar en cache si es una consulta SELECT
            if (stripos(trim($query), 'SELECT') === 0 && $cacheTtl > 0) {
                $this->cache->set($cacheKey, $result, $cacheTtl);
            }

            $executionTime = microtime(true) - $startTime;
            $this->logQuery($query, $params, $executionTime, false);

            return $result;

        } catch (Exception $e) {
            $this->logger->error('Error en consulta optimizada: ' . $e->getMessage(), [
                'query' => $query,
                'params' => $params
            ]);
            throw $e;
        }
    }

    /**
     * Analizar rendimiento de consultas
     */
    public function analyzeQueryPerformance(): array
    {
        try {
            $analysis = [
                'slow_queries' => [],
                'most_frequent' => [],
                'cache_hit_rate' => 0,
                'total_queries' => count($this->queryLog),
                'avg_execution_time' => 0,
                'recommendations' => []
            ];

            if (empty($this->queryLog)) {
                return $analysis;
            }

            // Analizar consultas lentas
            $slowQueries = array_filter($this->queryLog, function($log) {
                return $log['execution_time'] > $this->slowQueryThreshold && !$log['from_cache'];
            });

            $analysis['slow_queries'] = array_slice($slowQueries, 0, 10);

            // Calcular estadísticas
            $totalTime = array_sum(array_column($this->queryLog, 'execution_time'));
            $cacheHits = count(array_filter($this->queryLog, fn($log) => $log['from_cache']));
            
            $analysis['avg_execution_time'] = $totalTime / count($this->queryLog);
            $analysis['cache_hit_rate'] = ($cacheHits / count($this->queryLog)) * 100;

            // Consultas más frecuentes
            $queryFrequency = [];
            foreach ($this->queryLog as $log) {
                $queryHash = md5($log['query']);
                $queryFrequency[$queryHash] = ($queryFrequency[$queryHash] ?? 0) + 1;
            }
            
            arsort($queryFrequency);
            $analysis['most_frequent'] = array_slice($queryFrequency, 0, 5, true);

            // Generar recomendaciones
            $analysis['recommendations'] = $this->generateOptimizationRecommendations($analysis);

            return $analysis;

        } catch (Exception $e) {
            $this->logger->error('Error analizando rendimiento: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Optimizar índices de base de datos
     */
    public function optimizeIndexes(): array
    {
        try {
            $results = [];
            $tables = $this->getTableList();

            foreach ($tables as $table) {
                $result = $this->optimizeTableIndexes($table);
                $results[$table] = $result;
            }

            $this->logger->info('Optimización de índices completada', $results);
            return $results;

        } catch (Exception $e) {
            $this->logger->error('Error optimizando índices: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Analizar uso de índices
     */
    public function analyzeIndexUsage(): array
    {
        try {
            $query = "
                SELECT 
                    TABLE_NAME,
                    INDEX_NAME,
                    COLUMN_NAME,
                    CARDINALITY,
                    SUB_PART,
                    NULLABLE
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE()
                ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
            ";

            $indexes = $this->database->select($query);
            
            $analysis = [
                'total_indexes' => count($indexes),
                'by_table' => [],
                'unused_indexes' => [],
                'recommendations' => []
            ];

            // Agrupar por tabla
            foreach ($indexes as $index) {
                $table = $index['TABLE_NAME'];
                if (!isset($analysis['by_table'][$table])) {
                    $analysis['by_table'][$table] = [];
                }
                $analysis['by_table'][$table][] = $index;
            }

            // Detectar índices potencialmente no utilizados
            $analysis['unused_indexes'] = $this->detectUnusedIndexes();

            // Generar recomendaciones de índices
            $analysis['recommendations'] = $this->generateIndexRecommendations();

            return $analysis;

        } catch (Exception $e) {
            $this->logger->error('Error analizando índices: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Limpiar cache de consultas
     */
    public function clearQueryCache(): bool
    {
        try {
            // Limpiar cache específico de consultas
            $this->cache->clear();
            
            $this->logger->info('Cache de consultas limpiado');
            return true;

        } catch (Exception $e) {
            $this->logger->error('Error limpiando cache de consultas: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtener estadísticas de rendimiento de la base de datos
     */
    public function getDatabaseStats(): array
    {
        try {
            $stats = [];

            // Estadísticas generales
            $generalStats = $this->database->select("SHOW STATUS LIKE 'Questions'");
            $stats['total_queries'] = $generalStats[0]['Value'] ?? 0;

            // Estadísticas de cache
            $cacheStats = $this->database->select("SHOW STATUS LIKE 'Qcache%'");
            foreach ($cacheStats as $stat) {
                $stats['query_cache'][$stat['Variable_name']] = $stat['Value'];
            }

            // Estadísticas de conexiones
            $connectionStats = $this->database->select("SHOW STATUS LIKE 'Connections'");
            $stats['connections'] = $connectionStats[0]['Value'] ?? 0;

            // Tamaño de las tablas
            $tableSizes = $this->database->select("
                SELECT 
                    TABLE_NAME,
                    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS size_mb,
                    TABLE_ROWS
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = DATABASE()
                ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC
            ");
            $stats['table_sizes'] = $tableSizes;

            // Estadísticas de nuestro log de consultas
            $stats['app_query_stats'] = [
                'total_logged' => count($this->queryLog),
                'slow_queries' => count(array_filter($this->queryLog, fn($log) => $log['execution_time'] > $this->slowQueryThreshold)),
                'cache_hits' => count(array_filter($this->queryLog, fn($log) => $log['from_cache']))
            ];

            return $stats;

        } catch (Exception $e) {
            $this->logger->error('Error obteniendo estadísticas de BD: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Optimizar tabla específica
     */
    public function optimizeTable(string $tableName): array
    {
        try {
            $result = [];

            // ANALYZE TABLE
            $analyzeResult = $this->database->select("ANALYZE TABLE `{$tableName}`");
            $result['analyze'] = $analyzeResult;

            // OPTIMIZE TABLE
            $optimizeResult = $this->database->select("OPTIMIZE TABLE `{$tableName}`");
            $result['optimize'] = $optimizeResult;

            $this->logger->info("Tabla {$tableName} optimizada", $result);
            return $result;

        } catch (Exception $e) {
            $this->logger->error("Error optimizando tabla {$tableName}: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Generar consulta optimizada para transacciones por usuario
     */
    public function getOptimizedUserTransactions(int $userId, array $filters = [], int $limit = 20, int $offset = 0): array
    {
        $cacheKey = "user_transactions:{$userId}:" . md5(serialize($filters)) . ":{$limit}:{$offset}";
        
        return $this->cache->remember($cacheKey, function() use ($userId, $filters, $limit, $offset) {
            $whereConditions = ['t.user_id = :user_id'];
            $params = ['user_id' => $userId];

            // Aplicar filtros
            if (!empty($filters['start_date'])) {
                $whereConditions[] = 't.transaction_date >= :start_date';
                $params['start_date'] = $filters['start_date'];
            }

            if (!empty($filters['end_date'])) {
                $whereConditions[] = 't.transaction_date <= :end_date';
                $params['end_date'] = $filters['end_date'];
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = 't.category_id = :category_id';
                $params['category_id'] = $filters['category_id'];
            }

            if (!empty($filters['account_id'])) {
                $whereConditions[] = 't.account_id = :account_id';
                $params['account_id'] = $filters['account_id'];
            }

            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

            $query = "
                SELECT 
                    t.*,
                    c.name as category_name,
                    c.color as category_color,
                    c.icon as category_icon,
                    s.name as subcategory_name,
                    a.name as account_name
                FROM transactions t
                INNER JOIN categories c ON t.category_id = c.id
                LEFT JOIN subcategories s ON t.subcategory_id = s.id
                INNER JOIN accounts a ON t.account_id = a.id
                {$whereClause}
                ORDER BY t.transaction_date DESC, t.id DESC
                LIMIT :limit OFFSET :offset
            ";

            $params['limit'] = $limit;
            $params['offset'] = $offset;

            return $this->database->select($query, $params);
        }, 300); // Cache por 5 minutos
    }

    /**
     * Registrar consulta en el log
     */
    private function logQuery(string $query, array $params, float $executionTime, bool $fromCache): void
    {
        $this->queryLog[] = [
            'query' => $query,
            'params' => $params,
            'execution_time' => $executionTime,
            'from_cache' => $fromCache,
            'timestamp' => microtime(true)
        ];

        // Mantener solo las últimas 1000 consultas en memoria
        if (count($this->queryLog) > 1000) {
            array_shift($this->queryLog);
        }

        // Log de consultas lentas
        if ($executionTime > $this->slowQueryThreshold && !$fromCache) {
            $this->logger->warning('Consulta lenta detectada', [
                'query' => $query,
                'execution_time' => $executionTime,
                'params' => $params
            ]);
        }
    }

    /**
     * Obtener lista de tablas
     */
    private function getTableList(): array
    {
        $result = $this->database->select("SHOW TABLES");
        return array_column($result, array_keys($result[0])[0]);
    }

    /**
     * Optimizar índices de una tabla específica
     */
    private function optimizeTableIndexes(string $table): array
    {
        try {
            $result = [
                'table' => $table,
                'actions' => [],
                'recommendations' => []
            ];

            // Analizar la tabla
            $analyzeResult = $this->database->select("ANALYZE TABLE `{$table}`");
            $result['actions'][] = 'ANALYZE completed';

            // Verificar índices existentes
            $indexes = $this->database->select("SHOW INDEX FROM `{$table}`");
            $result['current_indexes'] = count($indexes);

            // Generar recomendaciones específicas para la tabla
            $result['recommendations'] = $this->getTableSpecificRecommendations($table);

            return $result;

        } catch (Exception $e) {
            return [
                'table' => $table,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Detectar índices no utilizados
     */
    private function detectUnusedIndexes(): array
    {
        try {
            // Esta es una implementación simplificada
            // En producción se podría usar PERFORMANCE_SCHEMA para detectar índices realmente no utilizados
            $query = "
                SELECT 
                    TABLE_NAME,
                    INDEX_NAME,
                    COUNT(*) as column_count
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE()
                AND INDEX_NAME != 'PRIMARY'
                GROUP BY TABLE_NAME, INDEX_NAME
                HAVING column_count = 1
            ";

            return $this->database->select($query);

        } catch (Exception $e) {
            $this->logger->error('Error detectando índices no utilizados: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Generar recomendaciones de índices
     */
    private function generateIndexRecommendations(): array
    {
        $recommendations = [];

        // Recomendaciones basadas en consultas comunes
        $commonQueries = [
            'transactions' => [
                'user_id + transaction_date' => 'Para consultas de transacciones por usuario y fecha',
                'category_id + transaction_date' => 'Para reportes por categoría',
                'account_id + transaction_date' => 'Para consultas por cuenta'
            ],
            'reminders' => [
                'user_id + due_date' => 'Para recordatorios por usuario y fecha',
                'status + due_date' => 'Para recordatorios por estado'
            ]
        ];

        foreach ($commonQueries as $table => $indexes) {
            foreach ($indexes as $columns => $description) {
                $recommendations[] = [
                    'table' => $table,
                    'columns' => $columns,
                    'description' => $description,
                    'priority' => 'medium'
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Generar recomendaciones de optimización
     */
    private function generateOptimizationRecommendations(array $analysis): array
    {
        $recommendations = [];

        // Recomendaciones basadas en cache hit rate
        if ($analysis['cache_hit_rate'] < 50) {
            $recommendations[] = [
                'type' => 'cache',
                'priority' => 'high',
                'description' => 'Baja tasa de aciertos de cache. Considerar aumentar TTL de cache o revisar patrones de consulta.'
            ];
        }

        // Recomendaciones basadas en consultas lentas
        if (count($analysis['slow_queries']) > 0) {
            $recommendations[] = [
                'type' => 'performance',
                'priority' => 'high',
                'description' => 'Se detectaron consultas lentas. Revisar índices y optimizar consultas.'
            ];
        }

        // Recomendaciones basadas en tiempo promedio
        if ($analysis['avg_execution_time'] > 0.5) {
            $recommendations[] = [
                'type' => 'performance',
                'priority' => 'medium',
                'description' => 'Tiempo promedio de ejecución alto. Considerar optimización general.'
            ];
        }

        return $recommendations;
    }

    /**
     * Obtener recomendaciones específicas para una tabla
     */
    private function getTableSpecificRecommendations(string $table): array
    {
        $recommendations = [];

        switch ($table) {
            case 'transactions':
                $recommendations[] = 'Considerar particionado por fecha para mejorar rendimiento';
                $recommendations[] = 'Índice compuesto en (user_id, transaction_date) recomendado';
                break;
            case 'audit_log':
                $recommendations[] = 'Implementar archivado automático de registros antiguos';
                $recommendations[] = 'Índice en created_at para consultas temporales';
                break;
            case 'login_attempts':
                $recommendations[] = 'Limpieza automática de intentos antiguos';
                $recommendations[] = 'Índice compuesto en (email, attempted_at)';
                break;
        }

        return $recommendations;
    }
}
