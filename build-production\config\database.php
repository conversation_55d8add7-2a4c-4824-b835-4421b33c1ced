<?php
/**
 * Configuración de Base de Datos
 * Control de Gastos Personales
 */

return [
    // Configuración principal de la base de datos
    'default' => $_ENV['DB_CONNECTION'] ?? 'mysql',
    
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'database' => $_ENV['DB_DATABASE'] ?? 'control_gastos',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => 'InnoDB',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]
        ],
        
        'testing' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_TEST_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_TEST_PORT'] ?? '3306',
            'database' => $_ENV['DB_TEST_DATABASE'] ?? 'control_gastos_test',
            'username' => $_ENV['DB_TEST_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_TEST_PASSWORD'] ?? '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => 'InnoDB',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        ]
    ],

    // Configuración de pool de conexiones
    'pool' => [
        'max_connections' => 10,
        'min_connections' => 2,
        'connection_timeout' => 30,
        'idle_timeout' => 300
    ],

    // Configuración de migraciones
    'migrations' => [
        'table' => 'migrations',
        'path' => __DIR__ . '/../database/migrations'
    ],

    // Configuración de backup de base de datos
    'backup' => [
        'mysqldump_path' => $_ENV['MYSQLDUMP_PATH'] ?? 'mysqldump',
        'mysql_path' => $_ENV['MYSQL_PATH'] ?? 'mysql',
        'compression' => true,
        'add_drop_table' => true,
        'add_drop_database' => false,
        'single_transaction' => true,
        'lock_tables' => false,
        'add_locks' => true,
        'extended_insert' => true,
        'disable_keys' => true,
        'where' => '',
        'no_data' => false
    ]
];
