<?php
/**
 * Script para crear categorías de ejemplo
 * Control de Gastos - Desarrollo Local
 */

// Configuración de base de datos
$host = 'localhost';
$dbname = 'control_gastos';
$username = 'root';
$password = '';

try {
    echo "🏷️ CREANDO CATEGORÍAS DE EJEMPLO\n";
    echo "================================\n\n";

    // Conectar a la base de datos
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "✅ Conexión a base de datos establecida\n";

    // Obtener el ID del usuario (asumiendo que hay al menos un usuario)
    $stmt = $pdo->query("SELECT id FROM users ORDER BY id ASC LIMIT 1");
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "❌ No se encontró ningún usuario en la base de datos\n";
        echo "   Ejecuta primero create-admin-local.php para crear un usuario\n";
        exit(1);
    }

    $userId = $user['id'];
    echo "👤 Usuario encontrado: ID $userId\n";

    // Verificar si ya existen categorías para este usuario
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories WHERE user_id = ?");
    $stmt->execute([$userId]);
    $existingCategories = $stmt->fetchColumn();

    if ($existingCategories > 0) {
        echo "⚠️  Ya existen $existingCategories categorías para este usuario\n";
        echo "   ¿Deseas continuar y agregar más categorías? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $response = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($response) !== 'y' && strtolower($response) !== 'yes') {
            echo "❌ Operación cancelada\n";
            exit(0);
        }
    }

    echo "\n📝 Creando categorías de ejemplo...\n";

    // Categorías de gastos
    $expenseCategories = [
        ['Hogar', 'Gastos relacionados con el hogar', '#28a745', 'fas fa-home'],
        ['Alimentación', 'Gastos en comida y bebidas', '#fd7e14', 'fas fa-utensils'],
        ['Transporte', 'Gastos de movilidad', '#007bff', 'fas fa-car'],
        ['Salud', 'Gastos médicos y de bienestar', '#dc3545', 'fas fa-heartbeat'],
        ['Entretenimiento', 'Gastos de ocio y diversión', '#6f42c1', 'fas fa-gamepad'],
        ['Educación', 'Gastos educativos y formación', '#20c997', 'fas fa-graduation-cap'],
        ['Ropa', 'Vestimenta y accesorios', '#e83e8c', 'fas fa-tshirt'],
        ['Servicios', 'Servicios básicos y suscripciones', '#17a2b8', 'fas fa-cog'],
        ['Tecnología', 'Dispositivos y software', '#6c757d', 'fas fa-laptop'],
        ['Mascotas', 'Gastos de mascotas', '#ffc107', 'fas fa-paw']
    ];

    // Categorías de ingresos
    $incomeCategories = [
        ['Salario', 'Ingresos por trabajo', '#198754', 'fas fa-money-bill-wave'],
        ['Freelance', 'Trabajos independientes', '#20c997', 'fas fa-laptop'],
        ['Inversiones', 'Dividendos e intereses', '#6610f2', 'fas fa-chart-line'],
        ['Ventas', 'Ingresos por ventas', '#fd7e14', 'fas fa-shopping-bag'],
        ['Otros Ingresos', 'Regalos, bonos, otros', '#6c757d', 'fas fa-plus-circle']
    ];

    $allCategories = array_merge($expenseCategories, $incomeCategories);

    // Insertar categorías
    $sql = "INSERT INTO categories (user_id, name, description, color, icon) VALUES (?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);

    $createdCount = 0;
    foreach ($allCategories as $category) {
        try {
            $stmt->execute([$userId, $category[0], $category[1], $category[2], $category[3]]);
            echo "  ✅ {$category[0]}\n";
            $createdCount++;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                echo "  ⚠️  {$category[0]} (ya existe)\n";
            } else {
                echo "  ❌ Error creando {$category[0]}: " . $e->getMessage() . "\n";
            }
        }
    }

    echo "\n📂 Creando subcategorías de ejemplo...\n";

    // Subcategorías para algunas categorías principales
    $subcategoriesData = [
        'Hogar' => ['Arriendo/Hipoteca', 'Servicios Públicos', 'Mantenimiento', 'Decoración', 'Seguros'],
        'Alimentación' => ['Supermercado', 'Restaurantes', 'Comida Rápida', 'Bebidas', 'Delivery'],
        'Transporte' => ['Combustible', 'Transporte Público', 'Taxi/Uber', 'Mantenimiento Vehículo', 'Parqueadero'],
        'Salud' => ['Medicamentos', 'Consultas Médicas', 'Exámenes', 'Seguros Médicos', 'Gimnasio'],
        'Entretenimiento' => ['Cine', 'Streaming', 'Videojuegos', 'Libros', 'Eventos'],
        'Educación' => ['Cursos', 'Libros Académicos', 'Certificaciones', 'Material de Estudio', 'Matrícula'],
        'Salario' => ['Salario Base', 'Horas Extra', 'Bonificaciones', 'Comisiones', 'Aguinaldo'],
        'Freelance' => ['Desarrollo Web', 'Diseño Gráfico', 'Consultoría', 'Redacción', 'Otros Proyectos']
    ];

    // Obtener IDs de categorías creadas
    $stmt = $pdo->prepare("SELECT id, name FROM categories WHERE user_id = ? AND name = ?");
    $subcategoryStmt = $pdo->prepare("INSERT INTO subcategories (category_id, name) VALUES (?, ?)");

    $subcategoriesCreated = 0;
    foreach ($subcategoriesData as $categoryName => $subcategories) {
        $stmt->execute([$userId, $categoryName]);
        $category = $stmt->fetch();
        
        if ($category) {
            echo "  📁 {$categoryName}:\n";
            foreach ($subcategories as $subcategoryName) {
                try {
                    $subcategoryStmt->execute([$category['id'], $subcategoryName]);
                    echo "    ✅ {$subcategoryName}\n";
                    $subcategoriesCreated++;
                } catch (PDOException $e) {
                    echo "    ⚠️  {$subcategoryName} (ya existe o error)\n";
                }
            }
        }
    }

    echo "\n🎉 PROCESO COMPLETADO\n";
    echo "====================\n";
    echo "✅ Categorías creadas: $createdCount\n";
    echo "✅ Subcategorías creadas: $subcategoriesCreated\n";
    echo "👤 Usuario ID: $userId\n";
    echo "\n🌐 Puedes ver las categorías en:\n";
    echo "   http://localhost/controlGastos/public/?route=categories\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
