<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Repositories\CategoryRepository;
use ControlGastos\Repositories\SubcategoryRepository;
use ControlGastos\Models\Category;
use ControlGastos\Models\Subcategory;
use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de gestión de categorías y subcategorías
 * Maneja la lógica de negocio para categorías
 */
class CategoryService
{
    private CategoryRepository $categoryRepository;
    private SubcategoryRepository $subcategoryRepository;
    private Logger $logger;

    public function __construct(
        CategoryRepository $categoryRepository,
        SubcategoryRepository $subcategoryRepository,
        Logger $logger
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->subcategoryRepository = $subcategoryRepository;
        $this->logger = $logger;
    }

    /**
     * Crear nueva categoría
     */
    public function createCategory(array $data, int $userId): array
    {
        try {
            // Crear instancia de categoría
            $category = new Category();
            $category->setUserId($userId);
            $category->setName($data['name']);
            $category->setDescription($data['description'] ?? null);
            $category->setColor($data['color'] ?? '#007bff');
            $category->setIcon($data['icon'] ?? 'fas fa-folder');

            // Validar datos
            $errors = $category->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Verificar si el nombre ya existe
            if ($this->categoryRepository->nameExists($category->getName(), $userId)) {
                return [
                    'success' => false,
                    'message' => 'Ya existe una categoría con ese nombre'
                ];
            }

            // Crear categoría
            $category = $this->categoryRepository->create($category);

            $this->logger->info('Categoría creada', [
                'category_id' => $category->getId(),
                'user_id' => $userId,
                'name' => $category->getName()
            ]);

            return [
                'success' => true,
                'message' => 'Categoría creada exitosamente',
                'category' => $category->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al crear categoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Actualizar categoría existente
     */
    public function updateCategory(int $categoryId, array $data, int $userId): array
    {
        try {
            // Buscar categoría
            $category = $this->categoryRepository->findByIdAndUser($categoryId, $userId);
            if (!$category) {
                return [
                    'success' => false,
                    'message' => 'Categoría no encontrada'
                ];
            }

            // Actualizar datos
            if (isset($data['name'])) {
                $category->setName($data['name']);
            }
            
            if (isset($data['description'])) {
                $category->setDescription($data['description']);
            }
            
            if (isset($data['color'])) {
                $category->setColor($data['color']);
            }
            
            if (isset($data['icon'])) {
                $category->setIcon($data['icon']);
            }

            // Validar datos
            $errors = $category->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Verificar si el nombre ya existe (excluyendo la categoría actual)
            if ($this->categoryRepository->nameExists($category->getName(), $userId, $categoryId)) {
                return [
                    'success' => false,
                    'message' => 'Ya existe una categoría con ese nombre'
                ];
            }

            // Actualizar categoría
            $this->categoryRepository->update($category);

            $this->logger->info('Categoría actualizada', [
                'category_id' => $category->getId(),
                'user_id' => $userId,
                'name' => $category->getName()
            ]);

            return [
                'success' => true,
                'message' => 'Categoría actualizada exitosamente',
                'category' => $category->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al actualizar categoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Eliminar categoría
     */
    public function deleteCategory(int $categoryId, int $userId): array
    {
        try {
            // Buscar categoría
            $category = $this->categoryRepository->findByIdAndUser($categoryId, $userId);
            if (!$category) {
                return [
                    'success' => false,
                    'message' => 'Categoría no encontrada'
                ];
            }

            // Verificar si la categoría tiene transacciones
            if ($this->hasTransactions($categoryId)) {
                // Solo desactivar si tiene transacciones
                $category->deactivate();
                $this->categoryRepository->update($category);
                
                // También desactivar subcategorías
                $this->subcategoryRepository->deleteByCategory($categoryId);
                
                $message = 'Categoría desactivada (tiene transacciones asociadas)';
            } else {
                // Eliminar permanentemente si no tiene transacciones
                $this->categoryRepository->forceDelete($categoryId);
                $message = 'Categoría eliminada exitosamente';
            }

            $this->logger->info('Categoría eliminada/desactivada', [
                'category_id' => $categoryId,
                'user_id' => $userId,
                'name' => $category->getName()
            ]);

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al eliminar categoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener categoría por ID
     */
    public function getCategory(int $categoryId, int $userId): array
    {
        try {
            $category = $this->categoryRepository->findByIdAndUser($categoryId, $userId);
            
            if (!$category) {
                return [
                    'success' => false,
                    'message' => 'Categoría no encontrada'
                ];
            }

            return [
                'success' => true,
                'category' => $category->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener categoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener todas las categorías del usuario
     */
    public function getUserCategories(int $userId, bool $activeOnly = true): array
    {
        try {
            $categories = $this->categoryRepository->findByUser($userId, $activeOnly);
            
            return [
                'success' => true,
                'categories' => array_map(fn($category) => $category->toDetailedArray(), $categories)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener categorías: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Crear nueva subcategoría
     */
    public function createSubcategory(array $data, int $userId): array
    {
        try {
            // Verificar que la categoría pertenezca al usuario
            $category = $this->categoryRepository->findByIdAndUser($data['category_id'], $userId);
            if (!$category) {
                return [
                    'success' => false,
                    'message' => 'Categoría no encontrada'
                ];
            }

            // Crear instancia de subcategoría
            $subcategory = new Subcategory();
            $subcategory->setCategoryId($data['category_id']);
            $subcategory->setName($data['name']);
            $subcategory->setDescription($data['description'] ?? null);

            // Validar datos
            $errors = $subcategory->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Verificar si el nombre ya existe en la categoría
            if ($this->subcategoryRepository->nameExistsInCategory($subcategory->getName(), $data['category_id'])) {
                return [
                    'success' => false,
                    'message' => 'Ya existe una subcategoría con ese nombre en esta categoría'
                ];
            }

            // Crear subcategoría
            $subcategory = $this->subcategoryRepository->create($subcategory);
            $subcategory->setCategory($category);

            $this->logger->info('Subcategoría creada', [
                'subcategory_id' => $subcategory->getId(),
                'category_id' => $data['category_id'],
                'user_id' => $userId,
                'name' => $subcategory->getName()
            ]);

            return [
                'success' => true,
                'message' => 'Subcategoría creada exitosamente',
                'subcategory' => $subcategory->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al crear subcategoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Actualizar subcategoría existente
     */
    public function updateSubcategory(int $subcategoryId, array $data, int $userId): array
    {
        try {
            // Buscar subcategoría
            $subcategory = $this->subcategoryRepository->findByIdAndUser($subcategoryId, $userId);
            if (!$subcategory) {
                return [
                    'success' => false,
                    'message' => 'Subcategoría no encontrada'
                ];
            }

            // Si se cambia la categoría, verificar que la nueva categoría pertenezca al usuario
            if (isset($data['category_id']) && $data['category_id'] != $subcategory->getCategoryId()) {
                $newCategory = $this->categoryRepository->findByIdAndUser($data['category_id'], $userId);
                if (!$newCategory) {
                    return [
                        'success' => false,
                        'message' => 'Nueva categoría no encontrada'
                    ];
                }
                $subcategory->setCategoryId($data['category_id']);
            }

            // Actualizar datos
            if (isset($data['name'])) {
                $subcategory->setName($data['name']);
            }
            
            if (isset($data['description'])) {
                $subcategory->setDescription($data['description']);
            }

            // Validar datos
            $errors = $subcategory->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Verificar si el nombre ya existe en la categoría (excluyendo la subcategoría actual)
            if ($this->subcategoryRepository->nameExistsInCategory(
                $subcategory->getName(), 
                $subcategory->getCategoryId(), 
                $subcategoryId
            )) {
                return [
                    'success' => false,
                    'message' => 'Ya existe una subcategoría con ese nombre en esta categoría'
                ];
            }

            // Actualizar subcategoría
            $this->subcategoryRepository->update($subcategory);

            $this->logger->info('Subcategoría actualizada', [
                'subcategory_id' => $subcategory->getId(),
                'user_id' => $userId,
                'name' => $subcategory->getName()
            ]);

            return [
                'success' => true,
                'message' => 'Subcategoría actualizada exitosamente',
                'subcategory' => $subcategory->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al actualizar subcategoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Eliminar subcategoría
     */
    public function deleteSubcategory(int $subcategoryId, int $userId): array
    {
        try {
            // Buscar subcategoría
            $subcategory = $this->subcategoryRepository->findByIdAndUser($subcategoryId, $userId);
            if (!$subcategory) {
                return [
                    'success' => false,
                    'message' => 'Subcategoría no encontrada'
                ];
            }

            // Verificar si la subcategoría tiene transacciones
            if ($this->subcategoryHasTransactions($subcategoryId)) {
                // Solo desactivar si tiene transacciones
                $subcategory->deactivate();
                $this->subcategoryRepository->update($subcategory);
                $message = 'Subcategoría desactivada (tiene transacciones asociadas)';
            } else {
                // Eliminar permanentemente si no tiene transacciones
                $this->subcategoryRepository->forceDelete($subcategoryId);
                $message = 'Subcategoría eliminada exitosamente';
            }

            $this->logger->info('Subcategoría eliminada/desactivada', [
                'subcategory_id' => $subcategoryId,
                'user_id' => $userId,
                'name' => $subcategory->getName()
            ]);

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al eliminar subcategoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener subcategorías por categoría
     */
    public function getSubcategoriesByCategory(int $categoryId, int $userId): array
    {
        try {
            // Verificar que la categoría pertenezca al usuario
            $category = $this->categoryRepository->findByIdAndUser($categoryId, $userId);
            if (!$category) {
                return [
                    'success' => false,
                    'message' => 'Categoría no encontrada'
                ];
            }

            $subcategories = $this->subcategoryRepository->findByCategory($categoryId, true);
            
            return [
                'success' => true,
                'subcategories' => array_map(fn($sub) => $sub->toDetailedArray(), $subcategories)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener subcategorías: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener estadísticas de categorías
     */
    public function getCategoryStats(int $userId): array
    {
        try {
            $stats = $this->categoryRepository->getStats($userId);
            
            return [
                'success' => true,
                'stats' => $stats
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener estadísticas: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Activar/Desactivar categoría
     */
    public function toggleCategoryStatus(int $categoryId, int $userId): array
    {
        try {
            $category = $this->categoryRepository->findByIdAndUser($categoryId, $userId);
            
            if (!$category) {
                return [
                    'success' => false,
                    'message' => 'Categoría no encontrada'
                ];
            }

            // Cambiar estado
            if ($category->isActive()) {
                $category->deactivate();
                $message = 'Categoría desactivada';
            } else {
                $category->activate();
                $message = 'Categoría activada';
            }

            $this->categoryRepository->update($category);

            $this->logger->info('Estado de categoría cambiado', [
                'category_id' => $categoryId,
                'user_id' => $userId,
                'new_status' => $category->isActive() ? 'active' : 'inactive'
            ]);

            return [
                'success' => true,
                'message' => $message,
                'category' => $category->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al cambiar estado de categoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Verificar si una categoría tiene transacciones
     */
    private function hasTransactions(int $categoryId): bool
    {
        // Esta función se implementará cuando tengamos el repositorio de transacciones
        // Por ahora retornamos false
        return false;
    }

    /**
     * Verificar si una subcategoría tiene transacciones
     */
    private function subcategoryHasTransactions(int $subcategoryId): bool
    {
        // Esta función se implementará cuando tengamos el repositorio de transacciones
        // Por ahora retornamos false
        return false;
    }

    /**
     * Obtener colores disponibles
     */
    public function getAvailableColors(): array
    {
        return Category::getAvailableColors();
    }

    /**
     * Obtener iconos disponibles
     */
    public function getAvailableIcons(): array
    {
        return Category::getAvailableIcons();
    }
}
