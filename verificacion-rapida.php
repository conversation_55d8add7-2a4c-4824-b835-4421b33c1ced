<?php
/**
 * Verificación Rápida del Sistema de Tarjetas de Crédito
 * Crea datos de prueba y verifica funcionalidades básicas
 */

echo "🚀 VERIFICACIÓN RÁPIDA - TARJETAS DE CRÉDITO\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Configuración
$config = [
    'host' => 'localhost',
    'dbname' => 'control_gastos',
    'username' => 'root',
    'password' => '',
];

try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4";
    $db = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✅ Conexión establecida\n\n";
    
} catch (PDOException $e) {
    echo "❌ Error de conexión: " . $e->getMessage() . "\n";
    exit(1);
}

// Crear datos de prueba completos
echo "📝 CREANDO DATOS DE PRUEBA\n";
echo "-" . str_repeat("-", 40) . "\n";

try {
    // 1. Crear tarjeta principal
    $sql = "INSERT INTO credit_cards (
        user_id, card_name, bank_name, card_type, card_number_last4, 
        credit_limit, expiry_date, cut_off_day, payment_due_days, 
        cvv, description, status
    ) VALUES (
        1, 'Visa Principal', 'Banco Nacional', 'visa', '1234',
        50000.00, '2027-12-31', 15, 20,
        '123', 'Tarjeta principal para gastos cotidianos', 'active'
    )";
    
    $db->exec($sql);
    $card1Id = $db->lastInsertId();
    echo "✅ Tarjeta 1 creada: Visa Principal (ID: {$card1Id})\n";
    
    // 2. Crear segunda tarjeta
    $sql = "INSERT INTO credit_cards (
        user_id, card_name, bank_name, card_type, card_number_last4,
        credit_limit, expiry_date, cut_off_day, payment_due_days,
        description, status
    ) VALUES (
        1, 'Mastercard Compras', 'Banco Internacional', 'mastercard', '5678',
        30000.00, '2026-08-31', 5, 25,
        'Tarjeta para compras online y viajes', 'active'
    )";
    
    $db->exec($sql);
    $card2Id = $db->lastInsertId();
    echo "✅ Tarjeta 2 creada: Mastercard Compras (ID: {$card2Id})\n";
    
    // 3. Crear tarjeta vencida para testing
    $sql = "INSERT INTO credit_cards (
        user_id, card_name, bank_name, card_type,
        credit_limit, expiry_date, cut_off_day, payment_due_days,
        description, status
    ) VALUES (
        1, 'Tarjeta Vencida', 'Banco Test', 'visa',
        10000.00, '2023-12-31', 10, 15,
        'Tarjeta para testing de vencimiento', 'expired'
    )";
    
    $db->exec($sql);
    $card3Id = $db->lastInsertId();
    echo "✅ Tarjeta 3 creada: Tarjeta Vencida (ID: {$card3Id})\n";
    
    // 4. Crear transacciones de ejemplo
    $transactions = [
        [$card1Id, 150.00, 'Compra en supermercado', 'Walmart', 'purchase'],
        [$card1Id, 80.00, 'Gasolina', 'Estación Shell', 'purchase'],
        [$card1Id, 1200.00, 'Compra electrodoméstico', 'Tienda Tech', 'purchase'],
        [$card2Id, 250.00, 'Cena restaurante', 'Restaurante Gourmet', 'purchase'],
        [$card2Id, 45.00, 'Suscripción streaming', 'Netflix', 'purchase'],
    ];
    
    foreach ($transactions as $i => $trans) {
        $sql = "INSERT INTO credit_card_transactions (
            credit_card_id, user_id, transaction_type, amount, description,
            merchant, transaction_date, posting_date
        ) VALUES (?, 1, ?, ?, ?, ?, CURDATE(), CURDATE())";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$trans[0], $trans[4], $trans[1], $trans[2], $trans[3]]);
        echo "✅ Transacción " . ($i + 1) . ": {$trans[2]} - $" . number_format($trans[1], 2) . "\n";
    }
    
    // 5. Crear pagos de ejemplo
    $payments = [
        [$card1Id, 500.00, 'partial', 'bank_transfer'],
        [$card2Id, 150.00, 'partial', 'online'],
    ];
    
    foreach ($payments as $i => $payment) {
        $sql = "INSERT INTO credit_card_payments (
            credit_card_id, user_id, payment_type, amount, payment_date, payment_method
        ) VALUES (?, 1, ?, ?, CURDATE(), ?)";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$payment[0], $payment[2], $payment[1], $payment[3]]);
        echo "✅ Pago " . ($i + 1) . ": $" . number_format($payment[1], 2) . " ({$payment[2]})\n";
    }
    
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ Error creando datos: " . $e->getMessage() . "\n";
    exit(1);
}

// Verificar cálculos
echo "🧮 VERIFICANDO CÁLCULOS FINANCIEROS\n";
echo "-" . str_repeat("-", 40) . "\n";

try {
    // Calcular saldos
    $cards = [$card1Id, $card2Id, $card3Id];
    $cardNames = ['Visa Principal', 'Mastercard Compras', 'Tarjeta Vencida'];
    
    foreach ($cards as $i => $cardId) {
        // Obtener información de la tarjeta
        $stmt = $db->prepare("SELECT * FROM credit_cards WHERE id = ?");
        $stmt->execute([$cardId]);
        $card = $stmt->fetch();
        
        // Calcular saldo
        $stmt = $db->prepare("
            SELECT 
                COALESCE(SUM(amount), 0) as total_charges
            FROM credit_card_transactions 
            WHERE credit_card_id = ?
        ");
        $stmt->execute([$cardId]);
        $charges = $stmt->fetch()['total_charges'];
        
        $stmt = $db->prepare("
            SELECT 
                COALESCE(SUM(amount), 0) as total_payments
            FROM credit_card_payments 
            WHERE credit_card_id = ?
        ");
        $stmt->execute([$cardId]);
        $payments = $stmt->fetch()['total_payments'];
        
        $balance = $charges - $payments;
        $available = $card['credit_limit'] - $balance;
        $utilization = $card['credit_limit'] > 0 ? ($balance / $card['credit_limit']) * 100 : 0;
        
        echo "💳 {$cardNames[$i]}:\n";
        echo "   Cupo: $" . number_format($card['credit_limit'], 2) . "\n";
        echo "   Cargos: $" . number_format($charges, 2) . "\n";
        echo "   Pagos: $" . number_format($payments, 2) . "\n";
        echo "   Saldo: $" . number_format($balance, 2) . "\n";
        echo "   Disponible: $" . number_format($available, 2) . "\n";
        echo "   Utilización: " . number_format($utilization, 1) . "%\n";
        echo "   Estado: {$card['status']}\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error en cálculos: " . $e->getMessage() . "\n";
}

// Mostrar URLs para testing
echo "🌐 URLS PARA TESTING MANUAL\n";
echo "-" . str_repeat("-", 40) . "\n";

$baseUrl = "http://localhost/controlGastos/public/";

echo "📊 Dashboard Principal:\n";
echo "   {$baseUrl}\n\n";

echo "💳 Dashboard Tarjetas:\n";
echo "   {$baseUrl}?route=credit-cards\n\n";

echo "➕ Nueva Tarjeta:\n";
echo "   {$baseUrl}?route=credit-cards/create\n\n";

echo "👁️ Ver Detalles:\n";
foreach ($cards as $i => $cardId) {
    echo "   {$cardNames[$i]}: {$baseUrl}?route=credit-cards/show&id={$cardId}\n";
}
echo "\n";

echo "✏️ Editar:\n";
foreach ($cards as $i => $cardId) {
    echo "   {$cardNames[$i]}: {$baseUrl}?route=credit-cards/edit&id={$cardId}\n";
}
echo "\n";

echo "📈 Reportes:\n";
foreach ($cards as $i => $cardId) {
    echo "   {$cardNames[$i]}: {$baseUrl}?route=credit-cards/reports&id={$cardId}\n";
}
echo "\n";

// Instrucciones finales
echo "🎯 INSTRUCCIONES DE TESTING\n";
echo "-" . str_repeat("-", 40) . "\n";

echo "1. 🌐 Abre tu navegador y ve al dashboard principal\n";
echo "2. 🔐 Asegúrate de estar autenticado en el sistema\n";
echo "3. 💳 Haz clic en 'Tarjetas de Crédito' para ver las tarjetas creadas\n";
echo "4. 👁️ Haz clic en 'Ver' en cualquier tarjeta para ver detalles\n";
echo "5. ➕ Prueba agregar nuevas transacciones y pagos\n";
echo "6. 🔄 Verifica que los saldos se actualicen automáticamente\n";
echo "7. ⚙️ Prueba cambiar estados (bloquear/reactivar)\n";
echo "8. 📊 Revisa los reportes con gráficos\n\n";

echo "🧹 LIMPIEZA (Opcional)\n";
echo "-" . str_repeat("-", 40) . "\n";
echo "Para eliminar los datos de prueba, ejecuta:\n";
echo "DELETE FROM credit_card_payments WHERE credit_card_id IN ({$card1Id}, {$card2Id}, {$card3Id});\n";
echo "DELETE FROM credit_card_transactions WHERE credit_card_id IN ({$card1Id}, {$card2Id}, {$card3Id});\n";
echo "DELETE FROM credit_cards WHERE id IN ({$card1Id}, {$card2Id}, {$card3Id});\n\n";

echo "🎉 VERIFICACIÓN COMPLETADA - SISTEMA LISTO PARA TESTING\n";
