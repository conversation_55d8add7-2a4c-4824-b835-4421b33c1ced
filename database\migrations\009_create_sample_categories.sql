-- Migración 009: Crear categorías de ejemplo
-- Fecha: 2024-12-30
-- Descripción: Crear categorías básicas para el sistema

-- Insertar categorías de gastos
INSERT INTO categories (user_id, name, description, color, icon) VALUES
(1, 'Alimentación', 'Gastos en supermercado, restaurantes y comida', '#dc3545', 'fas fa-utensils'),
(1, 'Transporte', 'Gasolina, transporte público, mantenimiento vehículo', '#ffc107', 'fas fa-car'),
(1, 'Servicios', 'Electricidad, agua, internet, teléfono', '#17a2b8', 'fas fa-home'),
(1, 'Entretenimiento', 'Cine, streaming, salidas, hobbies', '#6f42c1', 'fas fa-gamepad'),
(1, 'Salud', 'Medicamentos, consultas médicas, seguros', '#28a745', 'fas fa-heartbeat'),
(1, 'Educación', 'Cursos, libros, capacitaciones', '#fd7e14', 'fas fa-graduation-cap'),
(1, 'Ropa', 'Vestimenta y accesorios', '#e83e8c', 'fas fa-tshirt'),
(1, 'Hogar', 'Muebles, decoración, mantenimiento', '#6c757d', 'fas fa-couch');

-- Insertar categorías de ingresos
INSERT INTO categories (user_id, name, description, color, icon) VALUES
(1, 'Salario', 'Ingresos por trabajo', '#28a745', 'fas fa-money-bill-wave'),
(1, 'Freelance', 'Ingresos por trabajos independientes', '#20c997', 'fas fa-laptop'),
(1, 'Inversiones', 'Dividendos, intereses, ganancias', '#6610f2', 'fas fa-chart-line'),
(1, 'Ventas', 'Ingresos por ventas de productos', '#fd7e14', 'fas fa-shopping-bag'),
(1, 'Otros Ingresos', 'Regalos, bonos, otros', '#6c757d', 'fas fa-plus-circle');
