<?php

declare(strict_types=1);

namespace ControlGastos\Repositories;

use ControlGastos\Core\Database;
use ControlGastos\Models\Subcategory;
use ControlGastos\Models\Category;
use Exception;

/**
 * Repositorio de subcategorías
 * Maneja todas las operaciones de base de datos relacionadas con subcategorías
 */
class SubcategoryRepository
{
    private Database $database;

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Crear nueva subcategoría
     */
    public function create(Subcategory $subcategory): Subcategory
    {
        $query = "
            INSERT INTO subcategories (
                category_id, name, description, is_active, 
                created_at, updated_at
            ) VALUES (
                :category_id, :name, :description, :is_active,
                :created_at, :updated_at
            )
        ";

        $params = [
            'category_id' => $subcategory->getCategoryId(),
            'name' => $subcategory->getName(),
            'description' => $subcategory->getDescription(),
            'is_active' => $subcategory->isActive() ? 1 : 0,
            'created_at' => $subcategory->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $subcategory->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        $id = $this->database->insert($query, $params);
        $subcategory->setId($id);

        return $subcategory;
    }

    /**
     * Buscar subcategoría por ID
     */
    public function findById(int $id): ?Subcategory
    {
        $query = "
            SELECT s.*, c.user_id
            FROM subcategories s
            INNER JOIN categories c ON s.category_id = c.id
            WHERE s.id = :id
        ";
        
        $result = $this->database->selectOne($query, ['id' => $id]);

        if (!$result) {
            return null;
        }

        return new Subcategory($result);
    }

    /**
     * Buscar subcategoría por ID y verificar que pertenezca al usuario
     */
    public function findByIdAndUser(int $id, int $userId): ?Subcategory
    {
        $query = "
            SELECT s.*
            FROM subcategories s
            INNER JOIN categories c ON s.category_id = c.id
            WHERE s.id = :id AND c.user_id = :user_id
        ";
        
        $result = $this->database->selectOne($query, ['id' => $id, 'user_id' => $userId]);

        if (!$result) {
            return null;
        }

        return new Subcategory($result);
    }

    /**
     * Obtener subcategorías por categoría
     */
    public function findByCategory(int $categoryId, bool $activeOnly = false): array
    {
        $query = "SELECT * FROM subcategories WHERE category_id = :category_id";
        $params = ['category_id' => $categoryId];

        if ($activeOnly) {
            $query .= " AND is_active = 1";
        }

        $query .= " ORDER BY name ASC";

        $results = $this->database->select($query, $params);
        return array_map(fn($row) => new Subcategory($row), $results);
    }

    /**
     * Obtener subcategorías por usuario
     */
    public function findByUser(int $userId, bool $activeOnly = false): array
    {
        $query = "
            SELECT s.*, c.name as category_name, c.color as category_color, c.icon as category_icon
            FROM subcategories s
            INNER JOIN categories c ON s.category_id = c.id
            WHERE c.user_id = :user_id
        ";
        
        $params = ['user_id' => $userId];

        if ($activeOnly) {
            $query .= " AND s.is_active = 1 AND c.is_active = 1";
        }

        $query .= " ORDER BY c.name ASC, s.name ASC";

        $results = $this->database->select($query, $params);
        
        return array_map(function($row) {
            $subcategory = new Subcategory($row);
            
            // Crear objeto Category básico para la relación
            $category = new Category([
                'id' => $row['category_id'],
                'name' => $row['category_name'],
                'color' => $row['category_color'],
                'icon' => $row['category_icon']
            ]);
            
            $subcategory->setCategory($category);
            return $subcategory;
        }, $results);
    }

    /**
     * Actualizar subcategoría
     */
    public function update(Subcategory $subcategory): bool
    {
        $query = "
            UPDATE subcategories SET
                category_id = :category_id,
                name = :name,
                description = :description,
                is_active = :is_active,
                updated_at = :updated_at
            WHERE id = :id
        ";

        $params = [
            'id' => $subcategory->getId(),
            'category_id' => $subcategory->getCategoryId(),
            'name' => $subcategory->getName(),
            'description' => $subcategory->getDescription(),
            'is_active' => $subcategory->isActive() ? 1 : 0,
            'updated_at' => $subcategory->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        return $this->database->update($query, $params) > 0;
    }

    /**
     * Eliminar subcategoría (soft delete)
     */
    public function delete(int $id): bool
    {
        $query = "
            UPDATE subcategories SET 
                is_active = 0,
                updated_at = NOW()
            WHERE id = :id
        ";

        return $this->database->update($query, ['id' => $id]) > 0;
    }

    /**
     * Eliminar subcategoría permanentemente
     */
    public function forceDelete(int $id): bool
    {
        $query = "DELETE FROM subcategories WHERE id = :id";
        return $this->database->delete($query, ['id' => $id]) > 0;
    }

    /**
     * Verificar si el nombre de subcategoría ya existe en la categoría
     */
    public function nameExistsInCategory(string $name, int $categoryId, ?int $excludeId = null): bool
    {
        $query = "SELECT COUNT(*) as count FROM subcategories WHERE name = :name AND category_id = :category_id";
        $params = ['name' => $name, 'category_id' => $categoryId];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $result = $this->database->selectOne($query, $params);
        return $result['count'] > 0;
    }

    /**
     * Obtener subcategorías más utilizadas
     */
    public function getMostUsed(int $userId, int $limit = 10): array
    {
        $query = "
            SELECT 
                s.*,
                c.name as category_name,
                c.color as category_color,
                c.icon as category_icon,
                COUNT(t.id) as usage_count
            FROM subcategories s
            INNER JOIN categories c ON s.category_id = c.id
            LEFT JOIN transactions t ON s.id = t.subcategory_id
            WHERE c.user_id = :user_id AND s.is_active = 1 AND c.is_active = 1
            GROUP BY s.id
            ORDER BY usage_count DESC, s.name ASC
            LIMIT :limit
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'limit' => $limit
        ]);

        return array_map(function($row) {
            $subcategory = new Subcategory($row);
            
            $category = new Category([
                'id' => $row['category_id'],
                'name' => $row['category_name'],
                'color' => $row['category_color'],
                'icon' => $row['category_icon']
            ]);
            
            $subcategory->setCategory($category);
            return $subcategory;
        }, $results);
    }

    /**
     * Buscar subcategorías por nombre
     */
    public function searchByName(int $userId, string $search): array
    {
        $query = "
            SELECT s.*, c.name as category_name, c.color as category_color, c.icon as category_icon
            FROM subcategories s
            INNER JOIN categories c ON s.category_id = c.id
            WHERE c.user_id = :user_id 
            AND s.name LIKE :search 
            AND s.is_active = 1 AND c.is_active = 1
            ORDER BY s.name ASC
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'search' => '%' . $search . '%'
        ]);

        return array_map(function($row) {
            $subcategory = new Subcategory($row);
            
            $category = new Category([
                'id' => $row['category_id'],
                'name' => $row['category_name'],
                'color' => $row['category_color'],
                'icon' => $row['category_icon']
            ]);
            
            $subcategory->setCategory($category);
            return $subcategory;
        }, $results);
    }

    /**
     * Obtener estadísticas de subcategorías por categoría
     */
    public function getStatsByCategory(int $categoryId): array
    {
        $query = "
            SELECT 
                COUNT(*) as total_subcategories,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_subcategories
            FROM subcategories
            WHERE category_id = :category_id
        ";

        return $this->database->selectOne($query, ['category_id' => $categoryId]) ?: [];
    }

    /**
     * Mover subcategorías a otra categoría
     */
    public function moveToCategory(array $subcategoryIds, int $newCategoryId): bool
    {
        if (empty($subcategoryIds)) {
            return true;
        }

        $placeholders = str_repeat('?,', count($subcategoryIds) - 1) . '?';
        $query = "
            UPDATE subcategories 
            SET category_id = ?, updated_at = NOW()
            WHERE id IN ({$placeholders})
        ";

        $params = array_merge([$newCategoryId], $subcategoryIds);
        return $this->database->update($query, $params) > 0;
    }

    /**
     * Eliminar todas las subcategorías de una categoría
     */
    public function deleteByCategory(int $categoryId): bool
    {
        $query = "
            UPDATE subcategories 
            SET is_active = 0, updated_at = NOW()
            WHERE category_id = :category_id
        ";

        return $this->database->update($query, ['category_id' => $categoryId]) > 0;
    }

    /**
     * Obtener subcategorías con paginación
     */
    public function getPaginated(int $userId, int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ['c.user_id = :user_id'];
        $params = ['user_id' => $userId];

        // Filtros
        if (!empty($filters['category_id'])) {
            $whereConditions[] = "s.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        if (isset($filters['is_active'])) {
            $whereConditions[] = "s.is_active = :is_active";
            $params['is_active'] = $filters['is_active'] ? 1 : 0;
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = "(s.name LIKE :search OR s.description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Consulta principal
        $query = "
            SELECT s.*, c.name as category_name, c.color as category_color, c.icon as category_icon
            FROM subcategories s
            INNER JOIN categories c ON s.category_id = c.id
            {$whereClause}
            ORDER BY c.name ASC, s.name ASC 
            LIMIT :limit OFFSET :offset
        ";

        $params['limit'] = $perPage;
        $params['offset'] = $offset;

        $results = $this->database->select($query, $params);
        $subcategories = array_map(function($row) {
            $subcategory = new Subcategory($row);
            
            $category = new Category([
                'id' => $row['category_id'],
                'name' => $row['category_name'],
                'color' => $row['category_color'],
                'icon' => $row['category_icon']
            ]);
            
            $subcategory->setCategory($category);
            return $subcategory;
        }, $results);

        // Contar total
        $countQuery = "
            SELECT COUNT(*) as total 
            FROM subcategories s
            INNER JOIN categories c ON s.category_id = c.id
            {$whereClause}
        ";
        unset($params['limit'], $params['offset']);
        $totalResult = $this->database->selectOne($countQuery, $params);
        $total = $totalResult['total'];

        return [
            'data' => $subcategories,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
}
