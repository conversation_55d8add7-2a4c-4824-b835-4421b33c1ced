<?php

declare(strict_types=1);

// Bootstrap de la aplicación
// Configuración inicial y carga de dependencias

// Configurar zona horaria
date_default_timezone_set('America/Mexico_City');

// Detectar entorno
$environment = $_ENV['APP_ENV'] ?? 'development';
$isProduction = $environment === 'production';

// Configurar manejo de errores según entorno
if ($isProduction) {
    error_reporting(E_ERROR | E_WARNING | E_PARSE);
    ini_set('display_errors', '0');
    ini_set('log_errors', '1');
    ini_set('error_log', __DIR__ . '/../logs/php-errors.log');
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
}

// Cargar variables de entorno
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue; // Comentarios
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Autoloader simple
spl_autoload_register(function ($class) {
    $prefix = 'ControlGastos\\';
    $base_dir = __DIR__ . '/';

    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }

    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';

    if (file_exists($file)) {
        require $file;
    }
});

// Cargar helpers
require_once __DIR__ . '/helpers/assets.php';

// Configuración
$config = [];

// Configuración de base de datos (formato esperado por Database class)
$config['database'] = [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => (int) ($_ENV['DB_PORT'] ?? 3306),
            'database' => $_ENV['DB_NAME'] ?? 'control_gastos',
            'username' => $_ENV['DB_USER'] ?? 'root',
            'password' => $_ENV['DB_PASS'] ?? '',
            'charset' => 'utf8mb4',
            'options' => [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
            ]
        ]
    ]
];

// Configuración de aplicación
$config['app'] = [
    'name' => $_ENV['APP_NAME'] ?? 'Control de Gastos',
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',
    'env' => $environment,
    'debug' => $_ENV['APP_DEBUG'] ?? !$isProduction,
    'key' => $_ENV['APP_KEY'] ?? 'default-key-change-in-production',
    'timezone' => $_ENV['APP_TIMEZONE'] ?? 'America/Mexico_City',
];

// Configuración de email
$config['mail'] = [
    'driver' => $_ENV['MAIL_DRIVER'] ?? 'smtp',
    'host' => $_ENV['SMTP_HOST'] ?? 'localhost',
    'port' => (int) ($_ENV['SMTP_PORT'] ?? 587),
    'username' => $_ENV['SMTP_USERNAME'] ?? '',
    'password' => $_ENV['SMTP_PASSWORD'] ?? '',
    'encryption' => $_ENV['SMTP_ENCRYPTION'] ?? 'tls',
    'from' => [
        'address' => $_ENV['MAIL_FROM_ADDRESS'] ?? 'noreply@localhost',
        'name' => $_ENV['MAIL_FROM_NAME'] ?? 'Control de Gastos'
    ]
];

// Configuración de cache
$config['cache'] = [
    'driver' => $_ENV['CACHE_DRIVER'] ?? 'file',
    'path' => __DIR__ . '/../storage/cache',
    'redis' => [
        'host' => $_ENV['REDIS_HOST'] ?? '127.0.0.1',
        'port' => (int) ($_ENV['REDIS_PORT'] ?? 6379),
        'password' => $_ENV['REDIS_PASSWORD'] ?? null,
    ]
];

// Configuración de logs
$config['logging'] = [
    'level' => $_ENV['LOG_LEVEL'] ?? ($isProduction ? 'error' : 'debug'),
    'path' => $_ENV['LOG_FILE'] ?? __DIR__ . '/../logs/app.log',
    'max_files' => 30,
];

// Inicializar contenedor de dependencias
use ControlGastos\Core\Container;
use ControlGastos\Core\SimpleDatabase;
use ControlGastos\Core\Logger;
use ControlGastos\Core\Session;

$container = new Container();

// Registrar configuración
$container->set('config', function() use ($config) {
    return $config;
});

// Registrar servicios core
$container->set('database', function() use ($config) {
    return new \ControlGastos\Core\SimpleDatabase($config);
});

$container->set('logger', function() use ($config) {
    // Crear configuración compatible con Logger
    $loggerConfig = [
        'files' => [
            'logs_path' => __DIR__ . '/../logs'
        ],
        'level' => $config['logging']['level'] ?? 'debug',
        'max_files' => $config['logging']['max_files'] ?? 30
    ];
    return new Logger($loggerConfig);
});

$container->set('session', function() use ($config) {
    // Configuración para Session
    $sessionConfig = [
        'name' => 'control_gastos_session',
        'lifetime' => 7200, // 2 horas
        'path' => '/',
        'domain' => '',
        'secure' => false, // true en producción con HTTPS
        'httponly' => true,
        'samesite' => 'Lax'
    ];
    return new Session($sessionConfig);
});

// Registrar solo los servicios básicos que existen
// Los demás se pueden agregar cuando se necesiten

// Función auxiliar para verificar si una clase existe antes de registrarla
$registerIfExists = function($serviceName, $className, $dependencies = []) use ($container) {
    if (class_exists($className)) {
        $container->set($serviceName, function() use ($container, $className, $dependencies) {
            $args = [];
            foreach ($dependencies as $dep) {
                $args[] = $container->get($dep);
            }
            return new $className(...$args);
        });
    }
};

// Registrar servicios si existen
$registerIfExists('userService', '\\ControlGastos\\Services\\UserService', ['database', 'logger']);
$registerIfExists('accountService', '\\ControlGastos\\Services\\AccountService', ['database', 'logger']);
$registerIfExists('transactionService', '\\ControlGastos\\Services\\TransactionService', ['database', 'logger']);
$registerIfExists('categoryService', '\\ControlGastos\\Services\\CategoryService', ['database', 'logger']);
$registerIfExists('reminderService', '\\ControlGastos\\Services\\ReminderService', ['database', 'logger']);
$registerIfExists('reportService', '\\ControlGastos\\Services\\ReportService', ['database', 'logger']);
$registerIfExists('advancedReportService', '\\ControlGastos\\Services\\AdvancedReportService', ['database', 'logger']);
$registerIfExists('backupService', '\\ControlGastos\\Services\\BackupService', ['database', 'logger']);
$registerIfExists('securityService', '\\ControlGastos\\Services\\SecurityService', ['database', 'logger']);

// EmailService tiene parámetros diferentes
if (class_exists('\\ControlGastos\\Services\\EmailService')) {
    $container->set('emailService', function() use ($container, $config) {
        return new \ControlGastos\Services\EmailService(
            $container->get('logger'),
            $container->get('database'),
            $config['mail']
        );
    });
}

// Inicializar sesión
$container->get('session')->start();

// Configurar manejo global de errores para producción
if ($isProduction) {
    set_error_handler(function($severity, $message, $file, $line) use ($container) {
        $logger = $container->get('logger');
        $logger->error("PHP Error: $message in $file:$line");
    });

    set_exception_handler(function($exception) use ($container) {
        $logger = $container->get('logger');
        $logger->error("Uncaught Exception: " . $exception->getMessage(), [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        // Mostrar página de error genérica
        http_response_code(500);
        if (file_exists(__DIR__ . '/../templates/errors/500.php')) {
            include __DIR__ . '/../templates/errors/500.php';
        } else {
            echo "Error interno del servidor. Por favor, contacte al administrador.";
        }
        exit;
    });
}
