<?php
/**
 * Test Simple de Vista
 * Simula lo que hace el controlador sin dependencias complejas
 */

echo "🔍 TEST SIMPLE DE VISTA\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// Verificar archivos
echo "📁 Verificando archivos de vista:\n";
$viewPath = __DIR__ . "/templates/pages/credit_cards/index.php";
echo "Ruta: {$viewPath}\n";

if (file_exists($viewPath)) {
    echo "✅ Archivo existe (" . filesize($viewPath) . " bytes)\n";
    
    // Simular datos que pasaría el controlador
    $cards = [
        [
            'card' => [
                'id' => 2,
                'card_name' => 'Visa Principal',
                'bank_name' => 'Banco Nacional',
                'status' => 'active',
                'credit_limit' => 50000,
                'card_number_last4' => '1234'
            ],
            'current_balance' => 930.00,
            'available_credit' => 49070.00,
            'credit_utilization' => 1.9,
            'next_payment_date' => '2025-07-05',
            'minimum_payment' => 46.50
        ]
    ];
    
    $recent_transactions = [];
    $recent_payments = [];
    $title = 'Mis Tarjetas de Crédito';
    
    echo "\n🎭 Simulando renderizado de vista:\n";
    
    // Capturar output
    ob_start();
    
    try {
        // Simular el include que hace el controlador
        include $viewPath;
        $output = ob_get_clean();
        
        echo "✅ Vista renderizada exitosamente\n";
        echo "📄 Tamaño del output: " . strlen($output) . " caracteres\n";
        
        // Verificar contenido específico
        $checks = [
            'Mis Tarjetas de Crédito' => 'Título principal',
            'Nueva Tarjeta' => 'Botón crear',
            'Visa Principal' => 'Tarjeta de prueba',
            'card-header' => 'Estructura de tarjeta',
            'btn btn-primary' => 'Botones Bootstrap'
        ];
        
        echo "\n🔍 Verificando contenido:\n";
        foreach ($checks as $needle => $description) {
            if (strpos($output, $needle) !== false) {
                echo "✅ {$description}: ENCONTRADO\n";
            } else {
                echo "❌ {$description}: NO ENCONTRADO\n";
            }
        }
        
        // Guardar para inspección
        file_put_contents('test_view_output.html', $output);
        echo "\n💾 Output guardado en test_view_output.html\n";
        
        // Mostrar primeras líneas
        echo "\n📝 Primeras líneas del output:\n";
        $lines = explode("\n", $output);
        for ($i = 0; $i < min(10, count($lines)); $i++) {
            echo "   " . ($i + 1) . ": " . trim($lines[$i]) . "\n";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ Error renderizando vista: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ Archivo de vista no existe\n";
}

echo "\n🌐 Probando acceso directo por HTTP:\n";

$url = 'http://localhost/controlGastos/public/?route=credit-cards';
echo "URL: {$url}\n";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET'
    ]
]);

$content = @file_get_contents($url, false, $context);

if ($content !== false) {
    echo "✅ Respuesta HTTP recibida (" . strlen($content) . " caracteres)\n";
    
    // Verificar si es HTML válido
    if (strpos($content, '<html') !== false) {
        echo "✅ Contiene HTML válido\n";
    } else {
        echo "⚠️ No parece ser HTML válido\n";
    }
    
    // Verificar errores PHP
    if (strpos($content, 'Fatal error') !== false || strpos($content, 'Parse error') !== false) {
        echo "❌ Contiene errores PHP\n";
        
        // Extraer el error
        preg_match('/Fatal error:.*?in.*?on line \d+/', $content, $matches);
        if ($matches) {
            echo "Error: " . $matches[0] . "\n";
        }
    } else {
        echo "✅ Sin errores PHP visibles\n";
    }
    
    // Verificar contenido específico
    $webChecks = [
        'Bienvenido' => 'Mensaje de bienvenida (dashboard)',
        'Mis Tarjetas de Crédito' => 'Título de tarjetas',
        'Control de Gastos' => 'Título de la app'
    ];
    
    echo "\n🔍 Verificando contenido web:\n";
    foreach ($webChecks as $needle => $description) {
        if (strpos($content, $needle) !== false) {
            echo "✅ {$description}: ENCONTRADO\n";
        } else {
            echo "❌ {$description}: NO ENCONTRADO\n";
        }
    }
    
    // Guardar respuesta HTTP
    file_put_contents('test_http_response.html', $content);
    echo "\n💾 Respuesta HTTP guardada en test_http_response.html\n";
    
} else {
    echo "❌ No se pudo obtener respuesta HTTP\n";
}

echo "\n🎯 CONCLUSIONES:\n";
echo "-" . str_repeat("-", 30) . "\n";

if (file_exists('test_view_output.html') && file_exists('test_http_response.html')) {
    echo "1. Compara test_view_output.html (vista directa) con test_http_response.html (HTTP)\n";
    echo "2. Si son diferentes, hay un problema en el enrutamiento\n";
    echo "3. Si son iguales pero no muestran tarjetas, hay problema en los datos\n";
} else {
    echo "1. Hay problemas básicos en el renderizado de vistas\n";
    echo "2. Revisa los errores mostrados arriba\n";
}

echo "\n🎉 TEST COMPLETADO\n";
