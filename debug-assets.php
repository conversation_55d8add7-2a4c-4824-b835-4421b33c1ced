<?php
require_once 'src/helpers/assets.php';

$debugInfo = debugPaths();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Assets - Control de Gastos</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .debug-item { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .debug-item strong { color: #007bff; }
        .test-link { display: inline-block; margin: 5px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .test-link:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug de Assets - Control de Gastos</h1>
        
        <h2>📍 Información de Rutas</h2>
        <?php foreach ($debugInfo as $key => $value): ?>
            <div class="debug-item">
                <strong><?= htmlspecialchars($key) ?>:</strong> 
                <code><?= htmlspecialchars($value) ?></code>
            </div>
        <?php endforeach; ?>
        
        <h2>🧪 Pruebas de Assets</h2>
        
        <h3>CSS Files:</h3>
        <?php
        $cssFiles = ['bootstrap.min.css', 'fontawesome.min.css', 'custom.css'];
        foreach ($cssFiles as $file) {
            $url = css($file);
            $exists = @file_get_contents($url, false, stream_context_create(['http' => ['timeout' => 5]])) !== false;
            echo '<div class="debug-item">';
            echo '<strong>' . $file . ':</strong> ';
            echo '<a href="' . $url . '" target="_blank">' . $url . '</a> ';
            echo '<span class="' . ($exists ? 'success' : 'error') . '">';
            echo $exists ? '✅ Accesible' : '❌ No accesible';
            echo '</span>';
            echo '</div>';
        }
        ?>
        
        <h3>JS Files:</h3>
        <?php
        $jsFiles = ['bootstrap.bundle.min.js'];
        foreach ($jsFiles as $file) {
            $url = js($file);
            $exists = @file_get_contents($url, false, stream_context_create(['http' => ['timeout' => 5]])) !== false;
            echo '<div class="debug-item">';
            echo '<strong>' . $file . ':</strong> ';
            echo '<a href="' . $url . '" target="_blank">' . $url . '</a> ';
            echo '<span class="' . ($exists ? 'success' : 'error') . '">';
            echo $exists ? '✅ Accesible' : '❌ No accesible';
            echo '</span>';
            echo '</div>';
        }
        ?>
        
        <h2>🔗 Enlaces de Prueba</h2>
        <div>
            <a href="test-styles.php" class="test-link">🎨 Prueba de Estilos</a>
            <a href="public/" class="test-link">🏠 Aplicación Principal</a>
            <a href="public/?route=auth/login" class="test-link">🔐 Login</a>
            <a href="create-admin-local.php" class="test-link">👤 Crear Admin</a>
        </div>
        
        <h2>📋 Verificación de Archivos</h2>
        <?php
        $filesToCheck = [
            'public/assets/css/bootstrap.min.css',
            'public/assets/css/fontawesome.min.css', 
            'public/assets/css/custom.css',
            'public/assets/js/bootstrap.bundle.min.js'
        ];
        
        foreach ($filesToCheck as $file) {
            $exists = file_exists($file);
            $size = $exists ? filesize($file) : 0;
            echo '<div class="debug-item">';
            echo '<strong>' . $file . ':</strong> ';
            echo '<span class="' . ($exists ? 'success' : 'error') . '">';
            echo $exists ? '✅ Existe (' . number_format($size) . ' bytes)' : '❌ No existe';
            echo '</span>';
            echo '</div>';
        }
        ?>
        
        <h2>🌐 Información del Servidor</h2>
        <div class="debug-item">
            <strong>Document Root:</strong> <code><?= $_SERVER['DOCUMENT_ROOT'] ?? 'N/A' ?></code>
        </div>
        <div class="debug-item">
            <strong>Script Filename:</strong> <code><?= $_SERVER['SCRIPT_FILENAME'] ?? 'N/A' ?></code>
        </div>
        <div class="debug-item">
            <strong>Current Directory:</strong> <code><?= getcwd() ?></code>
        </div>
        <div class="debug-item">
            <strong>PHP Version:</strong> <code><?= PHP_VERSION ?></code>
        </div>
        
        <h2>🔧 Soluciones Recomendadas</h2>
        <div class="debug-item">
            <strong>Si los assets no se cargan:</strong>
            <ul>
                <li>Verifica que los archivos existan en <code>public/assets/</code></li>
                <li>Asegúrate de que XAMPP tenga permisos de lectura</li>
                <li>Verifica que no haya bloqueadores de anuncios</li>
                <li>Limpia la caché del navegador (Ctrl+F5)</li>
            </ul>
        </div>
        
        <div class="debug-item">
            <strong>Si las rutas son incorrectas:</strong>
            <ul>
                <li>Verifica la configuración de XAMPP</li>
                <li>Asegúrate de acceder desde <code>localhost/controlGastos/</code></li>
                <li>Verifica que el .htaccess no interfiera</li>
            </ul>
        </div>
    </div>
</body>
</html>
