<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Reportes y Análisis Financiero</h2>
    <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-download"></i> Exportar
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="/reports/export/financial-summary?format=json&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>">
                <i class="fas fa-file-code"></i> Resumen Financiero (JSON)
            </a></li>
            <li><a class="dropdown-item" href="/reports/export/financial-summary?format=csv&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>">
                <i class="fas fa-file-csv"></i> Resumen Financiero (CSV)
            </a></li>
        </ul>
    </div>
</div>

<!-- Filtros de Período -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-calendar"></i> Período de Análisis</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="/reports" class="row g-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">Fecha Inicio</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">Fecha Fin</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> Generar Reporte
                    </button>
                    <a href="/reports" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh"></i> Limpiar
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Navegación de Reportes -->
<div class="row mb-4">
    <div class="col-12">
        <div class="nav nav-pills justify-content-center" role="tablist">
            <a class="nav-link active" href="/reports">
                <i class="fas fa-chart-pie"></i> Resumen General
            </a>
            <a class="nav-link" href="/reports/cash-flow">
                <i class="fas fa-exchange-alt"></i> Flujo de Efectivo
            </a>
            <a class="nav-link" href="/reports/category-expenses">
                <i class="fas fa-tags"></i> Por Categorías
            </a>
            <a class="nav-link" href="/reports/trends">
                <i class="fas fa-chart-line"></i> Tendencias
            </a>
            <a class="nav-link" href="/reports/accounts">
                <i class="fas fa-university"></i> Cuentas
            </a>
            <a class="nav-link" href="/reports/compare-periods">
                <i class="fas fa-balance-scale"></i> Comparar Períodos
            </a>
        </div>
    </div>
</div>

<?php if ($financial_summary): ?>
<!-- Resumen Financiero -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Resumen Financiero
                    <small class="text-muted">
                        (<?= date('d/m/Y', strtotime($financial_summary['period']['start_date'])) ?> - 
                         <?= date('d/m/Y', strtotime($financial_summary['period']['end_date'])) ?>)
                    </small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-success">+$ <?= number_format($financial_summary['summary']['total_income'], 2) ?></h4>
                            <p class="mb-0 text-muted">Total Ingresos</p>
                            <small class="text-muted"><?= $financial_summary['summary']['total_transactions'] ?> transacciones</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-danger">-$ <?= number_format($financial_summary['summary']['total_expenses'], 2) ?></h4>
                            <p class="mb-0 text-muted">Total Egresos</p>
                            <small class="text-muted">Promedio: $ <?= number_format($financial_summary['summary']['avg_expense'], 2) ?></small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="<?= $financial_summary['summary']['net_balance'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                <?= $financial_summary['summary']['net_balance'] >= 0 ? '+' : '' ?>$ <?= number_format($financial_summary['summary']['net_balance'], 2) ?>
                            </h4>
                            <p class="mb-0 text-muted">Balance Neto</p>
                            <small class="text-muted">Tasa de ahorro: <?= number_format($financial_summary['summary']['savings_rate'], 1) ?>%</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-info">$ <?= number_format($financial_summary['summary']['current_balance'], 2) ?></h4>
                            <p class="mb-0 text-muted">Balance Actual</p>
                            <small class="text-muted">Todas las cuentas</small>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6>Promedios Diarios</h6>
                        <ul class="list-unstyled">
                            <li><strong>Ingresos:</strong> $ <?= number_format($financial_summary['summary']['avg_daily_income'], 2) ?></li>
                            <li><strong>Egresos:</strong> $ <?= number_format($financial_summary['summary']['avg_daily_expense'], 2) ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Período Analizado</h6>
                        <ul class="list-unstyled">
                            <li><strong>Días:</strong> <?= $financial_summary['period']['days'] ?></li>
                            <li><strong>Transacciones:</strong> <?= $financial_summary['summary']['total_transactions'] ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- Gráfico de Ingresos vs Egresos -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Ingresos vs Egresos (Últimos 6 meses)</h6>
            </div>
            <div class="card-body">
                <canvas id="incomeVsExpensesChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Gráfico de Gastos por Categoría -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Gastos por Categoría</h6>
            </div>
            <div class="card-body">
                <canvas id="categoryPieChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<?php if ($category_report && !empty($category_report['categories'])): ?>
<!-- Top Categorías de Gastos -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tags"></i> Top Categorías de Gastos</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Categoría</th>
                                <th>Monto</th>
                                <th>Porcentaje</th>
                                <th>Transacciones</th>
                                <th>Progreso</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($category_report['categories'], 0, 10) as $category): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="<?= htmlspecialchars($category['category_icon'] ?? 'fas fa-folder') ?> me-2" 
                                               style="color: <?= htmlspecialchars($category['category_color'] ?? '#007bff') ?>;"></i>
                                            <?= htmlspecialchars($category['category_name']) ?>
                                        </div>
                                    </td>
                                    <td><strong><?= $category['amount_formatted'] ?></strong></td>
                                    <td><?= number_format($category['percentage'], 1) ?>%</td>
                                    <td><?= $category['transaction_count'] ?></td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: <?= $category['percentage'] ?>%; background-color: <?= htmlspecialchars($category['category_color'] ?? '#007bff') ?>;"
                                                 aria-valuenow="<?= $category['percentage'] ?>" aria-valuemin="0" aria-valuemax="100">
                                                <?= number_format($category['percentage'], 1) ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="/reports/category-expenses" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> Ver Reporte Completo
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($account_report && !empty($account_report['accounts'])): ?>
<!-- Resumen de Cuentas -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-university"></i> Resumen de Cuentas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach (array_slice($account_report['accounts'], 0, 4) as $account): ?>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title"><?= htmlspecialchars($account['name']) ?></h6>
                                    <h4 class="<?= $account['balance'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                        <?= $account['balance_formatted'] ?>
                                    </h4>
                                    <small class="text-muted"><?= $account['type_label'] ?></small>
                                    <br>
                                    <small class="text-muted"><?= $account['transaction_count'] ?> transacciones</small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="text-center">
                    <a href="/reports/accounts" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> Ver Todas las Cuentas
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($trend_report): ?>
<!-- Tendencias -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Tendencias (Últimos 6 meses)</h5>
            </div>
            <div class="card-body">
                <canvas id="trendChart" width="400" height="150"></canvas>
                
                <div class="row mt-3">
                    <div class="col-md-4 text-center">
                        <h6>Tendencia de Ingresos</h6>
                        <span class="badge <?= $trend_report['trends']['income_trend'] >= 0 ? 'bg-success' : 'bg-danger' ?> fs-6">
                            <i class="fas fa-arrow-<?= $trend_report['trends']['income_trend'] >= 0 ? 'up' : 'down' ?>"></i>
                            <?= $trend_report['trends']['income_trend'] >= 0 ? 'Creciente' : 'Decreciente' ?>
                        </span>
                    </div>
                    <div class="col-md-4 text-center">
                        <h6>Tendencia de Egresos</h6>
                        <span class="badge <?= $trend_report['trends']['expense_trend'] <= 0 ? 'bg-success' : 'bg-danger' ?> fs-6">
                            <i class="fas fa-arrow-<?= $trend_report['trends']['expense_trend'] <= 0 ? 'down' : 'up' ?>"></i>
                            <?= $trend_report['trends']['expense_trend'] <= 0 ? 'Decreciente' : 'Creciente' ?>
                        </span>
                    </div>
                    <div class="col-md-4 text-center">
                        <h6>Tendencia Neta</h6>
                        <span class="badge <?= $trend_report['trends']['net_trend'] >= 0 ? 'bg-success' : 'bg-danger' ?> fs-6">
                            <i class="fas fa-arrow-<?= $trend_report['trends']['net_trend'] >= 0 ? 'up' : 'down' ?>"></i>
                            <?= $trend_report['trends']['net_trend'] >= 0 ? 'Positiva' : 'Negativa' ?>
                        </span>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="/reports/trends" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> Análisis Detallado de Tendencias
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Scripts para gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de Ingresos vs Egresos
    loadIncomeVsExpensesChart();
    
    // Gráfico de Categorías
    loadCategoryPieChart();
    
    // Gráfico de Tendencias
    <?php if ($trend_report): ?>
    loadTrendChart();
    <?php endif; ?>
});

function loadIncomeVsExpensesChart() {
    fetch('/reports/chart-data?type=income_vs_expenses&months=6')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const ctx = document.getElementById('incomeVsExpensesChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.data.map(item => item.month),
                        datasets: [{
                            label: 'Ingresos',
                            data: data.data.map(item => item.income),
                            backgroundColor: 'rgba(40, 167, 69, 0.8)',
                            borderColor: 'rgba(40, 167, 69, 1)',
                            borderWidth: 1
                        }, {
                            label: 'Egresos',
                            data: data.data.map(item => item.expenses),
                            backgroundColor: 'rgba(220, 53, 69, 0.8)',
                            borderColor: 'rgba(220, 53, 69, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
}

function loadCategoryPieChart() {
    const startDate = '<?= $start_date ?>';
    const endDate = '<?= $end_date ?>';
    
    fetch(`/reports/chart-data?type=category_pie&start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                const ctx = document.getElementById('categoryPieChart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.data.map(item => item.name),
                        datasets: [{
                            data: data.data.map(item => item.value),
                            backgroundColor: data.data.map(item => item.color || '#007bff'),
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': $' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
}

<?php if ($trend_report): ?>
function loadTrendChart() {
    const trendData = <?= json_encode($trend_report['data']) ?>;
    
    const ctx = document.getElementById('trendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: trendData.map(item => item.period_label),
            datasets: [{
                label: 'Balance Neto',
                data: trendData.map(item => item.net),
                borderColor: 'rgba(0, 123, 255, 1)',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Balance Neto: $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}
<?php endif; ?>
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
