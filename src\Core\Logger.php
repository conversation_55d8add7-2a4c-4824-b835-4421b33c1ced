<?php

declare(strict_types=1);

namespace ControlGastos\Core;

/**
 * Sistema de logging de la aplicación
 * Maneja el registro de eventos, errores y debug
 */
class Logger
{
    private array $config;
    private string $logPath;
    
    // Niveles de log
    const DEBUG = 'debug';
    const INFO = 'info';
    const WARNING = 'warning';
    const ERROR = 'error';
    const CRITICAL = 'critical';

    private array $levels = [
        self::DEBUG => 0,
        self::INFO => 1,
        self::WARNING => 2,
        self::ERROR => 3,
        self::CRITICAL => 4
    ];

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->logPath = $config['files']['logs_path'] ?? __DIR__ . '/../../storage/logs';
        
        // Crear directorio de logs si no existe
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }

    /**
     * Log de debug
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log(self::DEBUG, $message, $context);
    }

    /**
     * Log de información
     */
    public function info(string $message, array $context = []): void
    {
        $this->log(self::INFO, $message, $context);
    }

    /**
     * Log de advertencia
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log(self::WARNING, $message, $context);
    }

    /**
     * Log de error
     */
    public function error(string $message, array $context = []): void
    {
        $this->log(self::ERROR, $message, $context);
    }

    /**
     * Log crítico
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log(self::CRITICAL, $message, $context);
    }

    /**
     * Método principal de logging
     */
    public function log(string $level, string $message, array $context = []): void
    {
        // Verificar si el logging está habilitado
        if (!$this->isLoggingEnabled()) {
            return;
        }

        // Verificar nivel mínimo
        if (!$this->shouldLog($level)) {
            return;
        }

        // Formatear mensaje
        $formattedMessage = $this->formatMessage($level, $message, $context);

        // Escribir al archivo
        $this->writeToFile($level, $formattedMessage);

        // Log crítico también se envía por email (opcional)
        if ($level === self::CRITICAL) {
            $this->notifyCriticalError($message, $context);
        }
    }

    /**
     * Verificar si el logging está habilitado
     */
    private function isLoggingEnabled(): bool
    {
        return $this->config['logging']['enabled'] ?? true;
    }

    /**
     * Verificar si se debe loggear este nivel
     */
    private function shouldLog(string $level): bool
    {
        $configLevel = $this->config['logging']['level'] ?? self::INFO;
        $configLevelValue = $this->levels[$configLevel] ?? 1;
        $currentLevelValue = $this->levels[$level] ?? 0;

        return $currentLevelValue >= $configLevelValue;
    }

    /**
     * Formatear mensaje de log
     */
    private function formatMessage(string $level, string $message, array $context): string
    {
        $timestamp = date('Y-m-d H:i:s');
        $levelUpper = strtoupper($level);
        
        // Información de request
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'CLI';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'CLI';
        $uri = $_SERVER['REQUEST_URI'] ?? 'CLI';
        
        // Formatear contexto
        $contextString = '';
        if (!empty($context)) {
            $contextString = ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }

        return "[{$timestamp}] {$levelUpper}: {$message} | IP: {$ip} | Request: {$method} {$uri}{$contextString}";
    }

    /**
     * Escribir al archivo de log
     */
    private function writeToFile(string $level, string $message): void
    {
        $filename = $this->getLogFilename($level);
        $filepath = $this->logPath . '/' . $filename;

        // Verificar tamaño del archivo
        if (file_exists($filepath) && filesize($filepath) > $this->getMaxFileSize()) {
            $this->rotateLogFile($filepath);
        }

        // Escribir al archivo
        file_put_contents($filepath, $message . PHP_EOL, FILE_APPEND | LOCK_EX);

        // Limpiar archivos antiguos
        $this->cleanOldLogFiles();
    }

    /**
     * Obtener nombre del archivo de log
     */
    private function getLogFilename(string $level): string
    {
        $date = date('Y-m-d');
        return "app-{$date}.log";
    }

    /**
     * Obtener tamaño máximo del archivo
     */
    private function getMaxFileSize(): int
    {
        return $this->config['logging']['max_file_size'] ?? 10485760; // 10MB
    }

    /**
     * Rotar archivo de log
     */
    private function rotateLogFile(string $filepath): void
    {
        $rotatedPath = $filepath . '.' . time();
        rename($filepath, $rotatedPath);

        // Comprimir archivo rotado (opcional)
        if (function_exists('gzopen')) {
            $this->compressLogFile($rotatedPath);
        }
    }

    /**
     * Comprimir archivo de log
     */
    private function compressLogFile(string $filepath): void
    {
        $compressedPath = $filepath . '.gz';
        
        $file = fopen($filepath, 'rb');
        $compressed = gzopen($compressedPath, 'wb9');
        
        while (!feof($file)) {
            gzwrite($compressed, fread($file, 8192));
        }
        
        fclose($file);
        gzclose($compressed);
        
        // Eliminar archivo original
        unlink($filepath);
    }

    /**
     * Limpiar archivos de log antiguos
     */
    private function cleanOldLogFiles(): void
    {
        $maxFiles = $this->config['logging']['max_files'] ?? 30;
        $files = glob($this->logPath . '/app-*.log*');
        
        if (count($files) > $maxFiles) {
            // Ordenar por fecha de modificación
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // Eliminar archivos más antiguos
            $filesToDelete = array_slice($files, 0, count($files) - $maxFiles);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }

    /**
     * Notificar error crítico por email
     */
    private function notifyCriticalError(string $message, array $context): void
    {
        // Implementar notificación por email para errores críticos
        // Se puede integrar con el servicio de mail
    }

    /**
     * Obtener logs recientes
     */
    public function getRecentLogs(int $lines = 100): array
    {
        $filename = $this->getLogFilename(self::INFO);
        $filepath = $this->logPath . '/' . $filename;
        
        if (!file_exists($filepath)) {
            return [];
        }

        $logs = [];
        $file = new \SplFileObject($filepath);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        $startLine = max(0, $totalLines - $lines);
        $file->seek($startLine);
        
        while (!$file->eof()) {
            $line = trim($file->current());
            if (!empty($line)) {
                $logs[] = $this->parseLogLine($line);
            }
            $file->next();
        }
        
        return array_reverse($logs);
    }

    /**
     * Parsear línea de log
     */
    private function parseLogLine(string $line): array
    {
        $pattern = '/\[([^\]]+)\] ([A-Z]+): (.+)/';
        
        if (preg_match($pattern, $line, $matches)) {
            return [
                'timestamp' => $matches[1],
                'level' => $matches[2],
                'message' => $matches[3],
                'raw' => $line
            ];
        }
        
        return [
            'timestamp' => '',
            'level' => 'UNKNOWN',
            'message' => $line,
            'raw' => $line
        ];
    }

    /**
     * Obtener estadísticas de logs
     */
    public function getLogStats(): array
    {
        $files = glob($this->logPath . '/app-*.log');
        $totalSize = 0;
        $totalLines = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            $totalLines += count(file($file));
        }
        
        return [
            'total_files' => count($files),
            'total_size' => $totalSize,
            'total_lines' => $totalLines,
            'log_path' => $this->logPath
        ];
    }
}
