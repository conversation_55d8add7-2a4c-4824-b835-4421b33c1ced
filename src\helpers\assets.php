<?php

/**
 * Helper functions para manejo de assets
 */

/**
 * Obtener la URL base de la aplicación
 */
function getBaseUrl(): string
{
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
    
    // Detectar si estamos en public/ o en la raíz
    if (strpos($scriptName, '/public/') !== false) {
        // Estamos en public/index.php
        $basePath = str_replace('/public/index.php', '', $scriptName);
    } else {
        // Estamos en la raíz del proyecto
        $basePath = dirname($scriptName);
        if ($basePath === '/' || $basePath === '\\') {
            $basePath = '';
        }
    }
    
    return $protocol . '://' . $host . $basePath;
}

/**
 * Obtener URL de asset
 */
function asset(string $path): string
{
    $baseUrl = getBaseUrl();
    $path = ltrim($path, '/');
    
    // Si estamos en public/, los assets están en ../public/assets
    // Si estamos en la raíz, los assets están en public/assets
    if (strpos($_SERVER['SCRIPT_NAME'] ?? '', '/public/') !== false) {
        return $baseUrl . '/public/assets/' . $path;
    } else {
        return $baseUrl . '/public/assets/' . $path;
    }
}

/**
 * Obtener URL de CSS
 */
function css(string $filename): string
{
    return asset('css/' . $filename);
}

/**
 * Obtener URL de JS
 */
function js(string $filename): string
{
    return asset('js/' . $filename);
}

/**
 * Obtener URL de imagen
 */
function img(string $filename): string
{
    return asset('img/' . $filename);
}

/**
 * Generar tags de CSS
 */
function cssTag(string $filename): string
{
    return '<link href="' . css($filename) . '" rel="stylesheet">';
}

/**
 * Generar tags de JS
 */
function jsTag(string $filename): string
{
    return '<script src="' . js($filename) . '"></script>';
}

/**
 * Incluir todos los CSS básicos
 */
function includeBasicCSS(): string
{
    $html = '';
    $html .= cssTag('bootstrap.min.css') . "\n";
    $html .= cssTag('fontawesome.min.css') . "\n";
    $html .= cssTag('custom.css') . "\n";
    return $html;
}

/**
 * Incluir todos los JS básicos
 */
function includeBasicJS(): string
{
    return jsTag('bootstrap.bundle.min.js');
}

/**
 * Debug: mostrar información de rutas
 */
function debugPaths(): array
{
    return [
        'SCRIPT_NAME' => $_SERVER['SCRIPT_NAME'] ?? 'N/A',
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'N/A',
        'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'N/A',
        'BASE_URL' => getBaseUrl(),
        'ASSET_BASE' => asset(''),
        'CSS_BOOTSTRAP' => css('bootstrap.min.css'),
        'JS_BOOTSTRAP' => js('bootstrap.bundle.min.js'),
    ];
}
