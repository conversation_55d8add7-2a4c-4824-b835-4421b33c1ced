<?php
/**
 * Script para crear la tabla de categorías
 */

try {
    // Configuración de la base de datos
    $host = 'localhost';
    $dbname = 'controlGastos';
    $username = 'root';
    $password = '';

    // Primero conectar sin especificar base de datos para crearla
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    // Crear la base de datos si no existe
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$dbname`");

    echo "🔧 CREANDO TABLA DE CATEGORÍAS\n";
    echo "=" . str_repeat("=", 50) . "\n\n";

    // Verificar si la tabla existe
    $sql = "SHOW TABLES LIKE 'categories'";
    $stmt = $pdo->query($sql);
    $tableExists = $stmt->rowCount() > 0;

    if ($tableExists) {
        echo "✅ La tabla 'categories' ya existe\n";
        
        // Verificar estructura
        $sql = "DESCRIBE categories";
        $stmt = $pdo->query($sql);
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\n📋 ESTRUCTURA ACTUAL:\n";
        foreach ($columns as $column) {
            echo "  - {$column['Field']}: {$column['Type']}\n";
        }
    } else {
        echo "⚠️ La tabla 'categories' no existe. Creándola...\n";
        
        $sql = "
        CREATE TABLE categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            color VARCHAR(7) DEFAULT '#007bff',
            icon VARCHAR(50) DEFAULT 'fas fa-tag',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_active (is_active),
            UNIQUE KEY unique_user_category (user_id, name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "✅ Tabla 'categories' creada exitosamente\n";
    }

    // Verificar si existe la tabla de subcategorías
    $sql = "SHOW TABLES LIKE 'subcategories'";
    $stmt = $pdo->query($sql);
    $subcategoriesExists = $stmt->rowCount() > 0;

    if (!$subcategoriesExists) {
        echo "\n⚠️ La tabla 'subcategories' no existe. Creándola...\n";
        
        $sql = "
        CREATE TABLE subcategories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            category_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category_id (category_id),
            INDEX idx_active (is_active),
            UNIQUE KEY unique_category_subcategory (category_id, name),
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "✅ Tabla 'subcategories' creada exitosamente\n";
    } else {
        echo "✅ La tabla 'subcategories' ya existe\n";
    }

    // Insertar categorías de ejemplo si no existen
    $sql = "SELECT COUNT(*) FROM categories";
    $stmt = $pdo->query($sql);
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        echo "\n📝 INSERTANDO CATEGORÍAS DE EJEMPLO...\n";
        
        $categories = [
            ['Alimentación', 'Gastos en comida y bebidas', '#28a745', 'fas fa-utensils'],
            ['Transporte', 'Gastos de movilidad', '#007bff', 'fas fa-car'],
            ['Hogar', 'Gastos del hogar', '#ffc107', 'fas fa-home'],
            ['Salud', 'Gastos médicos', '#dc3545', 'fas fa-medkit'],
            ['Entretenimiento', 'Ocio y diversión', '#17a2b8', 'fas fa-gamepad'],
            ['Educación', 'Gastos educativos', '#6f42c1', 'fas fa-graduation-cap'],
            ['Ropa', 'Vestimenta y accesorios', '#fd7e14', 'fas fa-tshirt'],
            ['Servicios', 'Servicios básicos', '#20c997', 'fas fa-phone']
        ];

        $sql = "INSERT INTO categories (user_id, name, description, color, icon) VALUES (?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);

        foreach ($categories as $category) {
            $stmt->execute([1, $category[0], $category[1], $category[2], $category[3]]);
            echo "  ✅ {$category[0]}\n";
        }
        
        echo "\n🎉 Categorías de ejemplo insertadas exitosamente\n";
    } else {
        echo "\n✅ Ya existen categorías en la base de datos ($count categorías)\n";
    }

    echo "\n🎊 ¡PROCESO COMPLETADO EXITOSAMENTE!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
