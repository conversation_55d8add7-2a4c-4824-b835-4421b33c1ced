<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Repositories\TransactionRepository;
use ControlGastos\Repositories\AccountRepository;
use ControlGastos\Repositories\CategoryRepository;
use ControlGastos\Repositories\ReminderRepository;
use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de reportes y análisis financiero
 * Genera reportes, estadísticas y análisis de datos financieros
 */
class ReportService
{
    private TransactionRepository $transactionRepository;
    private AccountRepository $accountRepository;
    private CategoryRepository $categoryRepository;
    private ReminderRepository $reminderRepository;
    private Logger $logger;

    public function __construct(
        TransactionRepository $transactionRepository,
        AccountRepository $accountRepository,
        CategoryRepository $categoryRepository,
        ReminderRepository $reminderRepository,
        Logger $logger
    ) {
        $this->transactionRepository = $transactionRepository;
        $this->accountRepository = $accountRepository;
        $this->categoryRepository = $categoryRepository;
        $this->reminderRepository = $reminderRepository;
        $this->logger = $logger;
    }

    /**
     * Generar reporte financiero general
     */
    public function generateFinancialSummary(int $userId, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        try {
            // Establecer fechas por defecto (último mes)
            if (!$startDate) {
                $startDate = new \DateTime('first day of last month');
                $startDate->setTime(0, 0, 0);
            }
            if (!$endDate) {
                $endDate = new \DateTime('last day of last month');
                $endDate->setTime(23, 59, 59);
            }

            // Obtener estadísticas de transacciones
            $transactionStats = $this->transactionRepository->getStats($userId, $startDate, $endDate);
            
            // Obtener resumen por categorías
            $categoryStats = $this->transactionRepository->getCategorySummary($userId, $startDate, $endDate);
            
            // Obtener balances de cuentas
            $accounts = $this->accountRepository->findByUser($userId, true);
            $accountBalances = [];
            $totalBalance = 0;
            
            foreach ($accounts as $account) {
                $balance = $this->transactionRepository->getAccountBalance($account->getId());
                $accountBalances[] = [
                    'id' => $account->getId(),
                    'name' => $account->getName(),
                    'type' => $account->getType(),
                    'balance' => $balance,
                    'balance_formatted' => '$ ' . number_format($balance, 2, '.', ',')
                ];
                $totalBalance += $balance;
            }

            // Obtener estadísticas de recordatorios
            $reminderStats = $this->reminderRepository->getStats($userId);

            // Calcular métricas adicionales
            $avgDailyExpense = $transactionStats['total_expenses'] / max(1, $startDate->diff($endDate)->days);
            $avgDailyIncome = $transactionStats['total_income'] / max(1, $startDate->diff($endDate)->days);
            $savingsRate = $transactionStats['total_income'] > 0 ? 
                (($transactionStats['total_income'] - $transactionStats['total_expenses']) / $transactionStats['total_income']) * 100 : 0;

            return [
                'success' => true,
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'days' => $startDate->diff($endDate)->days + 1
                ],
                'summary' => [
                    'total_income' => $transactionStats['total_income'],
                    'total_expenses' => $transactionStats['total_expenses'],
                    'net_balance' => $transactionStats['net_balance'],
                    'total_transactions' => $transactionStats['total_transactions'],
                    'avg_income' => $transactionStats['avg_income'],
                    'avg_expense' => $transactionStats['avg_expense'],
                    'avg_daily_expense' => $avgDailyExpense,
                    'avg_daily_income' => $avgDailyIncome,
                    'savings_rate' => $savingsRate,
                    'current_balance' => $totalBalance
                ],
                'accounts' => $accountBalances,
                'categories' => $categoryStats,
                'reminders' => $reminderStats
            ];

        } catch (Exception $e) {
            $this->logger->error('Error generando reporte financiero: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Generar reporte de flujo de efectivo mensual
     */
    public function generateCashFlowReport(int $userId, int $year, ?int $month = null): array
    {
        try {
            $data = [];
            
            if ($month) {
                // Reporte mensual detallado
                $startDate = new \DateTime("{$year}-{$month}-01");
                $endDate = clone $startDate;
                $endDate->modify('last day of this month')->setTime(23, 59, 59);
                
                $data = $this->generateMonthlyCashFlow($userId, $startDate, $endDate);
            } else {
                // Reporte anual por meses
                for ($m = 1; $m <= 12; $m++) {
                    $startDate = new \DateTime("{$year}-{$m}-01");
                    $endDate = clone $startDate;
                    $endDate->modify('last day of this month')->setTime(23, 59, 59);
                    
                    $monthStats = $this->transactionRepository->getStats($userId, $startDate, $endDate);
                    
                    $data[] = [
                        'month' => $m,
                        'month_name' => $startDate->format('F'),
                        'year' => $year,
                        'income' => $monthStats['total_income'],
                        'expenses' => $monthStats['total_expenses'],
                        'net' => $monthStats['net_balance'],
                        'transactions' => $monthStats['total_transactions']
                    ];
                }
            }

            return [
                'success' => true,
                'year' => $year,
                'month' => $month,
                'data' => $data
            ];

        } catch (Exception $e) {
            $this->logger->error('Error generando reporte de flujo de efectivo: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Generar reporte de gastos por categoría
     */
    public function generateCategoryExpenseReport(int $userId, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        try {
            // Establecer fechas por defecto (último mes)
            if (!$startDate) {
                $startDate = new \DateTime('first day of last month');
                $startDate->setTime(0, 0, 0);
            }
            if (!$endDate) {
                $endDate = new \DateTime('last day of last month');
                $endDate->setTime(23, 59, 59);
            }

            // Obtener gastos por categoría
            $categoryStats = $this->transactionRepository->getCategorySummary($userId, $startDate, $endDate);
            
            // Filtrar solo egresos y calcular porcentajes
            $expenses = array_filter($categoryStats, fn($cat) => $cat['type'] === 'expense');
            $totalExpenses = array_sum(array_column($expenses, 'total_amount'));
            
            $categoryData = [];
            foreach ($expenses as $expense) {
                $percentage = $totalExpenses > 0 ? ($expense['total_amount'] / $totalExpenses) * 100 : 0;
                
                $categoryData[] = [
                    'category_id' => $expense['category_id'],
                    'category_name' => $expense['category_name'],
                    'category_color' => $expense['category_color'],
                    'category_icon' => $expense['category_icon'],
                    'amount' => $expense['total_amount'],
                    'amount_formatted' => '$ ' . number_format($expense['total_amount'], 2, '.', ','),
                    'percentage' => $percentage,
                    'transaction_count' => $expense['transaction_count']
                ];
            }

            // Ordenar por monto descendente
            usort($categoryData, fn($a, $b) => $b['amount'] <=> $a['amount']);

            return [
                'success' => true,
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d')
                ],
                'total_expenses' => $totalExpenses,
                'categories' => $categoryData
            ];

        } catch (Exception $e) {
            $this->logger->error('Error generando reporte de categorías: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Generar reporte de tendencias
     */
    public function generateTrendReport(int $userId, string $period = 'monthly', int $periods = 12): array
    {
        try {
            $data = [];
            $currentDate = new \DateTime();
            
            for ($i = $periods - 1; $i >= 0; $i--) {
                $endDate = clone $currentDate;
                $startDate = clone $currentDate;
                
                switch ($period) {
                    case 'daily':
                        $startDate->modify("-{$i} days");
                        $endDate = clone $startDate;
                        $endDate->setTime(23, 59, 59);
                        $startDate->setTime(0, 0, 0);
                        break;
                    case 'weekly':
                        $startDate->modify("-{$i} weeks")->modify('monday this week');
                        $endDate = clone $startDate;
                        $endDate->modify('sunday this week')->setTime(23, 59, 59);
                        $startDate->setTime(0, 0, 0);
                        break;
                    case 'monthly':
                    default:
                        $startDate->modify("-{$i} months")->modify('first day of this month');
                        $endDate = clone $startDate;
                        $endDate->modify('last day of this month')->setTime(23, 59, 59);
                        $startDate->setTime(0, 0, 0);
                        break;
                }
                
                $stats = $this->transactionRepository->getStats($userId, $startDate, $endDate);
                
                $data[] = [
                    'period' => $startDate->format($period === 'daily' ? 'Y-m-d' : ($period === 'weekly' ? 'Y-\WW' : 'Y-m')),
                    'period_label' => $startDate->format($period === 'daily' ? 'd/m/Y' : ($period === 'weekly' ? '\W\e\e\k W, Y' : 'F Y')),
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'income' => $stats['total_income'],
                    'expenses' => $stats['total_expenses'],
                    'net' => $stats['net_balance'],
                    'transactions' => $stats['total_transactions'],
                    'avg_transaction' => $stats['total_transactions'] > 0 ? 
                        ($stats['total_income'] + $stats['total_expenses']) / $stats['total_transactions'] : 0
                ];
            }

            // Calcular tendencias
            $trends = $this->calculateTrends($data);

            return [
                'success' => true,
                'period' => $period,
                'periods' => $periods,
                'data' => $data,
                'trends' => $trends
            ];

        } catch (Exception $e) {
            $this->logger->error('Error generando reporte de tendencias: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Generar reporte de presupuesto vs gastos reales
     */
    public function generateBudgetReport(int $userId, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        try {
            // Por ahora, este es un placeholder ya que no hemos implementado presupuestos
            // En el futuro se compararían los presupuestos definidos con los gastos reales
            
            if (!$startDate) {
                $startDate = new \DateTime('first day of this month');
                $startDate->setTime(0, 0, 0);
            }
            if (!$endDate) {
                $endDate = new \DateTime('last day of this month');
                $endDate->setTime(23, 59, 59);
            }

            $categoryStats = $this->transactionRepository->getCategorySummary($userId, $startDate, $endDate);
            $expenses = array_filter($categoryStats, fn($cat) => $cat['type'] === 'expense');

            // Simular presupuestos basados en gastos históricos
            $budgetData = [];
            foreach ($expenses as $expense) {
                // Simular un presupuesto 20% mayor que el gasto actual
                $suggestedBudget = $expense['total_amount'] * 1.2;
                $variance = $expense['total_amount'] - $suggestedBudget;
                $variancePercentage = $suggestedBudget > 0 ? ($variance / $suggestedBudget) * 100 : 0;

                $budgetData[] = [
                    'category_id' => $expense['category_id'],
                    'category_name' => $expense['category_name'],
                    'category_color' => $expense['category_color'],
                    'budgeted' => $suggestedBudget,
                    'actual' => $expense['total_amount'],
                    'variance' => $variance,
                    'variance_percentage' => $variancePercentage,
                    'status' => $variance > 0 ? 'over' : 'under'
                ];
            }

            return [
                'success' => true,
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d')
                ],
                'categories' => $budgetData,
                'note' => 'Los presupuestos mostrados son sugerencias basadas en gastos históricos'
            ];

        } catch (Exception $e) {
            $this->logger->error('Error generando reporte de presupuesto: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Generar reporte de cuentas
     */
    public function generateAccountReport(int $userId): array
    {
        try {
            $accounts = $this->accountRepository->findByUser($userId, true);
            $accountData = [];
            $totalBalance = 0;

            foreach ($accounts as $account) {
                $balance = $this->transactionRepository->getAccountBalance($account->getId());
                $transactions = $this->transactionRepository->findByAccount($account->getId(), $userId);
                
                // Calcular estadísticas de la cuenta
                $incomeTransactions = array_filter($transactions, fn($t) => $t['type'] === 'income');
                $expenseTransactions = array_filter($transactions, fn($t) => $t['type'] === 'expense');
                
                $totalIncome = array_sum(array_column($incomeTransactions, 'amount'));
                $totalExpenses = array_sum(array_column($expenseTransactions, 'amount'));
                
                $accountData[] = [
                    'id' => $account->getId(),
                    'name' => $account->getName(),
                    'type' => $account->getType(),
                    'type_label' => $account->getTypeLabel(),
                    'balance' => $balance,
                    'balance_formatted' => '$ ' . number_format($balance, 2, '.', ','),
                    'total_income' => $totalIncome,
                    'total_expenses' => $totalExpenses,
                    'transaction_count' => count($transactions),
                    'last_transaction' => !empty($transactions) ? $transactions[0]['transaction_date'] : null,
                    'is_active' => $account->isActive()
                ];
                
                if ($account->isActive()) {
                    $totalBalance += $balance;
                }
            }

            // Ordenar por balance descendente
            usort($accountData, fn($a, $b) => $b['balance'] <=> $a['balance']);

            return [
                'success' => true,
                'accounts' => $accountData,
                'total_balance' => $totalBalance,
                'total_balance_formatted' => '$ ' . number_format($totalBalance, 2, '.', ','),
                'account_count' => count($accountData)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error generando reporte de cuentas: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Exportar reporte a CSV
     */
    public function exportToCsv(array $data, string $filename): string
    {
        $csvContent = '';
        
        if (!empty($data)) {
            // Encabezados
            $headers = array_keys($data[0]);
            $csvContent .= implode(',', $headers) . "\n";
            
            // Datos
            foreach ($data as $row) {
                $csvRow = [];
                foreach ($row as $value) {
                    // Escapar comillas y envolver en comillas si contiene comas
                    $value = str_replace('"', '""', $value);
                    if (strpos($value, ',') !== false || strpos($value, '"') !== false) {
                        $value = '"' . $value . '"';
                    }
                    $csvRow[] = $value;
                }
                $csvContent .= implode(',', $csvRow) . "\n";
            }
        }
        
        return $csvContent;
    }

    /**
     * Generar flujo de efectivo mensual detallado
     */
    private function generateMonthlyCashFlow(int $userId, \DateTime $startDate, \DateTime $endDate): array
    {
        $dailyData = [];
        $currentDate = clone $startDate;
        
        while ($currentDate <= $endDate) {
            $dayStart = clone $currentDate;
            $dayStart->setTime(0, 0, 0);
            $dayEnd = clone $currentDate;
            $dayEnd->setTime(23, 59, 59);
            
            $dayStats = $this->transactionRepository->getStats($userId, $dayStart, $dayEnd);
            
            $dailyData[] = [
                'date' => $currentDate->format('Y-m-d'),
                'day' => $currentDate->format('j'),
                'day_name' => $currentDate->format('D'),
                'income' => $dayStats['total_income'],
                'expenses' => $dayStats['total_expenses'],
                'net' => $dayStats['net_balance'],
                'transactions' => $dayStats['total_transactions']
            ];
            
            $currentDate->modify('+1 day');
        }
        
        return $dailyData;
    }

    /**
     * Calcular tendencias de los datos
     */
    private function calculateTrends(array $data): array
    {
        if (count($data) < 2) {
            return [
                'income_trend' => 0,
                'expense_trend' => 0,
                'net_trend' => 0
            ];
        }

        $incomes = array_column($data, 'income');
        $expenses = array_column($data, 'expenses');
        $nets = array_column($data, 'net');

        return [
            'income_trend' => $this->calculateLinearTrend($incomes),
            'expense_trend' => $this->calculateLinearTrend($expenses),
            'net_trend' => $this->calculateLinearTrend($nets)
        ];
    }

    /**
     * Calcular tendencia lineal simple
     */
    private function calculateLinearTrend(array $values): float
    {
        $n = count($values);
        if ($n < 2) return 0;

        $sumX = 0;
        $sumY = 0;
        $sumXY = 0;
        $sumX2 = 0;

        for ($i = 0; $i < $n; $i++) {
            $x = $i + 1;
            $y = $values[$i];
            
            $sumX += $x;
            $sumY += $y;
            $sumXY += $x * $y;
            $sumX2 += $x * $x;
        }

        $denominator = $n * $sumX2 - $sumX * $sumX;
        if ($denominator == 0) return 0;

        return ($n * $sumXY - $sumX * $sumY) / $denominator;
    }

    /**
     * Obtener datos para gráficos
     */
    public function getChartData(int $userId, string $chartType, array $params = []): array
    {
        try {
            switch ($chartType) {
                case 'income_vs_expenses':
                    return $this->getIncomeVsExpensesChart($userId, $params);
                case 'category_pie':
                    return $this->getCategoryPieChart($userId, $params);
                case 'monthly_trend':
                    return $this->getMonthlyTrendChart($userId, $params);
                case 'account_balance':
                    return $this->getAccountBalanceChart($userId);
                default:
                    return [
                        'success' => false,
                        'message' => 'Tipo de gráfico no válido'
                    ];
            }
        } catch (Exception $e) {
            $this->logger->error('Error generando datos de gráfico: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Datos para gráfico de ingresos vs egresos
     */
    private function getIncomeVsExpensesChart(int $userId, array $params): array
    {
        $months = $params['months'] ?? 6;
        $data = [];
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $startDate = new \DateTime();
            $startDate->modify("-{$i} months")->modify('first day of this month')->setTime(0, 0, 0);
            $endDate = clone $startDate;
            $endDate->modify('last day of this month')->setTime(23, 59, 59);
            
            $stats = $this->transactionRepository->getStats($userId, $startDate, $endDate);
            
            $data[] = [
                'month' => $startDate->format('M Y'),
                'income' => $stats['total_income'],
                'expenses' => $stats['total_expenses']
            ];
        }
        
        return [
            'success' => true,
            'data' => $data
        ];
    }

    /**
     * Datos para gráfico circular de categorías
     */
    private function getCategoryPieChart(int $userId, array $params): array
    {
        $startDate = isset($params['start_date']) ? new \DateTime($params['start_date']) : new \DateTime('first day of this month');
        $endDate = isset($params['end_date']) ? new \DateTime($params['end_date']) : new \DateTime('last day of this month');
        
        $categoryStats = $this->transactionRepository->getCategorySummary($userId, $startDate, $endDate);
        $expenses = array_filter($categoryStats, fn($cat) => $cat['type'] === 'expense');
        
        $data = array_map(function($expense) {
            return [
                'name' => $expense['category_name'],
                'value' => $expense['total_amount'],
                'color' => $expense['category_color']
            ];
        }, $expenses);
        
        return [
            'success' => true,
            'data' => $data
        ];
    }

    /**
     * Datos para gráfico de tendencia mensual
     */
    private function getMonthlyTrendChart(int $userId, array $params): array
    {
        $result = $this->generateTrendReport($userId, 'monthly', $params['months'] ?? 12);
        
        if (!$result['success']) {
            return $result;
        }
        
        $data = array_map(function($item) {
            return [
                'month' => $item['period_label'],
                'net' => $item['net'],
                'income' => $item['income'],
                'expenses' => $item['expenses']
            ];
        }, $result['data']);
        
        return [
            'success' => true,
            'data' => $data,
            'trends' => $result['trends']
        ];
    }

    /**
     * Datos para gráfico de balance de cuentas
     */
    private function getAccountBalanceChart(int $userId): array
    {
        $accounts = $this->accountRepository->findByUser($userId, true);
        $data = [];
        
        foreach ($accounts as $account) {
            $balance = $this->transactionRepository->getAccountBalance($account->getId());
            if ($balance > 0) { // Solo mostrar cuentas con balance positivo
                $data[] = [
                    'name' => $account->getName(),
                    'value' => $balance,
                    'type' => $account->getTypeLabel()
                ];
            }
        }
        
        return [
            'success' => true,
            'data' => $data
        ];
    }
}
