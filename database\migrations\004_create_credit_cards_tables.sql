-- Migración 004: Crear tablas para sistema de tarjetas de crédito
-- Fecha: 2024-12-30
-- Descripción: Sistema completo de gestión de tarjetas de crédito

-- Tabla principal de tarjetas de crédito
CREATE TABLE IF NOT EXISTS credit_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    card_name VARCHAR(100) NOT NULL COMMENT 'Nombre personalizado de la tarjeta',
    bank_name VARCHAR(100) NOT NULL COMMENT 'Nombre del banco emisor',
    card_number_last4 VARCHAR(4) DEFAULT NULL COMMENT 'Últimos 4 dígitos de la tarjeta',
    credit_limit DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Cupo total de la tarjeta',
    expiry_date DATE NOT NULL COMMENT 'Fecha de expiración de la tarjeta',
    cut_off_day INT NOT NULL DEFAULT 1 COMMENT 'Día del mes de corte (1-31)',
    payment_due_days INT NOT NULL DEFAULT 20 COMMENT 'Días después del corte para pagar',
    cvv VARCHAR(4) DEFAULT NULL COMMENT 'Código CCV (opcional, encriptado)',
    description TEXT DEFAULT NULL COMMENT 'Descripción adicional de la tarjeta',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Si la tarjeta está activa',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices y restricciones
    INDEX idx_user_id (user_id),
    INDEX idx_active (is_active),
    INDEX idx_cut_off_day (cut_off_day),
    
    -- Restricciones
    CONSTRAINT fk_credit_cards_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT chk_credit_limit CHECK (credit_limit >= 0),
    CONSTRAINT chk_cut_off_day CHECK (cut_off_day BETWEEN 1 AND 31),
    CONSTRAINT chk_payment_due_days CHECK (payment_due_days BETWEEN 1 AND 60)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de transacciones de tarjetas de crédito
CREATE TABLE IF NOT EXISTS credit_card_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    credit_card_id INT NOT NULL,
    user_id INT NOT NULL,
    transaction_type ENUM('purchase', 'cash_advance', 'fee', 'interest', 'adjustment', 'refund') NOT NULL DEFAULT 'purchase',
    amount DECIMAL(15,2) NOT NULL COMMENT 'Monto de la transacción (positivo para cargos)',
    description VARCHAR(255) NOT NULL COMMENT 'Descripción de la transacción',
    merchant VARCHAR(150) DEFAULT NULL COMMENT 'Comercio donde se realizó',
    category_id INT DEFAULT NULL COMMENT 'Categoría de gasto',
    transaction_date DATE NOT NULL COMMENT 'Fecha de la transacción',
    posting_date DATE DEFAULT NULL COMMENT 'Fecha de procesamiento',
    reference_number VARCHAR(50) DEFAULT NULL COMMENT 'Número de referencia',
    installments INT DEFAULT 1 COMMENT 'Número de cuotas (1 = contado)',
    installment_number INT DEFAULT 1 COMMENT 'Número de cuota actual',
    notes TEXT DEFAULT NULL COMMENT 'Notas adicionales',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_credit_card_id (credit_card_id),
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_date (transaction_date),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_category_id (category_id),
    INDEX idx_posting_date (posting_date),
    
    -- Restricciones (se agregarán después)
    -- CONSTRAINT fk_cc_transactions_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_cc_transactions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_cc_transactions_category FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    CONSTRAINT chk_installments CHECK (installments >= 1),
    CONSTRAINT chk_installment_number CHECK (installment_number >= 1 AND installment_number <= installments)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de pagos realizados a tarjetas de crédito
CREATE TABLE IF NOT EXISTS credit_card_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    credit_card_id INT NOT NULL,
    user_id INT NOT NULL,
    payment_type ENUM('minimum', 'full', 'partial', 'extra') NOT NULL DEFAULT 'partial',
    amount DECIMAL(15,2) NOT NULL COMMENT 'Monto del pago',
    payment_date DATE NOT NULL COMMENT 'Fecha del pago',
    payment_method ENUM('bank_transfer', 'cash', 'check', 'online', 'automatic_debit', 'other') DEFAULT 'bank_transfer',
    reference_number VARCHAR(50) DEFAULT NULL COMMENT 'Número de referencia del pago',
    cut_off_period DATE DEFAULT NULL COMMENT 'Período de corte al que aplica el pago',
    notes TEXT DEFAULT NULL COMMENT 'Notas del pago',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_credit_card_id (credit_card_id),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_payment_type (payment_type),
    INDEX idx_cut_off_period (cut_off_period),
    
    -- Restricciones (se agregarán después)
    -- CONSTRAINT fk_cc_payments_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_cc_payments_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT chk_payment_amount CHECK (amount > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla para estados de cuenta mensuales (opcional, para tracking)
CREATE TABLE IF NOT EXISTS credit_card_statements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    credit_card_id INT NOT NULL,
    user_id INT NOT NULL,
    statement_date DATE NOT NULL COMMENT 'Fecha del estado de cuenta',
    cut_off_date DATE NOT NULL COMMENT 'Fecha de corte',
    due_date DATE NOT NULL COMMENT 'Fecha límite de pago',
    previous_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'Saldo anterior',
    new_charges DECIMAL(15,2) DEFAULT 0.00 COMMENT 'Nuevos cargos',
    payments_credits DECIMAL(15,2) DEFAULT 0.00 COMMENT 'Pagos y créditos',
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'Saldo actual',
    minimum_payment DECIMAL(15,2) DEFAULT 0.00 COMMENT 'Pago mínimo',
    interest_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Tasa de interés mensual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_credit_card_id (credit_card_id),
    INDEX idx_user_id (user_id),
    INDEX idx_statement_date (statement_date),
    INDEX idx_cut_off_date (cut_off_date),
    INDEX idx_due_date (due_date),
    
    -- Restricciones (se agregarán después)
    -- CONSTRAINT fk_cc_statements_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_cc_statements_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT uk_card_statement UNIQUE (credit_card_id, statement_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertar datos de ejemplo (opcional)
-- INSERT INTO credit_cards (user_id, card_name, bank_name, credit_limit, expiry_date, cut_off_day, payment_due_days, description) VALUES
-- (1, 'Visa Principal', 'Banco Nacional', 50000.00, '2027-12-31', 15, 20, 'Tarjeta principal para gastos cotidianos'),
-- (1, 'Mastercard Compras', 'Banco Internacional', 30000.00, '2026-08-31', 5, 25, 'Tarjeta para compras online y viajes');

-- Agregar foreign keys después de crear todas las tablas
ALTER TABLE credit_card_transactions
ADD CONSTRAINT fk_cc_transactions_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_cc_transactions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE credit_card_payments
ADD CONSTRAINT fk_cc_payments_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_cc_payments_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE credit_card_statements
ADD CONSTRAINT fk_cc_statements_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_cc_statements_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Comentarios de documentación
ALTER TABLE credit_cards COMMENT = 'Tabla principal de tarjetas de crédito del usuario';
ALTER TABLE credit_card_transactions COMMENT = 'Registro de todas las transacciones realizadas con tarjetas de crédito';
ALTER TABLE credit_card_payments COMMENT = 'Registro de pagos realizados a las tarjetas de crédito';
ALTER TABLE credit_card_statements COMMENT = 'Estados de cuenta mensuales de las tarjetas de crédito';
