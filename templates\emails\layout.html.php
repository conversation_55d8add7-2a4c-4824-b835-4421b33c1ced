<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?= htmlspecialchars($subject ?? 'Control de Gastos') ?></title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        /* Base styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f4f4f4;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        /* Header */
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px 20px;
            text-align: center;
        }

        .email-header h1 {
            color: #ffffff;
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .email-header .logo {
            font-size: 48px;
            color: #ffffff;
            margin-bottom: 10px;
        }

        /* Content */
        .email-content {
            padding: 40px 30px;
        }

        .email-content h2 {
            color: #333333;
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 20px 0;
        }

        .email-content h3 {
            color: #555555;
            font-size: 20px;
            font-weight: 600;
            margin: 30px 0 15px 0;
        }

        .email-content p {
            margin: 0 0 20px 0;
            line-height: 1.6;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 4px 12px rgba(17, 153, 142, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            box-shadow: 0 4px 12px rgba(255, 65, 108, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
        }

        /* Cards */
        .card {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .card-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .card-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .card-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .card-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        /* Tables */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: #f8f9fa;
        }

        /* Financial amounts */
        .amount-positive {
            color: #28a745;
            font-weight: 600;
        }

        .amount-negative {
            color: #dc3545;
            font-weight: 600;
        }

        /* Footer */
        .email-footer {
            background-color: #f8f9fa;
            padding: 30px 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .email-footer p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #6c757d;
        }

        .email-footer a {
            color: #667eea;
            text-decoration: none;
        }

        .email-footer a:hover {
            text-decoration: underline;
        }

        /* Social links */
        .social-links {
            margin: 20px 0;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6c757d;
            font-size: 20px;
            text-decoration: none;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            .email-content {
                padding: 20px 15px !important;
            }
            
            .email-header {
                padding: 20px 15px !important;
            }
            
            .email-header h1 {
                font-size: 24px !important;
            }
            
            .btn {
                display: block !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }
            
            .table {
                font-size: 14px !important;
            }
            
            .table th,
            .table td {
                padding: 8px 4px !important;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1a1a1a !important;
            }
            
            .email-content {
                color: #e0e0e0 !important;
            }
            
            .email-content h2,
            .email-content h3 {
                color: #ffffff !important;
            }
            
            .card {
                background-color: #2a2a2a !important;
                border-color: #404040 !important;
                color: #e0e0e0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="logo">💰</div>
            <h1><?= htmlspecialchars($app_name ?? 'Control de Gastos') ?></h1>
        </div>

        <!-- Content -->
        <div class="email-content">
            <?= $content ?>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p>
                <strong><?= htmlspecialchars($app_name ?? 'Control de Gastos') ?></strong><br>
                Tu asistente personal para el control financiero
            </p>
            
            <div class="social-links">
                <a href="#" title="Facebook">📘</a>
                <a href="#" title="Twitter">🐦</a>
                <a href="#" title="LinkedIn">💼</a>
                <a href="#" title="Instagram">📷</a>
            </div>
            
            <p>
                <a href="<?= $dashboard_url ?? '#' ?>">Ir al Dashboard</a> |
                <a href="<?= $support_email ? 'mailto:' . $support_email : '#' ?>">Soporte</a> |
                <a href="#">Política de Privacidad</a>
            </p>
            
            <p>
                <small>
                    Este email fue enviado a <?= htmlspecialchars($to_email ?? '') ?><br>
                    Si no deseas recibir estos emails, puedes <a href="#">darte de baja aquí</a>
                </small>
            </p>
            
            <p>
                <small>
                    © <?= date('Y') ?> <?= htmlspecialchars($app_name ?? 'Control de Gastos') ?>. Todos los derechos reservados.
                </small>
            </p>
        </div>
    </div>
</body>
</html>
