<?php
$content = ob_start();
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Dashboard Financiero</h1>
        <p class="text-muted mb-0">Resumen de tu situación financiera actual</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
            <span class="icon-emoji">🔄</span>
            <span class="d-none d-md-inline ms-1">Actualizar</span>
        </button>
        <a href="/reports" class="btn btn-outline-secondary">
            <span class="icon-emoji">📊</span>
            <span class="d-none d-md-inline ms-1">Ver Reportes</span>
        </a>
    </div>
</div>

<!-- Financial Summary Cards - Primera Fila -->
<div class="row g-4 mb-4">
    <!-- Total Balance -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-gradient-primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Patrimonio Total</h6>
                        <h3 class="text-white mb-0" id="totalBalance">
                            <?= isset($financial_summary['total_balance']) ? '$' . number_format($financial_summary['total_balance'], 0) : '$0' ?>
                        </h3>
                        <small class="text-white-50">
                            <span class="icon-emoji">📈</span>
                            Cuentas + Crédito disponible
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <span class="icon-emoji" style="font-size: 1.25rem;">💰</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Income -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-gradient-success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Ingresos del Mes</h6>
                        <h3 class="text-white mb-0" id="monthlyIncome">
                            <?= isset($financial_summary['monthly_income']) ? '$' . number_format($financial_summary['monthly_income'], 0) : '$0' ?>
                        </h3>
                        <small class="text-white-50">
                            <i class="fas fa-receipt"></i>
                            <?= isset($financial_summary['income_count']) ? $financial_summary['income_count'] : 0 ?> transacciones
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-arrow-up fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Expenses -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-gradient-danger h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Gastos del Mes</h6>
                        <h3 class="text-white mb-0" id="monthlyExpenses">
                            <?= isset($financial_summary['monthly_expenses']) ? '$' . number_format($financial_summary['monthly_expenses'], 0) : '$0' ?>
                        </h3>
                        <small class="text-white-50">
                            <i class="fas fa-receipt"></i>
                            <?= isset($financial_summary['expense_count']) ? $financial_summary['expense_count'] : 0 ?> transacciones
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-arrow-down fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Net Income -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-gradient-<?= isset($financial_summary['net_income']) && $financial_summary['net_income'] >= 0 ? 'success' : 'warning' ?> h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Balance del Mes</h6>
                        <h3 class="text-white mb-0" id="netIncome">
                            <?= isset($financial_summary['net_income']) ? '$' . number_format($financial_summary['net_income'], 0) : '$0' ?>
                        </h3>
                        <small class="text-white-50">
                            <i class="fas fa-<?= isset($financial_summary['net_income']) && $financial_summary['net_income'] >= 0 ? 'plus' : 'minus' ?>"></i>
                            <?= isset($financial_summary['net_income']) && $financial_summary['net_income'] >= 0 ? 'Superávit' : 'Déficit' ?>
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-balance-scale fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Segunda Fila de Métricas -->
<div class="row g-4 mb-4">
    <!-- Savings Rate -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-gradient-info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Tasa de Ahorro</h6>
                        <h3 class="text-white mb-0" id="savingsRate">
                            <?= isset($financial_summary['savings_rate']) ? number_format($financial_summary['savings_rate'], 1) . '%' : '0%' ?>
                        </h3>
                        <small class="text-white-50">
                            <i class="fas fa-target"></i>
                            Meta: 20%
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-piggy-bank fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Credit Utilization -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-gradient-<?= isset($financial_summary['credit_utilization']) && $financial_summary['credit_utilization'] > 70 ? 'danger' : (isset($financial_summary['credit_utilization']) && $financial_summary['credit_utilization'] > 30 ? 'warning' : 'success') ?> h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Utilización de Crédito</h6>
                        <h3 class="text-white mb-0" id="creditUtilization">
                            <?= isset($financial_summary['credit_utilization']) ? number_format($financial_summary['credit_utilization'], 1) . '%' : '0%' ?>
                        </h3>
                        <small class="text-white-50">
                            <i class="fas fa-info-circle"></i>
                            Recomendado: <30%
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-credit-card fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Average Transaction -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-gradient-secondary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Gasto Promedio</h6>
                        <h3 class="text-white mb-0" id="avgTransaction">
                            <?= isset($financial_summary['avg_transaction']) ? '$' . number_format($financial_summary['avg_transaction'], 0) : '$0' ?>
                        </h3>
                        <small class="text-white-50">
                            <i class="fas fa-calculator"></i>
                            Por transacción
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-chart-bar fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-gradient-dark h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Acciones Rápidas</h6>
                        <div class="d-grid gap-2">
                            <a href="?route=accounts" class="btn btn-light btn-sm">
                                <i class="fas fa-plus me-1"></i>Nueva Transacción
                            </a>
                            <a href="?route=banks" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-university me-1"></i>Gestionar Bancos
                            </a>
                        </div>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-bolt fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row g-4 mb-4">
    <!-- Income vs Expenses Chart -->
    <div class="col-lg-8">
        <div class="card h-100">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        Ingresos vs Gastos
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="chartPeriod" id="chart6months" checked>
                        <label class="btn btn-outline-primary" for="chart6months">6M</label>
                        
                        <input type="radio" class="btn-check" name="chartPeriod" id="chart1year">
                        <label class="btn btn-outline-primary" for="chart1year">1A</label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="incomeExpensesChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Category Distribution -->
    <div class="col-lg-4">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie text-primary me-2"></i>
                    Gastos por Categoría
                </h5>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" height="300"></canvas>
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Top categoría:</span>
                        <span class="fw-bold">Alimentación (35%)</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Total categorías:</span>
                        <span class="fw-bold"><?= count($category_expenses ?? []) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Accounts and Recent Transactions -->
<div class="row g-4 mb-4">
    <!-- Account Balances -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-university text-primary me-2"></i>
                        Mis Cuentas
                    </h5>
                    <a href="/accounts" class="btn btn-sm btn-outline-primary">Ver Todas</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($accounts)): ?>
                    <div class="row g-3">
                        <?php foreach (array_slice($accounts, 0, 4) as $account): ?>
                            <div class="col-md-6">
                                <div class="border rounded p-3 h-100">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1"><?= htmlspecialchars($account['name']) ?></h6>
                                            <small class="text-muted"><?= htmlspecialchars($account['type_label'] ?? $account['type']) ?></small>
                                        </div>
                                        <i class="fas fa-<?= $account['type'] === 'savings' ? 'piggy-bank' : ($account['type'] === 'credit' ? 'credit-card' : 'university') ?> text-primary"></i>
                                    </div>
                                    <h5 class="mb-0 <?= $account['balance'] >= 0 ? 'amount-positive' : 'amount-negative' ?>">
                                        $<?= number_format($account['balance'], 2) ?>
                                    </h5>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-university fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No tienes cuentas registradas</h6>
                        <a href="/accounts/create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Crear Primera Cuenta
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exchange-alt text-primary me-2"></i>
                        Transacciones Recientes
                    </h5>
                    <a href="/transactions" class="btn btn-sm btn-outline-primary">Ver Todas</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_transactions)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                            <div class="list-group-item px-0 py-3 border-0 border-bottom">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle p-2 me-3" style="background-color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?>20;">
                                            <i class="<?= htmlspecialchars($transaction['category_icon'] ?? 'fas fa-circle') ?>" 
                                               style="color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?>;"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1"><?= htmlspecialchars($transaction['description']) ?></h6>
                                            <small class="text-muted">
                                                <?= htmlspecialchars($transaction['category_name']) ?> • 
                                                <?= date('d/m/Y', strtotime($transaction['transaction_date'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <span class="fw-bold <?= $transaction['type'] === 'income' ? 'amount-positive' : 'amount-negative' ?>">
                                            <?= $transaction['type'] === 'income' ? '+' : '-' ?>$<?= number_format($transaction['amount'], 2) ?>
                                        </span>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($transaction['account_name']) ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No hay transacciones registradas</h6>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#quickTransactionModal">
                            <i class="fas fa-plus me-1"></i>Crear Primera Transacción
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Banks and Reminders -->
<div class="row g-4 mb-4">
    <!-- Banks Summary -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-university text-primary me-2"></i>
                        Entidades Bancarias
                    </h5>
                    <a href="?route=banks" class="btn btn-sm btn-outline-primary">Gestionar</a>
                </div>
            </div>
            <div class="card-body">
                <?php
                // Obtener estadísticas de bancos
                $userId = $session->get('user_id');
                $sql = "SELECT
                    COUNT(*) as total_banks,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_banks,
                    (SELECT COUNT(*) FROM bank_accounts ba WHERE ba.bank_id IN (SELECT id FROM banks WHERE user_id = ?)) as total_accounts,
                    (SELECT COUNT(*) FROM credit_cards cc WHERE cc.bank_id IN (SELECT id FROM banks WHERE user_id = ?)) as total_cards
                    FROM banks WHERE user_id = ?";
                $stmt = $database->getConnection()->prepare($sql);
                $stmt->execute([$userId, $userId, $userId]);
                $bankStats = $stmt->fetch();
                ?>

                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary mb-1"><?= $bankStats['total_banks'] ?? 0 ?></h4>
                            <small class="text-muted">Total Bancos</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-success mb-1"><?= $bankStats['active_banks'] ?? 0 ?></h4>
                            <small class="text-muted">Activos</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-info mb-1"><?= $bankStats['total_accounts'] ?? 0 ?></h4>
                            <small class="text-muted">Cuentas</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-warning mb-1"><?= $bankStats['total_cards'] ?? 0 ?></h4>
                            <small class="text-muted">Tarjetas</small>
                        </div>
                    </div>
                </div>

                <div class="mt-3 d-grid gap-2">
                    <a href="?route=banks&action=create" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Agregar Banco
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Reminders -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell text-warning me-2"></i>
                        Recordatorios Próximos
                    </h5>
                    <a href="/reminders" class="btn btn-sm btn-outline-primary">Ver Todos</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($upcoming_reminders)): ?>
                    <?php foreach (array_slice($upcoming_reminders, 0, 3) as $reminder): ?>
                        <div class="d-flex align-items-center mb-3 p-3 border rounded">
                            <div class="me-3">
                                <i class="fas fa-<?= $reminder['type'] === 'payment' ? 'credit-card' : 'bell' ?> text-warning"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?= htmlspecialchars($reminder['title']) ?></h6>
                                <small class="text-muted">
                                    Vence: <?= date('d/m/Y', strtotime($reminder['due_date'])) ?>
                                    <?php if (isset($reminder['amount'])): ?>
                                        • $<?= number_format($reminder['amount'], 2) ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                            <div>
                                <?php
                                $daysUntilDue = ceil((strtotime($reminder['due_date']) - time()) / 86400);
                                $badgeClass = $daysUntilDue <= 0 ? 'bg-danger' : ($daysUntilDue <= 3 ? 'bg-warning' : 'bg-info');
                                ?>
                                <span class="badge <?= $badgeClass ?>">
                                    <?= $daysUntilDue <= 0 ? 'Vencido' : $daysUntilDue . ' días' ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No hay recordatorios próximos</h6>
                        <a href="/reminders/create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Crear Recordatorio
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Financial Goals -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-target text-success me-2"></i>
                        Metas Financieras
                    </h5>
                    <a href="/goals" class="btn btn-sm btn-outline-primary">Ver Todas</a>
                </div>
            </div>
            <div class="card-body">
                <!-- Savings Goal -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Ahorro Mensual</h6>
                        <span class="text-muted">$15,000 / $20,000</span>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 75%"></div>
                    </div>
                    <small class="text-muted">75% completado • Faltan $5,000</small>
                </div>

                <!-- Emergency Fund -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Fondo de Emergencia</h6>
                        <span class="text-muted">$45,000 / $60,000</span>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: 75%"></div>
                    </div>
                    <small class="text-muted">75% completado • Faltan $15,000</small>
                </div>

                <!-- Vacation Fund -->
                <div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Vacaciones 2024</h6>
                        <span class="text-muted">$8,500 / $25,000</span>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 34%"></div>
                    </div>
                    <small class="text-muted">34% completado • Faltan $16,500</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt text-primary me-2"></i>
                    Acciones Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-2 col-md-4 col-6">
                        <button class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" 
                                data-bs-toggle="modal" data-bs-target="#quickTransactionModal">
                            <i class="fas fa-plus fa-2x mb-2"></i>
                            <span>Nueva Transacción</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="/accounts/transfer" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                            <span>Transferir</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="?route=reports" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <span>Ver Reportes</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="?route=banks" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-university fa-2x mb-2"></i>
                            <span>Bancos</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="?route=reminders&action=create" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-bell fa-2x mb-2"></i>
                            <span>Recordatorio</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="?route=backup" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                            <span>Backup</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboardCharts();
});

function initializeDashboardCharts() {
    // Income vs Expenses Chart
    const incomeExpensesCtx = document.getElementById('incomeExpensesChart').getContext('2d');
    new Chart(incomeExpensesCtx, {
        type: 'line',
        data: {
            labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'],
            datasets: [{
                label: 'Ingresos',
                data: [45000, 52000, 48000, 61000, 55000, 67000],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }, {
                label: 'Gastos',
                data: [38000, 42000, 35000, 48000, 40000, 45000],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Category Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: ['Alimentación', 'Transporte', 'Entretenimiento', 'Servicios', 'Otros'],
            datasets: [{
                data: [35, 20, 15, 20, 10],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

function refreshDashboard() {
    showLoading(true);
    
    // Simulate refresh
    setTimeout(() => {
        showLoading(false);
        showToast('Dashboard actualizado', 'success');
    }, 1500);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
