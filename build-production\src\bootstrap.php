<?php

declare(strict_types=1);

// Bootstrap de la aplicación
// Configuración inicial y carga de dependencias

// Configurar zona horaria
date_default_timezone_set('America/Mexico_City');

// Detectar entorno
$environment = $_ENV['APP_ENV'] ?? 'development';
$isProduction = $environment === 'production';

// Configurar manejo de errores según entorno
if ($isProduction) {
    error_reporting(E_ERROR | E_WARNING | E_PARSE);
    ini_set('display_errors', '0');
    ini_set('log_errors', '1');
    ini_set('error_log', __DIR__ . '/../logs/php-errors.log');
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
}

// Cargar variables de entorno
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue; // Comentarios
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Autoloader simple
spl_autoload_register(function ($class) {
    $prefix = 'ControlGastos\\';
    $base_dir = __DIR__ . '/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Configuración
$config = [];

// Configuración de base de datos
$config['database'] = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'dbname' => $_ENV['DB_NAME'] ?? 'control_gastos',
    'username' => $_ENV['DB_USER'] ?? 'root',
    'password' => $_ENV['DB_PASS'] ?? '',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];

// Configuración de aplicación
$config['app'] = [
    'name' => $_ENV['APP_NAME'] ?? 'Control de Gastos',
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',
    'env' => $environment,
    'debug' => $_ENV['APP_DEBUG'] ?? !$isProduction,
    'key' => $_ENV['APP_KEY'] ?? 'default-key-change-in-production',
    'timezone' => $_ENV['APP_TIMEZONE'] ?? 'America/Mexico_City',
];

// Configuración de email
$config['mail'] = [
    'driver' => $_ENV['MAIL_DRIVER'] ?? 'smtp',
    'host' => $_ENV['SMTP_HOST'] ?? 'localhost',
    'port' => (int) ($_ENV['SMTP_PORT'] ?? 587),
    'username' => $_ENV['SMTP_USERNAME'] ?? '',
    'password' => $_ENV['SMTP_PASSWORD'] ?? '',
    'encryption' => $_ENV['SMTP_ENCRYPTION'] ?? 'tls',
    'from' => [
        'address' => $_ENV['MAIL_FROM_ADDRESS'] ?? 'noreply@localhost',
        'name' => $_ENV['MAIL_FROM_NAME'] ?? 'Control de Gastos'
    ]
];

// Configuración de cache
$config['cache'] = [
    'driver' => $_ENV['CACHE_DRIVER'] ?? 'file',
    'path' => __DIR__ . '/../storage/cache',
    'redis' => [
        'host' => $_ENV['REDIS_HOST'] ?? '127.0.0.1',
        'port' => (int) ($_ENV['REDIS_PORT'] ?? 6379),
        'password' => $_ENV['REDIS_PASSWORD'] ?? null,
    ]
];

// Configuración de logs
$config['logging'] = [
    'level' => $_ENV['LOG_LEVEL'] ?? ($isProduction ? 'error' : 'debug'),
    'path' => $_ENV['LOG_FILE'] ?? __DIR__ . '/../logs/app.log',
    'max_files' => 30,
];

// Inicializar contenedor de dependencias
use ControlGastos\Core\Container;
use ControlGastos\Core\Database;
use ControlGastos\Core\Logger;
use ControlGastos\Core\Session;

$container = new Container();

// Registrar configuración
$container->set('config', function() use ($config) {
    return $config;
});

// Registrar servicios core
$container->set('database', function() use ($config) {
    return new Database($config['database']);
});

$container->set('logger', function() use ($config) {
    return new Logger($config['logging']);
});

$container->set('session', function() {
    return new Session();
});

// Registrar servicios de negocio
$container->set('userService', function() use ($container) {
    return new \ControlGastos\Services\UserService(
        $container->get('database'),
        $container->get('logger')
    );
});

$container->set('accountService', function() use ($container) {
    return new \ControlGastos\Services\AccountService(
        $container->get('database'),
        $container->get('logger')
    );
});

$container->set('transactionService', function() use ($container) {
    return new \ControlGastos\Services\TransactionService(
        $container->get('database'),
        $container->get('logger')
    );
});

$container->set('categoryService', function() use ($container) {
    return new \ControlGastos\Services\CategoryService(
        $container->get('database'),
        $container->get('logger')
    );
});

$container->set('reminderService', function() use ($container) {
    return new \ControlGastos\Services\ReminderService(
        $container->get('database'),
        $container->get('logger')
    );
});

$container->set('reportService', function() use ($container) {
    return new \ControlGastos\Services\ReportService(
        $container->get('database'),
        $container->get('logger')
    );
});

$container->set('advancedReportService', function() use ($container) {
    return new \ControlGastos\Services\AdvancedReportService(
        $container->get('database'),
        $container->get('logger')
    );
});

$container->set('emailService', function() use ($container, $config) {
    return new \ControlGastos\Services\EmailService(
        $container->get('logger'),
        $container->get('database'),
        $config['mail']
    );
});

$container->set('backupService', function() use ($container) {
    return new \ControlGastos\Services\BackupService(
        $container->get('database'),
        $container->get('logger')
    );
});

$container->set('securityService', function() use ($container) {
    return new \ControlGastos\Services\SecurityService(
        $container->get('database'),
        $container->get('logger')
    );
});

// Inicializar sesión
$container->get('session')->start();

// Configurar manejo global de errores para producción
if ($isProduction) {
    set_error_handler(function($severity, $message, $file, $line) use ($container) {
        $logger = $container->get('logger');
        $logger->error("PHP Error: $message in $file:$line");
    });

    set_exception_handler(function($exception) use ($container) {
        $logger = $container->get('logger');
        $logger->error("Uncaught Exception: " . $exception->getMessage(), [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        // Mostrar página de error genérica
        http_response_code(500);
        if (file_exists(__DIR__ . '/../templates/errors/500.php')) {
            include __DIR__ . '/../templates/errors/500.php';
        } else {
            echo "Error interno del servidor. Por favor, contacte al administrador.";
        }
        exit;
    });
}
