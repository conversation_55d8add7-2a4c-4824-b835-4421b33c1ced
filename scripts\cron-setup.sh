#!/bin/bash

# Script de configuración de Cron para Control de Gastos
# Este script configura las tareas automatizadas del sistema

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuración
PROJECT_PATH="/var/www/html/controlGastos"
PHP_PATH="/usr/bin/php"
LOG_PATH="/var/log/controlgastos"

echo -e "${BLUE}=== Configuración de Cron para Control de Gastos ===${NC}"

# Verificar si el script se ejecuta como root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}Error: Este script debe ejecutarse como root${NC}"
    echo "Uso: sudo $0"
    exit 1
fi

# Crear directorio de logs si no existe
if [ ! -d "$LOG_PATH" ]; then
    echo -e "${YELLOW}Creando directorio de logs: $LOG_PATH${NC}"
    mkdir -p "$LOG_PATH"
    chown www-data:www-data "$LOG_PATH"
    chmod 755 "$LOG_PATH"
fi

# Verificar que PHP existe
if [ ! -f "$PHP_PATH" ]; then
    echo -e "${RED}Error: PHP no encontrado en $PHP_PATH${NC}"
    echo "Por favor, ajusta la variable PHP_PATH en este script"
    exit 1
fi

# Verificar que el proyecto existe
if [ ! -d "$PROJECT_PATH" ]; then
    echo -e "${RED}Error: Proyecto no encontrado en $PROJECT_PATH${NC}"
    echo "Por favor, ajusta la variable PROJECT_PATH en este script"
    exit 1
fi

# Función para agregar tarea cron
add_cron_job() {
    local schedule="$1"
    local command="$2"
    local description="$3"
    local log_file="$4"
    
    echo -e "${YELLOW}Configurando: $description${NC}"
    
    # Crear entrada de cron
    local cron_entry="$schedule $command >> $LOG_PATH/$log_file 2>&1"
    
    # Verificar si ya existe
    if crontab -u www-data -l 2>/dev/null | grep -q "$command"; then
        echo -e "${YELLOW}  - Tarea ya existe, actualizando...${NC}"
        # Remover entrada existente
        crontab -u www-data -l 2>/dev/null | grep -v "$command" | crontab -u www-data -
    fi
    
    # Agregar nueva entrada
    (crontab -u www-data -l 2>/dev/null; echo "$cron_entry") | crontab -u www-data -
    echo -e "${GREEN}  - Configurado: $schedule${NC}"
}

echo -e "${BLUE}Configurando tareas cron...${NC}"

# 1. Procesar cola de emails (cada 5 minutos)
add_cron_job \
    "*/5 * * * *" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/ProcessEmailQueueCommand.php" \
    "Procesamiento de cola de emails" \
    "email-queue.log"

# 2. Enviar recordatorios diarios (8:00 AM)
add_cron_job \
    "0 8 * * *" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/SendRemindersCommand.php --type=daily" \
    "Envío de recordatorios diarios" \
    "daily-reminders.log"

# 3. Enviar recordatorios semanales (lunes 9:00 AM)
add_cron_job \
    "0 9 * * 1" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/SendRemindersCommand.php --type=weekly" \
    "Envío de recordatorios semanales" \
    "weekly-reminders.log"

# 4. Generar reportes mensuales (día 1 de cada mes, 10:00 AM)
add_cron_job \
    "0 10 1 * *" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/GenerateMonthlyReportsCommand.php" \
    "Generación de reportes mensuales" \
    "monthly-reports.log"

# 5. Backup automático diario (2:00 AM)
add_cron_job \
    "0 2 * * *" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/BackupCommand.php --type=daily" \
    "Backup automático diario" \
    "backup-daily.log"

# 6. Backup semanal completo (domingos 3:00 AM)
add_cron_job \
    "0 3 * * 0" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/BackupCommand.php --type=weekly --full" \
    "Backup semanal completo" \
    "backup-weekly.log"

# 7. Limpieza de logs antiguos (diario 4:00 AM)
add_cron_job \
    "0 4 * * *" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/CleanupCommand.php --logs --days=30" \
    "Limpieza de logs antiguos" \
    "cleanup.log"

# 8. Limpieza de sesiones expiradas (cada hora)
add_cron_job \
    "0 * * * *" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/CleanupCommand.php --sessions" \
    "Limpieza de sesiones expiradas" \
    "cleanup-sessions.log"

# 9. Verificación de salud del sistema (cada 15 minutos)
add_cron_job \
    "*/15 * * * *" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/HealthCheckCommand.php" \
    "Verificación de salud del sistema" \
    "health-check.log"

# 10. Actualización de estadísticas (cada 30 minutos)
add_cron_job \
    "*/30 * * * *" \
    "$PHP_PATH $PROJECT_PATH/src/Commands/UpdateStatsCommand.php" \
    "Actualización de estadísticas" \
    "update-stats.log"

echo -e "${BLUE}Configurando rotación de logs...${NC}"

# Crear configuración de logrotate
cat > /etc/logrotate.d/controlgastos << EOF
$LOG_PATH/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        # Reiniciar servicios si es necesario
        systemctl reload nginx > /dev/null 2>&1 || true
    endscript
}
EOF

echo -e "${GREEN}Configuración de logrotate creada${NC}"

echo -e "${BLUE}Configurando monitoreo...${NC}"

# Crear script de monitoreo
cat > "$PROJECT_PATH/scripts/monitor-cron.sh" << 'EOF'
#!/bin/bash

# Script de monitoreo de tareas cron
LOG_PATH="/var/log/controlgastos"
ALERT_EMAIL="<EMAIL>"

# Verificar logs de errores
check_errors() {
    local log_file="$1"
    local service_name="$2"
    
    if [ -f "$LOG_PATH/$log_file" ]; then
        # Buscar errores en las últimas 24 horas
        errors=$(grep -i "error\|fatal\|exception" "$LOG_PATH/$log_file" | tail -10)
        
        if [ ! -z "$errors" ]; then
            echo "ERRORES en $service_name:"
            echo "$errors"
            echo "---"
        fi
    fi
}

echo "=== Reporte de Monitoreo - $(date) ==="

check_errors "email-queue.log" "Cola de Emails"
check_errors "daily-reminders.log" "Recordatorios Diarios"
check_errors "monthly-reports.log" "Reportes Mensuales"
check_errors "backup-daily.log" "Backup Diario"
check_errors "health-check.log" "Verificación de Salud"

# Verificar espacio en disco
disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$disk_usage" -gt 85 ]; then
    echo "ALERTA: Espacio en disco al $disk_usage%"
fi

# Verificar memoria
memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$memory_usage" -gt 90 ]; then
    echo "ALERTA: Uso de memoria al $memory_usage%"
fi

echo "=== Fin del Reporte ==="
EOF

chmod +x "$PROJECT_PATH/scripts/monitor-cron.sh"

# Agregar monitoreo diario
add_cron_job \
    "0 6 * * *" \
    "$PROJECT_PATH/scripts/monitor-cron.sh" \
    "Monitoreo diario del sistema" \
    "monitor.log"

echo -e "${BLUE}Verificando configuración...${NC}"

# Mostrar crontab actual
echo -e "${YELLOW}Tareas cron configuradas para www-data:${NC}"
crontab -u www-data -l

echo -e "${BLUE}Creando script de gestión...${NC}"

# Crear script de gestión de cron
cat > "$PROJECT_PATH/scripts/manage-cron.sh" << 'EOF'
#!/bin/bash

# Script de gestión de tareas cron

case "$1" in
    "start")
        echo "Iniciando servicios cron..."
        systemctl start cron
        systemctl enable cron
        echo "Servicios iniciados"
        ;;
    "stop")
        echo "Deteniendo servicios cron..."
        systemctl stop cron
        echo "Servicios detenidos"
        ;;
    "restart")
        echo "Reiniciando servicios cron..."
        systemctl restart cron
        echo "Servicios reiniciados"
        ;;
    "status")
        echo "Estado de los servicios:"
        systemctl status cron
        echo ""
        echo "Tareas configuradas:"
        crontab -u www-data -l
        ;;
    "logs")
        echo "Logs recientes:"
        tail -50 /var/log/controlgastos/*.log
        ;;
    "test")
        echo "Ejecutando pruebas de comandos..."
        php /var/www/html/controlGastos/src/Commands/ProcessEmailQueueCommand.php --dry-run
        php /var/www/html/controlGastos/src/Commands/HealthCheckCommand.php
        ;;
    *)
        echo "Uso: $0 {start|stop|restart|status|logs|test}"
        echo ""
        echo "Comandos disponibles:"
        echo "  start   - Iniciar servicios cron"
        echo "  stop    - Detener servicios cron"
        echo "  restart - Reiniciar servicios cron"
        echo "  status  - Mostrar estado y configuración"
        echo "  logs    - Mostrar logs recientes"
        echo "  test    - Ejecutar pruebas de comandos"
        exit 1
        ;;
esac
EOF

chmod +x "$PROJECT_PATH/scripts/manage-cron.sh"

echo -e "${GREEN}=== Configuración Completada ===${NC}"
echo ""
echo -e "${YELLOW}Resumen de tareas configuradas:${NC}"
echo "• Cola de emails: cada 5 minutos"
echo "• Recordatorios diarios: 8:00 AM"
echo "• Recordatorios semanales: lunes 9:00 AM"
echo "• Reportes mensuales: día 1, 10:00 AM"
echo "• Backup diario: 2:00 AM"
echo "• Backup semanal: domingos 3:00 AM"
echo "• Limpieza de logs: 4:00 AM"
echo "• Limpieza de sesiones: cada hora"
echo "• Verificación de salud: cada 15 minutos"
echo "• Actualización de estadísticas: cada 30 minutos"
echo "• Monitoreo diario: 6:00 AM"
echo ""
echo -e "${YELLOW}Archivos creados:${NC}"
echo "• $LOG_PATH/ (directorio de logs)"
echo "• /etc/logrotate.d/controlgastos (rotación de logs)"
echo "• $PROJECT_PATH/scripts/monitor-cron.sh (monitoreo)"
echo "• $PROJECT_PATH/scripts/manage-cron.sh (gestión)"
echo ""
echo -e "${YELLOW}Comandos útiles:${NC}"
echo "• Ver tareas: crontab -u www-data -l"
echo "• Gestionar: $PROJECT_PATH/scripts/manage-cron.sh {start|stop|status|logs|test}"
echo "• Logs: tail -f $LOG_PATH/*.log"
echo ""
echo -e "${GREEN}¡Configuración de automatización completada!${NC}"
