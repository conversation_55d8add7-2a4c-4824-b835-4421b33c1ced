<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Backup y Seguridad</h2>
    <div>
        <button class="btn btn-primary" onclick="createBackup()">
            <i class="fas fa-plus"></i> Crear Backup
        </button>
        <button class="btn btn-outline-secondary" onclick="scheduleBackup()">
            <i class="fas fa-clock"></i> Programar Automático
        </button>
    </div>
</div>

<!-- Navegación -->
<div class="row mb-4">
    <div class="col-12">
        <div class="nav nav-pills justify-content-center" role="tablist">
            <a class="nav-link active" href="/backup">
                <i class="fas fa-database"></i> Backups
            </a>
            <a class="nav-link" href="/backup/audit-log">
                <i class="fas fa-list-alt"></i> Log de Auditoría
            </a>
            <a class="nav-link" href="/backup/security-analysis">
                <i class="fas fa-shield-alt"></i> Análisis de Seguridad
            </a>
        </div>
    </div>
</div>

<!-- Alertas de Seguridad -->
<?php if ($suspicious_activity && $suspicious_activity['has_suspicious_activity']): ?>
<div class="alert alert-warning mb-4">
    <h5><i class="fas fa-exclamation-triangle"></i> Actividad Sospechosa Detectada</h5>
    <ul class="mb-0">
        <?php foreach ($suspicious_activity['activities'] as $activity): ?>
            <li>
                <strong><?= htmlspecialchars($activity['description']) ?></strong>
                <span class="badge bg-<?= $activity['severity'] === 'high' ? 'danger' : ($activity['severity'] === 'medium' ? 'warning' : 'info') ?>">
                    <?= ucfirst($activity['severity']) ?>
                </span>
                <?php if (isset($activity['count'])): ?>
                    (<?= $activity['count'] ?> eventos)
                <?php endif; ?>
            </li>
        <?php endforeach; ?>
    </ul>
    <div class="mt-2">
        <a href="/backup/security-analysis" class="btn btn-sm btn-outline-warning">
            <i class="fas fa-search"></i> Ver Análisis Completo
        </a>
    </div>
</div>
<?php endif; ?>

<!-- Estadísticas de Backup -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">Total Backups</h5>
                <h3><?= count($backups) ?></h3>
                <small class="text-muted">Disponibles</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">Último Backup</h5>
                <h6>
                    <?php if (!empty($backups)): ?>
                        <?= $backups[0]['created_at_formatted'] ?>
                    <?php else: ?>
                        Nunca
                    <?php endif; ?>
                </h6>
                <small class="text-muted">
                    <?php if (!empty($backups)): ?>
                        <?= $backups[0]['age_days'] ?> días atrás
                    <?php endif; ?>
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Tamaño Total</h5>
                <h6>
                    <?php 
                    $totalSize = array_sum(array_column($backups, 'file_size'));
                    echo $this->formatFileSize($totalSize);
                    ?>
                </h6>
                <small class="text-muted">Espacio usado</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">Integridad</h5>
                <h6>
                    <?php 
                    $validBackups = array_filter($backups, fn($b) => $b['file_exists']);
                    echo count($validBackups) . '/' . count($backups);
                    ?>
                </h6>
                <small class="text-muted">Archivos válidos</small>
            </div>
        </div>
    </div>
</div>

<!-- Acciones Rápidas -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-tools"></i> Acciones Rápidas</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <button class="btn btn-outline-primary w-100 mb-2" onclick="createBackup('user')">
                    <i class="fas fa-user"></i> Backup de Mis Datos
                </button>
                <small class="text-muted">Crea una copia de seguridad solo de tus datos personales</small>
            </div>
            <div class="col-md-6">
                <button class="btn btn-outline-danger w-100 mb-2" onclick="cleanOldBackups()">
                    <i class="fas fa-trash"></i> Limpiar Backups Antiguos
                </button>
                <small class="text-muted">Elimina backups de más de 30 días</small>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Backups -->
<?php if (empty($backups)): ?>
    <div class="text-center py-5">
        <i class="fas fa-database fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No hay backups disponibles</h4>
        <p class="text-muted">Crea tu primer backup para proteger tus datos</p>
        <button class="btn btn-primary" onclick="createBackup()">
            <i class="fas fa-plus"></i> Crear Primer Backup
        </button>
    </div>
<?php else: ?>
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Lista de Backups
                <span class="badge bg-secondary"><?= count($backups) ?> backups</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Nombre</th>
                            <th>Tipo</th>
                            <th>Tamaño</th>
                            <th>Fecha</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($backups as $backup): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-archive me-2 text-primary"></i>
                                        <div>
                                            <strong><?= htmlspecialchars($backup['backup_name']) ?></strong>
                                            <br>
                                            <small class="text-muted">ID: <?= $backup['id'] ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge <?= $backup['backup_type'] === 'user' ? 'bg-info' : 'bg-primary' ?>">
                                        <?= $backup['backup_type'] === 'user' ? 'Usuario' : 'Sistema' ?>
                                    </span>
                                </td>
                                <td>
                                    <strong><?= $backup['file_size_formatted'] ?></strong>
                                </td>
                                <td>
                                    <div>
                                        <?= $backup['created_at_formatted'] ?>
                                        <br>
                                        <small class="text-muted">
                                            <?= $backup['age_days'] ?> días atrás
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($backup['file_exists']): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Disponible
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times"></i> Archivo no encontrado
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <?php if ($backup['file_exists']): ?>
                                                <li><a class="dropdown-item" href="#" onclick="verifyBackup(<?= $backup['id'] ?>)">
                                                    <i class="fas fa-check-circle"></i> Verificar Integridad
                                                </a></li>
                                                <li><a class="dropdown-item" href="/backup/<?= $backup['id'] ?>/download">
                                                    <i class="fas fa-download"></i> Descargar
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="restoreBackup(<?= $backup['id'] ?>)">
                                                    <i class="fas fa-undo"></i> Restaurar
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                            <?php endif; ?>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteBackup(<?= $backup['id'] ?>)">
                                                <i class="fas fa-trash"></i> Eliminar
                                            </a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Log de Auditoría Reciente -->
<?php if (!empty($audit_log)): ?>
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-history"></i> Actividad Reciente
            <a href="/backup/audit-log" class="btn btn-sm btn-outline-primary float-end">
                Ver Todo
            </a>
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Fecha</th>
                        <th>Usuario</th>
                        <th>Acción</th>
                        <th>Recurso</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (array_slice($audit_log, 0, 5) as $log): ?>
                        <tr>
                            <td>
                                <small><?= $log['created_at_formatted'] ?></small>
                            </td>
                            <td>
                                <small><?= htmlspecialchars($log['user_name'] ?? 'Sistema') ?></small>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?= htmlspecialchars($log['action']) ?></span>
                            </td>
                            <td>
                                <small><?= htmlspecialchars($log['resource']) ?></small>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Modales -->
<!-- Modal de Crear Backup -->
<div class="modal fade" id="createBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Crear Backup</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createBackupForm">
                    <div class="mb-3">
                        <label for="backupType" class="form-label">Tipo de Backup</label>
                        <select class="form-select" id="backupType" name="type">
                            <option value="user">Mis Datos Únicamente</option>
                            <option value="full">Sistema Completo</option>
                        </select>
                        <div class="form-text">
                            Los backups de usuario incluyen solo tus datos personales.
                            Los backups completos incluyen todos los datos del sistema.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateBackup()">
                    <i class="fas fa-database"></i> Crear Backup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Programar Backup -->
<div class="modal fade" id="scheduleBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Programar Backup Automático</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleBackupForm">
                    <div class="mb-3">
                        <label for="frequency" class="form-label">Frecuencia</label>
                        <select class="form-select" id="frequency" name="frequency">
                            <option value="daily">Diario</option>
                            <option value="weekly">Semanal</option>
                            <option value="monthly">Mensual</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="scheduleType" class="form-label">Tipo</label>
                        <select class="form-select" id="scheduleType" name="type">
                            <option value="user">Mis Datos</option>
                            <option value="full">Sistema Completo</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="submitScheduleBackup()">
                    <i class="fas fa-clock"></i> Programar
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function createBackup(type = null) {
    if (type) {
        document.getElementById('backupType').value = type;
        submitCreateBackup();
    } else {
        new bootstrap.Modal(document.getElementById('createBackupModal')).show();
    }
}

function submitCreateBackup() {
    const form = document.getElementById('createBackupForm');
    const formData = new FormData(form);
    
    // Mostrar loading
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creando...';
    button.disabled = true;
    
    fetch('/backup/create', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('createBackupModal')).hide();
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Error al crear backup');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function scheduleBackup() {
    new bootstrap.Modal(document.getElementById('scheduleBackupModal')).show();
}

function submitScheduleBackup() {
    const form = document.getElementById('scheduleBackupForm');
    const formData = new FormData(form);
    
    fetch('/backup/schedule', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        bootstrap.Modal.getInstance(document.getElementById('scheduleBackupModal')).hide();
        showAlert(data.success ? 'success' : 'error', data.message);
    })
    .catch(error => {
        showAlert('error', 'Error al programar backup');
    });
}

function restoreBackup(backupId) {
    if (confirm('¿Está seguro de que desea restaurar este backup? Esta acción sobrescribirá los datos actuales.')) {
        const formData = new FormData();
        formData.append('backup_id', backupId);
        
        fetch('/backup/restore', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            showAlert(data.success ? 'success' : 'error', data.message);
            if (data.success) {
                setTimeout(() => location.reload(), 2000);
            }
        })
        .catch(error => {
            showAlert('error', 'Error al restaurar backup');
        });
    }
}

function deleteBackup(backupId) {
    if (confirm('¿Está seguro de que desea eliminar este backup? Esta acción no se puede deshacer.')) {
        fetch(`/backup/${backupId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            showAlert(data.success ? 'success' : 'error', data.message);
            if (data.success) {
                setTimeout(() => location.reload(), 1000);
            }
        })
        .catch(error => {
            showAlert('error', 'Error al eliminar backup');
        });
    }
}

function verifyBackup(backupId) {
    fetch(`/backup/${backupId}/verify`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const status = data.is_valid ? 'válido' : 'corrupto';
            const alertType = data.is_valid ? 'success' : 'error';
            showAlert(alertType, `El backup está ${status}`);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Error al verificar backup');
    });
}

function cleanOldBackups() {
    if (confirm('¿Eliminar backups de más de 30 días?')) {
        const formData = new FormData();
        formData.append('days_to_keep', '30');
        
        fetch('/backup/clean', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            showAlert(data.success ? 'success' : 'error', data.message);
            if (data.success) {
                setTimeout(() => location.reload(), 1000);
            }
        })
        .catch(error => {
            showAlert('error', 'Error al limpiar backups');
        });
    }
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            bootstrap.Alert.getOrCreateInstance(alert).close();
        }
    }, 5000);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
