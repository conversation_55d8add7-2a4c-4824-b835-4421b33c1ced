<?php
$content = ob_start();
?>

<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Nueva Transacción</h1>
        <p class="text-muted mb-0">Registra un nuevo ingreso o gasto</p>
    </div>
    <div>
        <a href="/transactions" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i>
            <span class="d-none d-md-inline ms-1">Volver a Transacciones</span>
        </a>
    </div>
</div>

<!-- Transaction Form -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus text-primary me-2"></i>
                    Información de la Transacción
                </h5>
            </div>
            <div class="card-body">
                <form id="transactionForm" method="POST" action="/transactions/store">
                    <!-- Transaction Type -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <label class="form-label fw-semibold">Tipo de Transacción</label>
                            <div class="btn-group w-100" role="group" aria-label="Tipo de transacción">
                                <input type="radio" class="btn-check" name="type" id="typeIncome" value="income" required>
                                <label class="btn btn-outline-success" for="typeIncome">
                                    <i class="fas fa-arrow-up me-2"></i>
                                    Ingreso
                                </label>
                                
                                <input type="radio" class="btn-check" name="type" id="typeExpense" value="expense" required>
                                <label class="btn btn-outline-danger" for="typeExpense">
                                    <i class="fas fa-arrow-down me-2"></i>
                                    Gasto
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Amount and Date -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="amount" class="form-label">Monto *</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       step="0.01" min="0" required placeholder="0.00">
                            </div>
                            <div class="form-text">Ingrese el monto sin el signo de moneda</div>
                        </div>
                        <div class="col-md-6">
                            <label for="transaction_date" class="form-label">Fecha *</label>
                            <input type="date" class="form-control" id="transaction_date" name="transaction_date" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción *</label>
                        <input type="text" class="form-control" id="description" name="description" 
                               required placeholder="Ej: Compra en supermercado, Salario mensual, etc.">
                        <div class="form-text">Describe brevemente la transacción</div>
                    </div>

                    <!-- Account and Category -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="account_id" class="form-label">Cuenta *</label>
                            <select class="form-select" id="account_id" name="account_id" required>
                                <option value="">Seleccionar cuenta</option>
                                <?php if (!empty($accounts)): ?>
                                    <?php foreach ($accounts as $account): ?>
                                        <option value="<?= $account['id'] ?>" data-type="<?= $account['type'] ?>">
                                            <?= htmlspecialchars($account['name']) ?> 
                                            (<?= htmlspecialchars($account['type_label'] ?? $account['type']) ?>)
                                            - $<?= number_format($account['balance'], 2) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>No hay cuentas disponibles</option>
                                <?php endif; ?>
                            </select>
                            <div class="form-text">Cuenta desde/hacia donde se realiza la transacción</div>
                        </div>
                        <div class="col-md-6">
                            <label for="category_id" class="form-label">Categoría *</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Seleccionar categoría</option>
                                <!-- Se cargarán dinámicamente según el tipo -->
                            </select>
                            <div class="form-text">Categoría que mejor describe la transacción</div>
                        </div>
                    </div>

                    <!-- Subcategory -->
                    <div class="mb-3">
                        <label for="subcategory_id" class="form-label">Subcategoría (Opcional)</label>
                        <select class="form-select" id="subcategory_id" name="subcategory_id">
                            <option value="">Sin subcategoría</option>
                            <!-- Se cargarán dinámicamente según la categoría -->
                        </select>
                        <div class="form-text">Subcategoría para mayor detalle (opcional)</div>
                    </div>

                    <!-- Notes -->
                    <div class="mb-4">
                        <label for="notes" class="form-label">Notas Adicionales</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Información adicional sobre la transacción (opcional)"></textarea>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="/transactions" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Cancelar
                        </a>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="saveAsDraft()">
                                <i class="fas fa-save me-1"></i>Guardar Borrador
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-1"></i>Crear Transacción
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Info Sidebar -->
    <div class="col-lg-4">
        <!-- Account Balance Preview -->
        <div class="card mb-4">
            <div class="card-header bg-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    Balance de Cuenta
                </h6>
            </div>
            <div class="card-body">
                <div id="accountBalancePreview" class="text-center py-3">
                    <i class="fas fa-university fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">Selecciona una cuenta para ver el balance</p>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="card mb-4">
            <div class="card-header bg-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history text-secondary me-2"></i>
                    Transacciones Recientes
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_transactions)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                            <div class="list-group-item px-0 py-2 border-0">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1 small"><?= htmlspecialchars($transaction['description']) ?></h6>
                                        <small class="text-muted"><?= htmlspecialchars($transaction['category_name']) ?></small>
                                    </div>
                                    <span class="small <?= $transaction['type'] === 'income' ? 'text-success' : 'text-danger' ?>">
                                        <?= $transaction['type'] === 'income' ? '+' : '-' ?>$<?= number_format($transaction['amount'], 2) ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-exchange-alt fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0 small">No hay transacciones recientes</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Tips -->
        <div class="card">
            <div class="card-header bg-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb text-warning me-2"></i>
                    Consejos Rápidos
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        Usa descripciones claras y específicas
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        Selecciona la categoría más apropiada
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        Verifica la fecha de la transacción
                    </div>
                    <div class="mb-0">
                        <i class="fas fa-check text-success me-1"></i>
                        Revisa el balance de la cuenta
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeTransactionForm();
});

function initializeTransactionForm() {
    // Initialize form elements
    const typeInputs = document.querySelectorAll('input[name="type"]');
    const categorySelect = document.getElementById('category_id');
    const subcategorySelect = document.getElementById('subcategory_id');
    const accountSelect = document.getElementById('account_id');
    const amountInput = document.getElementById('amount');
    
    // Add event listeners
    typeInputs.forEach(input => {
        input.addEventListener('change', function() {
            loadCategoriesByType(this.value);
            updateFormStyling(this.value);
        });
    });
    
    categorySelect.addEventListener('change', function() {
        loadSubcategories(this.value);
    });
    
    accountSelect.addEventListener('change', function() {
        updateAccountBalance(this.value);
    });
    
    amountInput.addEventListener('input', function() {
        updateBalancePreview();
    });
    
    // Form submission
    document.getElementById('transactionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitTransaction();
    });
    
    // Initialize account balance if account is pre-selected
    if (accountSelect.value) {
        updateAccountBalance(accountSelect.value);
    }
}

async function loadCategoriesByType(type) {
    if (!type) {
        document.getElementById('category_id').innerHTML = '<option value="">Seleccionar categoría</option>';
        document.getElementById('subcategory_id').innerHTML = '<option value="">Sin subcategoría</option>';
        return;
    }

    try {
        const response = await fetch(`/api/categories?type=${type}`);
        const data = await response.json();
        
        if (data.success) {
            const categorySelect = document.getElementById('category_id');
            categorySelect.innerHTML = '<option value="">Seleccionar categoría</option>';
            
            data.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                option.dataset.color = category.color;
                option.dataset.icon = category.icon;
                categorySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        showToast('Error cargando categorías', 'error');
    }
}

async function loadSubcategories(categoryId) {
    const subcategorySelect = document.getElementById('subcategory_id');
    subcategorySelect.innerHTML = '<option value="">Sin subcategoría</option>';
    
    if (!categoryId) return;

    try {
        const response = await fetch(`/api/subcategories?category_id=${categoryId}`);
        const data = await response.json();
        
        if (data.success) {
            data.subcategories.forEach(subcategory => {
                const option = document.createElement('option');
                option.value = subcategory.id;
                option.textContent = subcategory.name;
                subcategorySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading subcategories:', error);
    }
}

function updateAccountBalance(accountId) {
    const accountSelect = document.getElementById('account_id');
    const selectedOption = accountSelect.querySelector(`option[value="${accountId}"]`);
    const balancePreview = document.getElementById('accountBalancePreview');
    
    if (!selectedOption || !accountId) {
        balancePreview.innerHTML = `
            <i class="fas fa-university fa-2x text-muted mb-2"></i>
            <p class="text-muted mb-0">Selecciona una cuenta para ver el balance</p>
        `;
        return;
    }
    
    const accountText = selectedOption.textContent;
    const balanceMatch = accountText.match(/\$([0-9,]+\.?\d*)/);
    const balance = balanceMatch ? balanceMatch[1] : '0.00';
    const accountName = accountText.split('(')[0].trim();
    const accountType = selectedOption.dataset.type || 'checking';
    
    const icon = getAccountIcon(accountType);
    const balanceClass = parseFloat(balance.replace(/,/g, '')) >= 0 ? 'text-success' : 'text-danger';
    
    balancePreview.innerHTML = `
        <i class="${icon} fa-2x text-primary mb-2"></i>
        <h6 class="mb-1">${accountName}</h6>
        <h5 class="mb-0 ${balanceClass}">$${balance}</h5>
        <small class="text-muted">Balance actual</small>
    `;
    
    updateBalancePreview();
}

function updateBalancePreview() {
    const accountSelect = document.getElementById('account_id');
    const amountInput = document.getElementById('amount');
    const typeInputs = document.querySelectorAll('input[name="type"]:checked');
    
    if (!accountSelect.value || !amountInput.value || typeInputs.length === 0) return;
    
    const selectedOption = accountSelect.querySelector(`option[value="${accountSelect.value}"]`);
    const accountText = selectedOption.textContent;
    const balanceMatch = accountText.match(/\$([0-9,]+\.?\d*)/);
    const currentBalance = balanceMatch ? parseFloat(balanceMatch[1].replace(/,/g, '')) : 0;
    
    const amount = parseFloat(amountInput.value) || 0;
    const type = typeInputs[0].value;
    
    const newBalance = type === 'income' ? currentBalance + amount : currentBalance - amount;
    const balanceClass = newBalance >= 0 ? 'text-success' : 'text-danger';
    
    const balancePreview = document.getElementById('accountBalancePreview');
    const currentBalanceElement = balancePreview.querySelector('h5');
    
    if (currentBalanceElement) {
        currentBalanceElement.innerHTML = `
            $${currentBalance.toLocaleString('es-AR', {minimumFractionDigits: 2})}
            <i class="fas fa-arrow-right mx-2 text-muted"></i>
            <span class="${balanceClass}">$${newBalance.toLocaleString('es-AR', {minimumFractionDigits: 2})}</span>
        `;
    }
}

function updateFormStyling(type) {
    const card = document.querySelector('.card');
    const submitBtn = document.querySelector('button[type="submit"]');
    
    // Remove existing classes
    card.classList.remove('border-success', 'border-danger');
    submitBtn.classList.remove('btn-success', 'btn-danger');
    
    // Add new classes based on type
    if (type === 'income') {
        card.classList.add('border-success');
        submitBtn.classList.add('btn-success');
        submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>Registrar Ingreso';
    } else if (type === 'expense') {
        card.classList.add('border-danger');
        submitBtn.classList.add('btn-danger');
        submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>Registrar Gasto';
    } else {
        submitBtn.classList.add('btn-primary');
        submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>Crear Transacción';
    }
}

function getAccountIcon(type) {
    const icons = {
        'checking': 'fas fa-university',
        'savings': 'fas fa-piggy-bank',
        'credit': 'fas fa-credit-card',
        'cash': 'fas fa-wallet',
        'investment': 'fas fa-chart-line'
    };
    return icons[type] || 'fas fa-university';
}

async function submitTransaction() {
    const form = document.getElementById('transactionForm');
    const formData = new FormData(form);
    
    // Show loading
    showLoading(true);
    
    try {
        const response = await fetch('/transactions/store', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            showToast('Transacción creada exitosamente', 'success');
            setTimeout(() => {
                window.location.href = '/transactions';
            }, 1500);
        } else {
            showToast(data.message || 'Error al crear la transacción', 'error');
        }
    } catch (error) {
        console.error('Error submitting transaction:', error);
        showToast('Error al crear la transacción', 'error');
    } finally {
        showLoading(false);
    }
}

function saveAsDraft() {
    const formData = new FormData(document.getElementById('transactionForm'));
    formData.append('is_draft', '1');
    
    // Save to localStorage as backup
    const draftData = {};
    for (let [key, value] of formData.entries()) {
        draftData[key] = value;
    }
    localStorage.setItem('transactionDraft', JSON.stringify(draftData));
    
    showToast('Borrador guardado localmente', 'info');
}

// Load draft on page load if exists
window.addEventListener('load', function() {
    const draft = localStorage.getItem('transactionDraft');
    if (draft) {
        try {
            const draftData = JSON.parse(draft);
            
            // Ask user if they want to load the draft
            if (confirm('Se encontró un borrador guardado. ¿Desea cargarlo?')) {
                Object.keys(draftData).forEach(key => {
                    const element = document.querySelector(`[name="${key}"]`);
                    if (element) {
                        if (element.type === 'radio') {
                            const radio = document.querySelector(`[name="${key}"][value="${draftData[key]}"]`);
                            if (radio) radio.checked = true;
                        } else {
                            element.value = draftData[key];
                        }
                    }
                });
                
                // Trigger change events
                const typeInput = document.querySelector('input[name="type"]:checked');
                if (typeInput) {
                    typeInput.dispatchEvent(new Event('change'));
                }
                
                const accountSelect = document.getElementById('account_id');
                if (accountSelect.value) {
                    accountSelect.dispatchEvent(new Event('change'));
                }
            }
        } catch (error) {
            console.error('Error loading draft:', error);
        }
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
