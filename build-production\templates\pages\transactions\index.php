<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Transacciones</h2>
    <div>
        <a href="/transactions/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nueva Transacción
        </a>
        <a href="/transactions/export<?= !empty($filters) ? '?' . http_build_query(array_filter($filters)) : '' ?>" class="btn btn-outline-secondary">
            <i class="fas fa-download"></i> Exportar
        </a>
    </div>
</div>

<!-- Estadísticas -->
<?php if (!empty($stats['general'])): ?>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">Total Transacciones</h5>
                <h3><?= number_format($stats['general']['total_transactions']) ?></h3>
                <small class="text-muted">
                    <?= $stats['general']['total_income_transactions'] ?> ingresos, 
                    <?= $stats['general']['total_expense_transactions'] ?> egresos
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">Ingresos</h5>
                <h3 class="text-success">+$ <?= number_format($stats['general']['total_income'], 2) ?></h3>
                <small class="text-muted">Promedio: $ <?= number_format($stats['general']['avg_income'], 2) ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">Egresos</h5>
                <h3 class="text-danger">-$ <?= number_format($stats['general']['total_expenses'], 2) ?></h3>
                <small class="text-muted">Promedio: $ <?= number_format($stats['general']['avg_expense'], 2) ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Balance Neto</h5>
                <h3 class="<?= $stats['general']['net_balance'] >= 0 ? 'text-success' : 'text-danger' ?>">
                    <?= $stats['general']['net_balance'] >= 0 ? '+' : '' ?>$ <?= number_format($stats['general']['net_balance'], 2) ?>
                </h3>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filtros -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter"></i> Filtros
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="/transactions" id="filtersForm">
            <div class="row">
                <div class="col-md-2">
                    <label for="type" class="form-label">Tipo</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">Todos</option>
                        <?php foreach ($transaction_types as $value => $label): ?>
                            <option value="<?= htmlspecialchars($value) ?>" 
                                    <?= ($filters['type'] ?? '') === $value ? 'selected' : '' ?>>
                                <?= htmlspecialchars($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="account_id" class="form-label">Cuenta</label>
                    <select class="form-select" id="account_id" name="account_id">
                        <option value="">Todas</option>
                        <?php foreach ($accounts as $account): ?>
                            <option value="<?= $account['id'] ?>" 
                                    <?= ($filters['account_id'] ?? '') == $account['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($account['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="category_id" class="form-label">Categoría</label>
                    <select class="form-select" id="category_id" name="category_id">
                        <option value="">Todas</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['id'] ?>" 
                                    <?= ($filters['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($category['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="start_date" class="form-label">Desde</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="<?= htmlspecialchars($filters['start_date'] ?? '') ?>">
                </div>
                <div class="col-md-2">
                    <label for="end_date" class="form-label">Hasta</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" 
                           value="<?= htmlspecialchars($filters['end_date'] ?? '') ?>">
                </div>
                <div class="col-md-2">
                    <label for="search" class="form-label">Buscar</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" 
                               placeholder="Descripción..." value="<?= htmlspecialchars($filters['search'] ?? '') ?>">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Aplicar Filtros
                    </button>
                    <a href="/transactions" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Limpiar
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Transacciones -->
<?php if (empty($transactions)): ?>
    <div class="text-center py-5">
        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No hay transacciones</h4>
        <p class="text-muted">
            <?php if (!empty(array_filter($filters))): ?>
                No se encontraron transacciones con los filtros aplicados
            <?php else: ?>
                Registra tu primera transacción para comenzar
            <?php endif; ?>
        </p>
        <a href="/transactions/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nueva Transacción
        </a>
    </div>
<?php else: ?>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Lista de Transacciones</h5>
            <span class="badge bg-secondary"><?= $pagination['total'] ?? 0 ?> transacciones</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Fecha</th>
                            <th>Tipo</th>
                            <th>Descripción</th>
                            <th>Categoría</th>
                            <th>Cuenta</th>
                            <th>Monto</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($transactions as $transaction): ?>
                            <tr>
                                <td>
                                    <small class="text-muted">
                                        <?= date('d/m/Y', strtotime($transaction['transaction_date'])) ?><br>
                                        <?= date('H:i', strtotime($transaction['transaction_date'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge <?= $transaction['type'] === 'income' ? 'bg-success' : 'bg-danger' ?>">
                                        <i class="fas fa-<?= $transaction['type'] === 'income' ? 'arrow-up' : 'arrow-down' ?>"></i>
                                        <?= $transaction['type_label'] ?>
                                    </span>
                                    <?php if ($transaction['is_recurring']): ?>
                                        <br><small class="text-info">
                                            <i class="fas fa-repeat"></i> Recurrente
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($transaction['description']) ?></strong>
                                    <?php if ($transaction['reference']): ?>
                                        <br><small class="text-muted">Ref: <?= htmlspecialchars($transaction['reference']) ?></small>
                                    <?php endif; ?>
                                    <?php if (!empty($transaction['tags_array'])): ?>
                                        <br>
                                        <?php foreach ($transaction['tags_array'] as $tag): ?>
                                            <span class="badge bg-light text-dark me-1">#<?= htmlspecialchars($tag) ?></span>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="<?= htmlspecialchars($transaction['category_icon'] ?? 'fas fa-folder') ?> me-2" 
                                           style="color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?>;"></i>
                                        <div>
                                            <div><?= htmlspecialchars($transaction['category_name']) ?></div>
                                            <?php if ($transaction['subcategory_name']): ?>
                                                <small class="text-muted"><?= htmlspecialchars($transaction['subcategory_name']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?= htmlspecialchars($transaction['account_name']) ?>
                                    </span>
                                </td>
                                <td>
                                    <strong class="<?= $transaction['type'] === 'income' ? 'text-success' : 'text-danger' ?>">
                                        <?= $transaction['amount_formatted'] ?>
                                    </strong>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="/transactions/<?= $transaction['id'] ?>">
                                                <i class="fas fa-eye"></i> Ver Detalles
                                            </a></li>
                                            <li><a class="dropdown-item" href="/transactions/<?= $transaction['id'] ?>/edit">
                                                <i class="fas fa-edit"></i> Editar
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteTransaction(<?= $transaction['id'] ?>)">
                                                <i class="fas fa-trash"></i> Eliminar
                                            </a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Paginación -->
        <?php if (($pagination['total_pages'] ?? 0) > 1): ?>
            <div class="card-footer">
                <nav aria-label="Paginación de transacciones">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($pagination['page'] > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $pagination['page'] - 1])) ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $pagination['page'] - 2); $i <= min($pagination['total_pages'], $pagination['page'] + 2); $i++): ?>
                            <li class="page-item <?= $i === $pagination['page'] ? 'active' : '' ?>">
                                <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $i])) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($pagination['page'] < $pagination['total_pages']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $pagination['page'] + 1])) ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <div class="text-center mt-2">
                    <small class="text-muted">
                        Página <?= $pagination['page'] ?> de <?= $pagination['total_pages'] ?> 
                        (<?= number_format($pagination['total']) ?> transacciones)
                    </small>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>

<script>
function deleteTransaction(transactionId) {
    if (confirm('¿Está seguro de que desea eliminar esta transacción?')) {
        fetch(`/transactions/${transactionId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': '<?= $csrf_token ?? '' ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Error al eliminar la transacción');
            }
        })
        .catch(error => {
            alert('Error al eliminar la transacción');
        });
    }
}

// Auto-submit del formulario de filtros cuando cambian los selects
document.addEventListener('DOMContentLoaded', function() {
    const selects = document.querySelectorAll('#filtersForm select, #filtersForm input[type="date"]');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            // Pequeño delay para permitir múltiples cambios
            setTimeout(() => {
                document.getElementById('filtersForm').submit();
            }, 100);
        });
    });
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
