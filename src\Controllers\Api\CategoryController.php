<?php

declare(strict_types=1);

namespace ControlGastos\Controllers\Api;

use ControlGastos\Core\Container;
use ControlGastos\Services\CategoryService;
use ControlGastos\Core\Session;

/**
 * Controlador API de categorías
 * Maneja endpoints REST para categorías y subcategorías
 */
class CategoryController
{
    private Container $container;
    private CategoryService $categoryService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->categoryService = $container->get('categoryService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * GET /api/v1/categories
     * Obtener todas las categorías del usuario
     */
    public function index(): void
    {
        try {
            $activeOnly = isset($_GET['active_only']) ? 
                filter_var($_GET['active_only'], FILTER_VALIDATE_BOOLEAN) : true;
            
            $includeSubcategories = isset($_GET['include_subcategories']) ? 
                filter_var($_GET['include_subcategories'], FILTER_VALIDATE_BOOLEAN) : true;

            $result = $this->categoryService->getUserCategories($this->userId, $activeOnly);

            if ($result['success']) {
                $categories = $result['categories'];

                // Filtrar subcategorías si no se requieren
                if (!$includeSubcategories) {
                    $categories = array_map(function($category) {
                        unset($category['subcategories']);
                        return $category;
                    }, $categories);
                }

                $this->jsonResponse([
                    'success' => true,
                    'data' => $categories,
                    'count' => count($categories)
                ]);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ], 500);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/categories/{id}
     * Obtener categoría específica
     */
    public function show(int $id): void
    {
        try {
            $result = $this->categoryService->getCategory($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['category']
                ]);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ], 404);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * POST /api/v1/categories
     * Crear nueva categoría
     */
    public function store(): void
    {
        try {
            $input = $this->getJsonInput();

            $data = [
                'name' => $input['name'] ?? '',
                'description' => $input['description'] ?? '',
                'color' => $input['color'] ?? '#007bff',
                'icon' => $input['icon'] ?? 'fas fa-folder'
            ];

            $result = $this->categoryService->createCategory($data, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['category']
                ], 201);
            } else {
                $statusCode = isset($result['errors']) ? 422 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * PUT /api/v1/categories/{id}
     * Actualizar categoría existente
     */
    public function update(int $id): void
    {
        try {
            $input = $this->getJsonInput();

            $data = [
                'name' => $input['name'] ?? '',
                'description' => $input['description'] ?? '',
                'color' => $input['color'] ?? '',
                'icon' => $input['icon'] ?? ''
            ];

            $result = $this->categoryService->updateCategory($id, $data, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['category']
                ]);
            } else {
                $statusCode = isset($result['errors']) ? 422 : 
                            ($result['message'] === 'Categoría no encontrada' ? 404 : 400);
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * DELETE /api/v1/categories/{id}
     * Eliminar categoría
     */
    public function delete(int $id): void
    {
        try {
            $result = $this->categoryService->deleteCategory($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message']
                ]);
            } else {
                $statusCode = $result['message'] === 'Categoría no encontrada' ? 404 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/categories/{id}/subcategories
     * Obtener subcategorías de una categoría
     */
    public function subcategories(int $id): void
    {
        try {
            $result = $this->categoryService->getSubcategoriesByCategory($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['subcategories'],
                    'count' => count($result['subcategories'])
                ]);
            } else {
                $statusCode = $result['message'] === 'Categoría no encontrada' ? 404 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * POST /api/v1/subcategories
     * Crear nueva subcategoría
     */
    public function storeSubcategory(): void
    {
        try {
            $input = $this->getJsonInput();

            $data = [
                'category_id' => (int) ($input['category_id'] ?? 0),
                'name' => $input['name'] ?? '',
                'description' => $input['description'] ?? ''
            ];

            $result = $this->categoryService->createSubcategory($data, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['subcategory']
                ], 201);
            } else {
                $statusCode = isset($result['errors']) ? 422 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * PUT /api/v1/subcategories/{id}
     * Actualizar subcategoría existente
     */
    public function updateSubcategory(int $id): void
    {
        try {
            $input = $this->getJsonInput();

            $data = [
                'category_id' => (int) ($input['category_id'] ?? 0),
                'name' => $input['name'] ?? '',
                'description' => $input['description'] ?? ''
            ];

            $result = $this->categoryService->updateSubcategory($id, $data, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['subcategory']
                ]);
            } else {
                $statusCode = isset($result['errors']) ? 422 : 
                            ($result['message'] === 'Subcategoría no encontrada' ? 404 : 400);
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * DELETE /api/v1/subcategories/{id}
     * Eliminar subcategoría
     */
    public function deleteSubcategory(int $id): void
    {
        try {
            $result = $this->categoryService->deleteSubcategory($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message']
                ]);
            } else {
                $statusCode = $result['message'] === 'Subcategoría no encontrada' ? 404 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * PATCH /api/v1/categories/{id}/toggle-status
     * Activar/Desactivar categoría
     */
    public function toggleStatus(int $id): void
    {
        try {
            $result = $this->categoryService->toggleCategoryStatus($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['category']
                ]);
            } else {
                $statusCode = $result['message'] === 'Categoría no encontrada' ? 404 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/categories/stats
     * Obtener estadísticas de categorías
     */
    public function stats(): void
    {
        try {
            $result = $this->categoryService->getCategoryStats($this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['stats']
                ]);
            } else {
                $this->jsonResponse($result, 500);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/categories/options
     * Obtener opciones para formularios (colores e iconos)
     */
    public function options(): void
    {
        $this->jsonResponse([
            'success' => true,
            'data' => [
                'colors' => $this->categoryService->getAvailableColors(),
                'icons' => $this->categoryService->getAvailableIcons()
            ]
        ]);
    }

    /**
     * Obtener input JSON del request
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('JSON inválido');
        }
        
        return $data ?: [];
    }

    /**
     * Enviar respuesta JSON
     */
    private function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
