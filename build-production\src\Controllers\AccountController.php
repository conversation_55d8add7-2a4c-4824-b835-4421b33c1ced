<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Services\AccountService;
use ControlGastos\Core\Session;

/**
 * Controlador de cuentas financieras
 * Maneja todas las rutas relacionadas con gestión de cuentas
 */
class AccountController
{
    private Container $container;
    private AccountService $accountService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->accountService = $container->get('accountService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Mostrar lista de cuentas
     */
    public function index(): string
    {
        $result = $this->accountService->getUserAccounts($this->userId);
        $summaryResult = $this->accountService->getFinancialSummary($this->userId);

        $data = [
            'title' => 'Mis Cuentas',
            'accounts' => $result['success'] ? $result['accounts'] : [],
            'summary' => $summaryResult['success'] ? $summaryResult['summary'] : [],
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('accounts/index', $data);
    }

    /**
     * Mostrar formulario de creación
     */
    public function create(): string
    {
        $data = [
            'title' => 'Nueva Cuenta',
            'account_types' => $this->accountService->getAccountTypes(),
            'currencies' => $this->accountService->getCurrencies(),
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('accounts/create', $data);
    }

    /**
     * Procesar creación de cuenta
     */
    public function store(): void
    {
        $data = [
            'name' => $_POST['name'] ?? '',
            'type' => $_POST['type'] ?? '',
            'currency' => $_POST['currency'] ?? 'COP',
            'description' => $_POST['description'] ?? '',
            'initial_balance' => $_POST['initial_balance'] ?? 0
        ];

        $result = $this->accountService->createAccount($data, $this->userId);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
            header('Location: /accounts');
            exit;
        }

        if (isset($result['errors'])) {
            $this->session->flash('error', 'Errores de validación');
            $this->session->flash('validation_errors', $result['errors']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /accounts/create');
        exit;
    }

    /**
     * Mostrar detalles de cuenta
     */
    public function show(int $id): string
    {
        $result = $this->accountService->getAccount($id, $this->userId);

        if (!$result['success']) {
            $this->session->flash('error', $result['message']);
            header('Location: /accounts');
            exit;
        }

        $data = [
            'title' => 'Detalles de Cuenta',
            'account' => $result['account'],
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('accounts/show', $data);
    }

    /**
     * Mostrar formulario de edición
     */
    public function edit(int $id): string
    {
        $result = $this->accountService->getAccount($id, $this->userId);

        if (!$result['success']) {
            $this->session->flash('error', $result['message']);
            header('Location: /accounts');
            exit;
        }

        $data = [
            'title' => 'Editar Cuenta',
            'account' => $result['account'],
            'account_types' => $this->accountService->getAccountTypes(),
            'currencies' => $this->accountService->getCurrencies(),
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error'),
            'validation_errors' => $this->session->getFlash('validation_errors', [])
        ];

        return $this->render('accounts/edit', $data);
    }

    /**
     * Procesar actualización de cuenta
     */
    public function update(int $id): void
    {
        $data = [
            'name' => $_POST['name'] ?? '',
            'type' => $_POST['type'] ?? '',
            'currency' => $_POST['currency'] ?? '',
            'description' => $_POST['description'] ?? ''
        ];

        $result = $this->accountService->updateAccount($id, $data, $this->userId);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
            header('Location: /accounts/' . $id);
            exit;
        }

        if (isset($result['errors'])) {
            $this->session->flash('error', 'Errores de validación');
            $this->session->flash('validation_errors', $result['errors']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /accounts/' . $id . '/edit');
        exit;
    }

    /**
     * Eliminar cuenta
     */
    public function delete(int $id): void
    {
        $result = $this->accountService->deleteAccount($id, $this->userId);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /accounts');
        exit;
    }

    /**
     * Activar/Desactivar cuenta
     */
    public function toggleStatus(int $id): void
    {
        $result = $this->accountService->toggleAccountStatus($id, $this->userId);

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /accounts');
        exit;
    }

    /**
     * Mostrar formulario de transferencia
     */
    public function showTransfer(): string
    {
        $accountsResult = $this->accountService->getUserAccounts($this->userId);

        $data = [
            'title' => 'Transferir entre Cuentas',
            'accounts' => $accountsResult['success'] ? $accountsResult['accounts'] : [],
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('accounts/transfer', $data);
    }

    /**
     * Procesar transferencia entre cuentas
     */
    public function transfer(): void
    {
        $fromAccountId = (int) ($_POST['from_account_id'] ?? 0);
        $toAccountId = (int) ($_POST['to_account_id'] ?? 0);
        $amount = (float) ($_POST['amount'] ?? 0);

        if ($fromAccountId === $toAccountId) {
            $this->session->flash('error', 'No puede transferir a la misma cuenta');
            header('Location: /accounts/transfer');
            exit;
        }

        $result = $this->accountService->transferBetweenAccounts(
            $fromAccountId, 
            $toAccountId, 
            $amount, 
            $this->userId
        );

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
            header('Location: /accounts');
            exit;
        }

        $this->session->flash('error', $result['message']);
        header('Location: /accounts/transfer');
        exit;
    }

    /**
     * API: Obtener balance de cuenta
     */
    public function getBalance(int $id): void
    {
        $result = $this->accountService->getAccount($id, $this->userId);

        header('Content-Type: application/json');
        
        if ($result['success']) {
            echo json_encode([
                'success' => true,
                'balance' => $result['account']['balance'],
                'balance_formatted' => $result['account']['balance_formatted']
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => $result['message']
            ]);
        }
        exit;
    }

    /**
     * API: Obtener resumen financiero
     */
    public function getSummary(): void
    {
        $result = $this->accountService->getFinancialSummary($this->userId);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Verificar si es una request AJAX
     */
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
