<?php

declare(strict_types=1);

namespace ControlGastos\Core;

use ControlGastos\Core\Container;
use ControlGastos\Exceptions\NotFoundException;
use ControlGastos\Exceptions\MethodNotAllowedException;
use Exception;

/**
 * Router de la aplicación
 * Maneja el enrutamiento de URLs y la ejecución de controladores
 */
class Router
{
    private array $routes = [];
    private array $middleware = [];
    private array $groupStack = [];

    /**
     * Registrar ruta GET
     */
    public function get(string $uri, $action): void
    {
        $this->addRoute('GET', $uri, $action);
    }

    /**
     * Registrar ruta POST
     */
    public function post(string $uri, $action): void
    {
        $this->addRoute('POST', $uri, $action);
    }

    /**
     * Registrar ruta PUT
     */
    public function put(string $uri, $action): void
    {
        $this->addRoute('PUT', $uri, $action);
    }

    /**
     * Registrar ruta DELETE
     */
    public function delete(string $uri, $action): void
    {
        $this->addRoute('DELETE', $uri, $action);
    }

    /**
     * Registrar ruta PATCH
     */
    public function patch(string $uri, $action): void
    {
        $this->addRoute('PATCH', $uri, $action);
    }

    /**
     * Agregar ruta al router
     */
    private function addRoute(string $method, string $uri, $action): void
    {
        $uri = $this->normalizeUri($uri);
        
        // Aplicar prefijo de grupo si existe
        if (!empty($this->groupStack)) {
            $group = end($this->groupStack);
            if (isset($group['prefix'])) {
                $uri = rtrim($group['prefix'], '/') . '/' . ltrim($uri, '/');
                $uri = $this->normalizeUri($uri);
            }
        }

        $route = [
            'method' => $method,
            'uri' => $uri,
            'action' => $action,
            'middleware' => $this->getRouteMiddleware()
        ];

        $this->routes[] = $route;
    }

    /**
     * Normalizar URI
     */
    private function normalizeUri(string $uri): string
    {
        $uri = trim($uri, '/');
        return $uri === '' ? '/' : '/' . $uri;
    }

    /**
     * Obtener middleware para la ruta actual
     */
    private function getRouteMiddleware(): array
    {
        $middleware = $this->middleware;
        
        // Agregar middleware de grupos
        foreach ($this->groupStack as $group) {
            if (isset($group['middleware'])) {
                $middleware = array_merge($middleware, $group['middleware']);
            }
        }
        
        return $middleware;
    }

    /**
     * Agregar middleware global
     */
    public function addMiddleware($middleware): void
    {
        $this->middleware[] = $middleware;
    }

    /**
     * Crear grupo de rutas
     */
    public function group(array $attributes, callable $callback): void
    {
        $this->groupStack[] = $attributes;
        $callback($this);
        array_pop($this->groupStack);
    }

    /**
     * Despachar request
     */
    public function dispatch(string $method, string $uri, Container $container)
    {
        $uri = $this->normalizeUri($uri);
        
        // Buscar ruta coincidente
        $route = $this->findRoute($method, $uri);
        
        if (!$route) {
            throw new NotFoundException("Ruta no encontrada: {$method} {$uri}");
        }

        // Ejecutar middleware
        $this->executeMiddleware($route['middleware'], $container);

        // Ejecutar acción
        return $this->executeAction($route['action'], $route['params'] ?? [], $container);
    }

    /**
     * Buscar ruta coincidente
     */
    private function findRoute(string $method, string $uri): ?array
    {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            $params = $this->matchUri($route['uri'], $uri);
            if ($params !== false) {
                $route['params'] = $params;
                return $route;
            }
        }

        // Verificar si existe la ruta con otro método
        foreach ($this->routes as $route) {
            if ($this->matchUri($route['uri'], $uri) !== false) {
                throw new MethodNotAllowedException("Método no permitido: {$method}");
            }
        }

        return null;
    }

    /**
     * Verificar si URI coincide con patrón
     */
    private function matchUri(string $pattern, string $uri)
    {
        // Convertir patrón a regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $uri, $matches)) {
            array_shift($matches); // Remover match completo
            return $matches;
        }

        return false;
    }

    /**
     * Ejecutar middleware
     */
    private function executeMiddleware(array $middleware, Container $container): void
    {
        foreach ($middleware as $middlewareInstance) {
            if (method_exists($middlewareInstance, 'handle')) {
                $middlewareInstance->handle($container);
            }
        }
    }

    /**
     * Ejecutar acción del controlador
     */
    private function executeAction($action, array $params, Container $container)
    {
        if (is_string($action)) {
            return $this->executeControllerAction($action, $params, $container);
        }

        if (is_callable($action)) {
            return call_user_func_array($action, $params);
        }

        throw new Exception("Acción de ruta inválida");
    }

    /**
     * Ejecutar acción de controlador
     */
    private function executeControllerAction(string $action, array $params, Container $container)
    {
        [$controllerName, $methodName] = explode('@', $action);
        
        $controllerClass = "ControlGastos\\Controllers\\{$controllerName}";
        
        if (!class_exists($controllerClass)) {
            throw new Exception("Controlador no encontrado: {$controllerClass}");
        }

        $controller = new $controllerClass($container);
        
        if (!method_exists($controller, $methodName)) {
            throw new Exception("Método no encontrado: {$controllerClass}::{$methodName}");
        }

        return call_user_func_array([$controller, $methodName], $params);
    }

    /**
     * Obtener todas las rutas registradas
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }

    /**
     * Generar URL para una ruta
     */
    public function url(string $name, array $params = []): string
    {
        // Implementación básica - se puede extender para rutas nombradas
        $url = $name;
        
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', (string) $value, $url);
        }
        
        return $url;
    }
}
