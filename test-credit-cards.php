<?php
/**
 * Test Suite Completo para Sistema de Tarjetas de Crédito
 * Verifica todas las rutas, funcionalidades y casos de uso
 */

require_once 'src/bootstrap.php';

class CreditCardTestSuite
{
    private $baseUrl = 'http://localhost/controlGastos/public/';
    private $testResults = [];
    private $testUserId = 1; // Usuario de prueba
    private $testCardId = null;
    private $db;
    
    public function __construct()
    {
        $container = require 'src/bootstrap.php';
        $this->db = $container->get('database')->getConnection();
        
        echo "🧪 INICIANDO TEST SUITE COMPLETO DE TARJETAS DE CRÉDITO\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
    }
    
    /**
     * Ejecutar todos los tests
     */
    public function runAllTests()
    {
        $this->testDatabaseStructure();
        $this->testModels();
        $this->testRoutes();
        $this->testCRUDOperations();
        $this->testBusinessLogic();
        $this->testTransactionsAndPayments();
        $this->testEdgeCases();
        $this->generateReport();
    }
    
    /**
     * Test 1: Verificar estructura de base de datos
     */
    public function testDatabaseStructure()
    {
        echo "📊 TEST 1: ESTRUCTURA DE BASE DE DATOS\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        // Verificar que existan las tablas
        $tables = ['credit_cards', 'credit_card_transactions', 'credit_card_payments', 'credit_card_statements'];
        
        foreach ($tables as $table) {
            try {
                $stmt = $this->db->query("DESCRIBE {$table}");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $this->addResult("✅ Tabla {$table} existe con " . count($columns) . " columnas", true);
            } catch (Exception $e) {
                $this->addResult("❌ Tabla {$table} no existe: " . $e->getMessage(), false);
            }
        }
        
        // Verificar columnas específicas de credit_cards
        try {
            $stmt = $this->db->query("DESCRIBE credit_cards");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $columnNames = array_column($columns, 'Field');
            
            $requiredColumns = ['id', 'user_id', 'card_name', 'bank_name', 'card_type', 'status', 'credit_limit', 'expiry_date'];
            
            foreach ($requiredColumns as $column) {
                if (in_array($column, $columnNames)) {
                    $this->addResult("✅ Columna {$column} existe en credit_cards", true);
                } else {
                    $this->addResult("❌ Columna {$column} falta en credit_cards", false);
                }
            }
        } catch (Exception $e) {
            $this->addResult("❌ Error verificando columnas: " . $e->getMessage(), false);
        }
        
        echo "\n";
    }
    
    /**
     * Test 2: Verificar modelos
     */
    public function testModels()
    {
        echo "🏗️ TEST 2: MODELOS Y CLASES\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        // Verificar que las clases existan
        $classes = [
            'ControlGastos\Models\CreditCard',
            'ControlGastos\Models\CreditCardTransaction',
            'ControlGastos\Models\CreditCardPayment',
            'ControlGastos\Controllers\CreditCardController'
        ];
        
        foreach ($classes as $class) {
            if (class_exists($class)) {
                $this->addResult("✅ Clase {$class} existe", true);
            } else {
                $this->addResult("❌ Clase {$class} no existe", false);
            }
        }
        
        // Test métodos del modelo CreditCard
        try {
            $creditCardModel = new \ControlGastos\Models\CreditCard($this->db);
            $methods = ['create', 'findById', 'findByUserId', 'update', 'changeStatus', 'getCurrentBalance'];
            
            foreach ($methods as $method) {
                if (method_exists($creditCardModel, $method)) {
                    $this->addResult("✅ Método CreditCard::{$method} existe", true);
                } else {
                    $this->addResult("❌ Método CreditCard::{$method} no existe", false);
                }
            }
        } catch (Exception $e) {
            $this->addResult("❌ Error instanciando CreditCard: " . $e->getMessage(), false);
        }
        
        echo "\n";
    }
    
    /**
     * Test 3: Verificar rutas HTTP
     */
    public function testRoutes()
    {
        echo "🛣️ TEST 3: RUTAS HTTP\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $routes = [
            'credit-cards' => 'Dashboard de tarjetas',
            'credit-cards/create' => 'Formulario de creación',
            'credit-cards/reports' => 'Reportes (requiere ID)'
        ];
        
        foreach ($routes as $route => $description) {
            $url = $this->baseUrl . "?route={$route}";
            
            try {
                $response = $this->makeHttpRequest($url);
                
                if ($response !== false && !empty($response)) {
                    // Verificar que no sea una página de error
                    if (strpos($response, 'Fatal error') === false && 
                        strpos($response, 'Parse error') === false) {
                        $this->addResult("✅ Ruta {$route} ({$description}) responde correctamente", true);
                    } else {
                        $this->addResult("❌ Ruta {$route} tiene errores PHP", false);
                    }
                } else {
                    $this->addResult("❌ Ruta {$route} no responde", false);
                }
            } catch (Exception $e) {
                $this->addResult("❌ Error en ruta {$route}: " . $e->getMessage(), false);
            }
        }
        
        echo "\n";
    }
    
    /**
     * Test 4: Operaciones CRUD
     */
    public function testCRUDOperations()
    {
        echo "💾 TEST 4: OPERACIONES CRUD\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        try {
            $creditCardModel = new \ControlGastos\Models\CreditCard($this->db);
            
            // CREATE - Crear tarjeta de prueba
            $testData = [
                'user_id' => $this->testUserId,
                'card_name' => 'Tarjeta Test',
                'bank_name' => 'Banco Test',
                'card_type' => 'visa',
                'credit_limit' => 10000.00,
                'expiry_date' => '2027-12-31',
                'cut_off_day' => 15,
                'payment_due_days' => 20,
                'description' => 'Tarjeta creada para testing'
            ];
            
            $this->testCardId = $creditCardModel->create($testData);
            
            if ($this->testCardId > 0) {
                $this->addResult("✅ CREATE: Tarjeta creada con ID {$this->testCardId}", true);
            } else {
                $this->addResult("❌ CREATE: Error creando tarjeta", false);
                return;
            }
            
            // READ - Leer tarjeta
            $card = $creditCardModel->findById($this->testCardId);
            if ($card && $card['card_name'] === 'Tarjeta Test') {
                $this->addResult("✅ READ: Tarjeta leída correctamente", true);
            } else {
                $this->addResult("❌ READ: Error leyendo tarjeta", false);
            }
            
            // UPDATE - Actualizar tarjeta
            $updateData = [
                'card_name' => 'Tarjeta Test Actualizada',
                'bank_name' => $testData['bank_name'],
                'card_type' => $testData['card_type'],
                'credit_limit' => 15000.00,
                'expiry_date' => $testData['expiry_date'],
                'cut_off_day' => $testData['cut_off_day'],
                'payment_due_days' => $testData['payment_due_days'],
                'description' => 'Tarjeta actualizada para testing'
            ];
            
            $updated = $creditCardModel->update($this->testCardId, $updateData);
            if ($updated) {
                $this->addResult("✅ UPDATE: Tarjeta actualizada correctamente", true);
            } else {
                $this->addResult("❌ UPDATE: Error actualizando tarjeta", false);
            }
            
            // Verificar actualización
            $updatedCard = $creditCardModel->findById($this->testCardId);
            if ($updatedCard && $updatedCard['card_name'] === 'Tarjeta Test Actualizada') {
                $this->addResult("✅ UPDATE: Verificación de actualización exitosa", true);
            } else {
                $this->addResult("❌ UPDATE: Verificación de actualización falló", false);
            }
            
        } catch (Exception $e) {
            $this->addResult("❌ CRUD: Error general: " . $e->getMessage(), false);
        }
        
        echo "\n";
    }
    
    /**
     * Hacer petición HTTP
     */
    private function makeHttpRequest($url)
    {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        return @file_get_contents($url, false, $context);
    }
    
    /**
     * Agregar resultado de test
     */
    private function addResult($message, $success)
    {
        $this->testResults[] = ['message' => $message, 'success' => $success];
        echo $message . "\n";
    }
    
    /**
     * Continuar con más tests...
     */
    public function testBusinessLogic()
    {
        echo "🧮 TEST 5: LÓGICA DE NEGOCIO\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        if (!$this->testCardId) {
            $this->addResult("❌ No hay tarjeta de prueba para testing de lógica", false);
            return;
        }
        
        try {
            $creditCardModel = new \ControlGastos\Models\CreditCard($this->db);
            
            // Test cálculo de saldo
            $balance = $creditCardModel->getCurrentBalance($this->testCardId);
            $this->addResult("✅ Cálculo de saldo actual: $" . number_format($balance, 2), true);
            
            // Test cupo disponible
            $availableCredit = $creditCardModel->getAvailableCredit($this->testCardId);
            $this->addResult("✅ Cálculo de cupo disponible: $" . number_format($availableCredit, 2), true);
            
            // Test cambio de estado
            $statusChanged = $creditCardModel->changeStatus($this->testCardId, 'blocked', 'Test de bloqueo');
            if ($statusChanged) {
                $this->addResult("✅ Cambio de estado a 'blocked' exitoso", true);
                
                // Verificar cambio
                $card = $creditCardModel->findById($this->testCardId);
                if ($card['status'] === 'blocked') {
                    $this->addResult("✅ Verificación de cambio de estado exitosa", true);
                } else {
                    $this->addResult("❌ Verificación de cambio de estado falló", false);
                }
                
                // Reactivar para otros tests
                $creditCardModel->changeStatus($this->testCardId, 'active', 'Reactivación para tests');
            } else {
                $this->addResult("❌ Error en cambio de estado", false);
            }
            
        } catch (Exception $e) {
            $this->addResult("❌ Error en lógica de negocio: " . $e->getMessage(), false);
        }
        
        echo "\n";
    }
    
    /**
     * Test transacciones y pagos
     */
    public function testTransactionsAndPayments()
    {
        echo "💳 TEST 7: TRANSACCIONES Y PAGOS\n";
        echo "-" . str_repeat("-", 40) . "\n";

        if (!$this->testCardId) {
            $this->addResult("❌ No hay tarjeta de prueba para testing de transacciones", false);
            return;
        }

        try {
            $transactionModel = new \ControlGastos\Models\CreditCardTransaction($this->db);
            $paymentModel = new \ControlGastos\Models\CreditCardPayment($this->db);

            // Test crear transacción
            $transactionData = [
                'credit_card_id' => $this->testCardId,
                'user_id' => $this->testUserId,
                'transaction_type' => 'purchase',
                'amount' => 100.00,
                'description' => 'Compra de prueba',
                'transaction_date' => date('Y-m-d'),
                'merchant' => 'Tienda Test'
            ];

            $transactionId = $transactionModel->create($transactionData);
            if ($transactionId > 0) {
                $this->addResult("✅ Transacción creada con ID {$transactionId}", true);

                // Verificar que afecte el saldo
                $creditCardModel = new \ControlGastos\Models\CreditCard($this->db);
                $newBalance = $creditCardModel->getCurrentBalance($this->testCardId);
                if ($newBalance == 100.00) {
                    $this->addResult("✅ Saldo actualizado correctamente después de transacción", true);
                } else {
                    $this->addResult("❌ Saldo no se actualizó correctamente: $" . $newBalance, false);
                }
            } else {
                $this->addResult("❌ Error creando transacción", false);
            }

            // Test crear pago
            $paymentData = [
                'credit_card_id' => $this->testCardId,
                'user_id' => $this->testUserId,
                'payment_type' => 'partial',
                'amount' => 50.00,
                'payment_date' => date('Y-m-d'),
                'payment_method' => 'bank_transfer'
            ];

            $paymentId = $paymentModel->create($paymentData);
            if ($paymentId > 0) {
                $this->addResult("✅ Pago creado con ID {$paymentId}", true);

                // Verificar que afecte el saldo
                $newBalance = $creditCardModel->getCurrentBalance($this->testCardId);
                if ($newBalance == 50.00) {
                    $this->addResult("✅ Saldo actualizado correctamente después de pago", true);
                } else {
                    $this->addResult("❌ Saldo no se actualizó correctamente después de pago: $" . $newBalance, false);
                }
            } else {
                $this->addResult("❌ Error creando pago", false);
            }

            // Test transacciones en cuotas
            $installmentData = [
                'credit_card_id' => $this->testCardId,
                'user_id' => $this->testUserId,
                'transaction_type' => 'purchase',
                'amount' => 300.00,
                'description' => 'Compra en cuotas',
                'transaction_date' => date('Y-m-d'),
                'installments' => 3
            ];

            $installmentIds = $transactionModel->createInstallmentTransactions($installmentData);
            if (count($installmentIds) == 3) {
                $this->addResult("✅ Transacción en 3 cuotas creada correctamente", true);
            } else {
                $this->addResult("❌ Error creando transacción en cuotas", false);
            }

        } catch (Exception $e) {
            $this->addResult("❌ Error en transacciones y pagos: " . $e->getMessage(), false);
        }

        echo "\n";
    }

    /**
     * Test casos extremos
     */
    public function testEdgeCases()
    {
        echo "⚠️ TEST 6: CASOS EXTREMOS\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        try {
            $creditCardModel = new \ControlGastos\Models\CreditCard($this->db);
            
            // Test tarjeta inexistente
            $nonExistentCard = $creditCardModel->findById(99999);
            if ($nonExistentCard === null || $nonExistentCard === false) {
                $this->addResult("✅ Manejo correcto de tarjeta inexistente", true);
            } else {
                $this->addResult("❌ No maneja correctamente tarjeta inexistente", false);
            }
            
            // Test verificación de tarjetas vencidas
            $expiredCount = $creditCardModel->checkAndUpdateExpiredCards();
            $this->addResult("✅ Verificación de tarjetas vencidas: {$expiredCount} tarjetas procesadas", true);
            
        } catch (Exception $e) {
            $this->addResult("❌ Error en casos extremos: " . $e->getMessage(), false);
        }
        
        echo "\n";
    }
    
    /**
     * Generar reporte final
     */
    public function generateReport()
    {
        echo "📋 REPORTE FINAL DE TESTING\n";
        echo "=" . str_repeat("=", 60) . "\n";
        
        $total = count($this->testResults);
        $passed = count(array_filter($this->testResults, fn($r) => $r['success']));
        $failed = $total - $passed;
        
        echo "Total de tests: {$total}\n";
        echo "✅ Exitosos: {$passed}\n";
        echo "❌ Fallidos: {$failed}\n";
        echo "📊 Porcentaje de éxito: " . round(($passed / $total) * 100, 2) . "%\n\n";
        
        if ($failed > 0) {
            echo "❌ TESTS FALLIDOS:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "   • " . $result['message'] . "\n";
                }
            }
        }
        
        // Cleanup - eliminar tarjeta de prueba
        if ($this->testCardId) {
            try {
                $this->db->exec("DELETE FROM credit_cards WHERE id = {$this->testCardId}");
                echo "\n🧹 Limpieza: Tarjeta de prueba eliminada\n";
            } catch (Exception $e) {
                echo "\n⚠️ No se pudo eliminar tarjeta de prueba: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n🎉 TESTING COMPLETADO\n";
    }
}

// Ejecutar tests
try {
    $testSuite = new CreditCardTestSuite();
    $testSuite->runAllTests();
} catch (Exception $e) {
    echo "❌ ERROR CRÍTICO EN TESTING: " . $e->getMessage() . "\n";
}
