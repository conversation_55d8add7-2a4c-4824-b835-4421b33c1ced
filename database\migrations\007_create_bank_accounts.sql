-- Migración 007: <PERSON><PERSON><PERSON> sistema de cuentas bancarias
-- Fecha: 2024-12-30
-- Descripción: Crear tablas para cuentas débito/bancarias y sus movimientos

-- Tabla principal de cuentas bancarias
CREATE TABLE bank_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    account_name VARCHAR(100) NOT NULL COMMENT 'Nombre personalizado de la cuenta',
    bank_name VARCHAR(100) NOT NULL COMMENT 'Nombre del banco o entidad',
    account_number VARCHAR(50) NULL COMMENT 'Número de cuenta bancaria',
    debit_card_number VARCHAR(20) NULL COMMENT 'Últimos 4 dígitos de tarjeta débito',
    account_type ENUM('savings', 'checking', 'business') NOT NULL DEFAULT 'savings' COMMENT 'Tipo de cuenta',
    initial_balance DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Saldo inicial al crear la cuenta',
    current_balance DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Saldo actual calculado',
    status ENUM('active', 'inactive', 'blocked', 'closed') NOT NULL DEFAULT 'active',
    description TEXT NULL COMMENT 'Descripción adicional',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_bank_name (bank_name),
    
    -- Restricciones
    CONSTRAINT fk_bank_accounts_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT = 'Cuentas bancarias y de débito de los usuarios';

-- Tabla de movimientos de cuentas bancarias
CREATE TABLE bank_account_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bank_account_id INT NOT NULL,
    user_id INT NOT NULL,
    movement_type ENUM('deposit', 'withdrawal', 'transfer_in', 'transfer_out', 'fee', 'interest') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description VARCHAR(255) NOT NULL,
    reference VARCHAR(100) NULL COMMENT 'Número de referencia o comprobante',
    movement_date DATE NOT NULL,
    posting_date DATE NOT NULL DEFAULT (CURDATE()),
    balance_after DECIMAL(15,2) NOT NULL COMMENT 'Saldo después del movimiento',
    related_account_id INT NULL COMMENT 'Cuenta relacionada en transferencias',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_bank_account_id (bank_account_id),
    INDEX idx_user_id (user_id),
    INDEX idx_movement_date (movement_date),
    INDEX idx_movement_type (movement_type),
    INDEX idx_posting_date (posting_date),
    
    -- Restricciones
    CONSTRAINT fk_movements_bank_account FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE CASCADE,
    CONSTRAINT fk_movements_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_movements_related_account FOREIGN KEY (related_account_id) REFERENCES bank_accounts(id) ON DELETE SET NULL
) COMMENT = 'Movimientos de las cuentas bancarias (depósitos, retiros, transferencias)';

-- Tabla para vincular transacciones de gastos con cuentas
CREATE TABLE expense_account_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id INT NOT NULL COMMENT 'ID de la transacción de gasto',
    bank_account_id INT NULL COMMENT 'Cuenta bancaria usada',
    credit_card_id INT NULL COMMENT 'Tarjeta de crédito usada',
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_bank_account_id (bank_account_id),
    INDEX idx_credit_card_id (credit_card_id),
    INDEX idx_user_id (user_id),
    
    -- Restricciones
    CONSTRAINT fk_expense_links_bank_account FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE SET NULL,
    CONSTRAINT fk_expense_links_credit_card FOREIGN KEY (credit_card_id) REFERENCES credit_cards(id) ON DELETE SET NULL,
    CONSTRAINT fk_expense_links_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Asegurar que se use solo una cuenta o tarjeta
    CONSTRAINT chk_one_payment_method CHECK (
        (bank_account_id IS NOT NULL AND credit_card_id IS NULL) OR 
        (bank_account_id IS NULL AND credit_card_id IS NOT NULL)
    )
) COMMENT = 'Vinculación de gastos con cuentas bancarias o tarjetas de crédito';

-- Actualizar tabla de pagos de tarjetas para incluir cuenta origen
ALTER TABLE credit_card_payments 
ADD COLUMN bank_account_id INT NULL COMMENT 'Cuenta bancaria desde donde se hizo el pago'
AFTER user_id;

ALTER TABLE credit_card_payments 
ADD CONSTRAINT fk_cc_payments_bank_account 
FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE SET NULL;

-- Crear índice para la nueva columna
ALTER TABLE credit_card_payments 
ADD INDEX idx_bank_account_id (bank_account_id);

-- Trigger para actualizar saldo de cuenta bancaria automáticamente
DELIMITER //

CREATE TRIGGER update_bank_account_balance_after_movement
AFTER INSERT ON bank_account_movements
FOR EACH ROW
BEGIN
    DECLARE new_balance DECIMAL(15,2);

    -- Calcular nuevo saldo basado en el tipo de movimiento
    IF NEW.movement_type IN ('deposit', 'transfer_in', 'interest') THEN
        SET new_balance = (SELECT current_balance FROM bank_accounts WHERE id = NEW.bank_account_id) + NEW.amount;
    ELSE
        SET new_balance = (SELECT current_balance FROM bank_accounts WHERE id = NEW.bank_account_id) - NEW.amount;
    END IF;

    -- Actualizar saldo de la cuenta
    UPDATE bank_accounts
    SET current_balance = new_balance, updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.bank_account_id;
END//

DELIMITER ;

-- Insertar datos de ejemplo para testing
INSERT INTO bank_accounts (user_id, account_name, bank_name, account_number, debit_card_number, account_type, initial_balance, current_balance) VALUES
(1, 'Cuenta Ahorros Principal', 'Banco Nacional', '**********', '5678', 'savings', 1000000.00, 1000000.00),
(1, 'Cuenta Corriente', 'Banco Internacional', '**********', '1234', 'checking', 500000.00, 500000.00),
(1, 'Cuenta Nómina', 'Banco Popular', '**********', '9876', 'savings', 200000.00, 200000.00);

-- Insertar algunos movimientos de ejemplo (sin trigger para evitar conflictos)
-- Los saldos se calcularán manualmente por ahora
INSERT INTO bank_account_movements (bank_account_id, user_id, movement_type, amount, description, movement_date, balance_after) VALUES
(1, 1, 'deposit', 100000.00, 'Consignación salario', CURDATE(), 1100000.00),
(2, 1, 'deposit', 75000.00, 'Transferencia recibida', CURDATE(), 575000.00),
(3, 1, 'deposit', 300000.00, 'Pago nómina', CURDATE(), 500000.00);

-- Actualizar saldos manualmente
UPDATE bank_accounts SET current_balance = 1100000.00 WHERE id = 1;
UPDATE bank_accounts SET current_balance = 575000.00 WHERE id = 2;
UPDATE bank_accounts SET current_balance = 500000.00 WHERE id = 3;
