<?php
/**
 * Debug de Vistas - Tarjetas de Crédito
 * Verifica qué está pasando con las vistas
 */

echo "🔍 DEBUG DE VISTAS - TARJETAS DE CRÉDITO\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Verificar archivos de vistas
echo "📁 VERIFICANDO ARCHIVOS DE VISTAS:\n";
echo "-" . str_repeat("-", 40) . "\n";

$viewsPath = __DIR__ . "/templates/pages/credit_cards/";
$views = ['index.php', 'create.php', 'show.php', 'edit.php', 'reports.php'];

foreach ($views as $view) {
    $fullPath = $viewsPath . $view;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        echo "✅ {$view} existe ({$size} bytes)\n";
    } else {
        echo "❌ {$view} NO EXISTE\n";
    }
}

echo "\n📊 VERIFICANDO DATOS EN BASE DE DATOS:\n";
echo "-" . str_repeat("-", 40) . "\n";

$config = [
    'host' => 'localhost',
    'dbname' => 'control_gastos',
    'username' => 'root',
    'password' => '',
];

try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4";
    $db = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // Verificar tarjetas
    $stmt = $db->query("SELECT COUNT(*) as count FROM credit_cards WHERE user_id = 1");
    $cardCount = $stmt->fetch()['count'];
    echo "💳 Tarjetas del usuario 1: {$cardCount}\n";
    
    // Verificar transacciones
    $stmt = $db->query("SELECT COUNT(*) as count FROM credit_card_transactions WHERE user_id = 1");
    $transactionCount = $stmt->fetch()['count'];
    echo "💰 Transacciones del usuario 1: {$transactionCount}\n";
    
    // Verificar pagos
    $stmt = $db->query("SELECT COUNT(*) as count FROM credit_card_payments WHERE user_id = 1");
    $paymentCount = $stmt->fetch()['count'];
    echo "💸 Pagos del usuario 1: {$paymentCount}\n";
    
} catch (Exception $e) {
    echo "❌ Error de BD: " . $e->getMessage() . "\n";
}

echo "\n🌐 SIMULANDO LLAMADA AL CONTROLADOR:\n";
echo "-" . str_repeat("-", 40) . "\n";

try {
    // Simular sesión
    session_start();
    $_SESSION['user_id'] = 1;
    
    // Incluir dependencias
    require_once 'src/bootstrap.php';
    $container = require 'src/bootstrap.php';
    
    // Crear controlador
    $creditCardController = new \ControlGastos\Controllers\CreditCardController(
        $container->get('database'),
        $container->get('session')
    );
    
    echo "✅ Controlador creado exitosamente\n";
    
    // Capturar output del método index
    ob_start();
    $creditCardController->index();
    $output = ob_get_clean();
    
    echo "📄 Output generado: " . strlen($output) . " caracteres\n";
    
    // Verificar contenido específico
    $checks = [
        'Mis Tarjetas de Crédito' => 'Título principal',
        'Nueva Tarjeta' => 'Botón crear',
        'bootstrap.min.css' => 'CSS Bootstrap',
        'Visa Principal' => 'Tarjeta de prueba',
        'navbar' => 'Barra de navegación'
    ];
    
    echo "\n🔍 VERIFICANDO CONTENIDO:\n";
    foreach ($checks as $needle => $description) {
        if (strpos($output, $needle) !== false) {
            echo "✅ {$description}: ENCONTRADO\n";
        } else {
            echo "❌ {$description}: NO ENCONTRADO\n";
        }
    }
    
    // Guardar output para inspección
    file_put_contents('debug_output.html', $output);
    echo "\n💾 Output guardado en debug_output.html\n";
    
} catch (Exception $e) {
    echo "❌ Error en controlador: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🔧 VERIFICANDO RUTAS DE ARCHIVOS:\n";
echo "-" . str_repeat("-", 40) . "\n";

$testPaths = [
    __DIR__ . "/templates/pages/credit_cards/index.php" => "Vista index",
    __DIR__ . "/public/assets/css/bootstrap.min.css" => "CSS Bootstrap",
    __DIR__ . "/public/assets/js/bootstrap.bundle.min.js" => "JS Bootstrap"
];

foreach ($testPaths as $path => $description) {
    if (file_exists($path)) {
        echo "✅ {$description}: {$path}\n";
    } else {
        echo "❌ {$description}: NO EXISTE - {$path}\n";
    }
}

echo "\n🎯 RECOMENDACIONES:\n";
echo "-" . str_repeat("-", 40) . "\n";

if (file_exists('debug_output.html')) {
    echo "1. Abre debug_output.html en tu navegador para ver el HTML generado\n";
    echo "2. Verifica que las rutas de CSS/JS sean correctas\n";
    echo "3. Asegúrate de estar autenticado como usuario ID 1\n";
} else {
    echo "1. Hay un error en el controlador que impide generar output\n";
    echo "2. Revisa los errores mostrados arriba\n";
    echo "3. Verifica que todas las dependencias estén correctas\n";
}

echo "\n🎉 DEBUG COMPLETADO\n";
