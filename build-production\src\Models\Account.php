<?php

declare(strict_types=1);

namespace ControlGastos\Models;

/**
 * Modelo de Cuenta Financiera
 * Representa una cuenta bancaria, efectivo, tarjeta de crédito, etc.
 */
class Account
{
    private ?int $id = null;
    private int $userId;
    private string $name;
    private string $type;
    private float $balance = 0.00;
    private string $currency = 'COP';
    private ?string $description = null;
    private bool $isActive = true;
    private \DateTime $createdAt;
    private \DateTime $updatedAt;

    // Tipos de cuenta disponibles
    public const TYPES = [
        'cash' => 'Efectivo',
        'bank' => 'Cuenta Bancaria',
        'credit_card' => 'Tarjeta de Crédito',
        'debit_card' => 'Tarjeta Débito',
        'savings' => 'Cuenta de Ahorros',
        'investment' => 'Inversión'
    ];

    // Monedas soportadas
    public const CURRENCIES = [
        'COP' => 'Peso Colombiano',
        'USD' => 'Dólar Estadounidense',
        'EUR' => 'Euro'
    ];

    public function __construct(array $data = [])
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    /**
     * Llenar modelo con datos
     */
    public function fill(array $data): void
    {
        if (isset($data['id'])) {
            $this->id = (int) $data['id'];
        }
        
        if (isset($data['user_id'])) {
            $this->userId = (int) $data['user_id'];
        }
        
        if (isset($data['name'])) {
            $this->name = trim($data['name']);
        }
        
        if (isset($data['type'])) {
            $this->type = $data['type'];
        }
        
        if (isset($data['balance'])) {
            $this->balance = (float) $data['balance'];
        }
        
        if (isset($data['currency'])) {
            $this->currency = $data['currency'];
        }
        
        if (isset($data['description'])) {
            $this->description = $data['description'] ? trim($data['description']) : null;
        }
        
        if (isset($data['is_active'])) {
            $this->isActive = (bool) $data['is_active'];
        }
        
        if (isset($data['created_at'])) {
            $this->createdAt = new \DateTime($data['created_at']);
        }
        
        if (isset($data['updated_at'])) {
            $this->updatedAt = new \DateTime($data['updated_at']);
        }
    }

    /**
     * Convertir a array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->userId,
            'name' => $this->name,
            'type' => $this->type,
            'balance' => $this->balance,
            'currency' => $this->currency,
            'description' => $this->description,
            'is_active' => $this->isActive,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Convertir a array con información adicional
     */
    public function toDetailedArray(): array
    {
        return array_merge($this->toArray(), [
            'type_label' => self::TYPES[$this->type] ?? $this->type,
            'currency_label' => self::CURRENCIES[$this->currency] ?? $this->currency,
            'balance_formatted' => $this->getFormattedBalance(),
            'status_label' => $this->isActive ? 'Activa' : 'Inactiva'
        ]);
    }

    /**
     * Validar datos de la cuenta
     */
    public function validate(): array
    {
        $errors = [];

        // Validar nombre
        if (empty($this->name)) {
            $errors['name'] = 'El nombre de la cuenta es requerido';
        } elseif (strlen($this->name) < 2) {
            $errors['name'] = 'El nombre debe tener al menos 2 caracteres';
        } elseif (strlen($this->name) > 100) {
            $errors['name'] = 'El nombre no puede exceder 100 caracteres';
        }

        // Validar tipo
        if (empty($this->type)) {
            $errors['type'] = 'El tipo de cuenta es requerido';
        } elseif (!array_key_exists($this->type, self::TYPES)) {
            $errors['type'] = 'Tipo de cuenta inválido';
        }

        // Validar moneda
        if (empty($this->currency)) {
            $errors['currency'] = 'La moneda es requerida';
        } elseif (!array_key_exists($this->currency, self::CURRENCIES)) {
            $errors['currency'] = 'Moneda inválida';
        }

        // Validar balance
        if (!is_numeric($this->balance)) {
            $errors['balance'] = 'El balance debe ser un número válido';
        }

        // Validar descripción
        if ($this->description && strlen($this->description) > 500) {
            $errors['description'] = 'La descripción no puede exceder 500 caracteres';
        }

        // Validar user_id
        if (empty($this->userId)) {
            $errors['user_id'] = 'El ID de usuario es requerido';
        }

        return $errors;
    }

    /**
     * Verificar si la cuenta es válida
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }

    /**
     * Obtener balance formateado
     */
    public function getFormattedBalance(): string
    {
        $symbol = $this->getCurrencySymbol();
        return $symbol . ' ' . number_format($this->balance, 2, '.', ',');
    }

    /**
     * Obtener símbolo de moneda
     */
    public function getCurrencySymbol(): string
    {
        return match($this->currency) {
            'COP' => '$',
            'USD' => 'US$',
            'EUR' => '€',
            default => $this->currency
        };
    }

    /**
     * Actualizar balance
     */
    public function updateBalance(float $amount): void
    {
        $this->balance += $amount;
        $this->updatedAt = new \DateTime();
    }

    /**
     * Establecer balance
     */
    public function setBalance(float $balance): void
    {
        $this->balance = $balance;
        $this->updatedAt = new \DateTime();
    }

    /**
     * Verificar si tiene saldo suficiente
     */
    public function hasSufficientBalance(float $amount): bool
    {
        // Para tarjetas de crédito, permitir saldo negativo
        if ($this->type === 'credit_card') {
            return true;
        }
        
        return $this->balance >= $amount;
    }

    /**
     * Activar cuenta
     */
    public function activate(): void
    {
        $this->isActive = true;
        $this->updatedAt = new \DateTime();
    }

    /**
     * Desactivar cuenta
     */
    public function deactivate(): void
    {
        $this->isActive = false;
        $this->updatedAt = new \DateTime();
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getUserId(): int { return $this->userId; }
    public function getName(): string { return $this->name; }
    public function getType(): string { return $this->type; }
    public function getBalance(): float { return $this->balance; }
    public function getCurrency(): string { return $this->currency; }
    public function getDescription(): ?string { return $this->description; }
    public function isActive(): bool { return $this->isActive; }
    public function getCreatedAt(): \DateTime { return $this->createdAt; }
    public function getUpdatedAt(): \DateTime { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setUserId(int $userId): void { $this->userId = $userId; }
    public function setName(string $name): void { $this->name = trim($name); $this->updatedAt = new \DateTime(); }
    public function setType(string $type): void { $this->type = $type; $this->updatedAt = new \DateTime(); }
    public function setCurrency(string $currency): void { $this->currency = $currency; $this->updatedAt = new \DateTime(); }
    public function setDescription(?string $description): void { $this->description = $description ? trim($description) : null; $this->updatedAt = new \DateTime(); }
    public function setIsActive(bool $isActive): void { $this->isActive = $isActive; $this->updatedAt = new \DateTime(); }
    public function setUpdatedAt(\DateTime $updatedAt): void { $this->updatedAt = $updatedAt; }

    /**
     * Obtener tipo de cuenta formateado
     */
    public function getTypeLabel(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * Obtener etiqueta de moneda
     */
    public function getCurrencyLabel(): string
    {
        return self::CURRENCIES[$this->currency] ?? $this->currency;
    }

    /**
     * Verificar si es una cuenta de crédito
     */
    public function isCreditAccount(): bool
    {
        return $this->type === 'credit_card';
    }

    /**
     * Verificar si es una cuenta de efectivo
     */
    public function isCashAccount(): bool
    {
        return $this->type === 'cash';
    }

    /**
     * Verificar si es una cuenta bancaria
     */
    public function isBankAccount(): bool
    {
        return in_array($this->type, ['bank', 'savings', 'debit_card']);
    }
}
