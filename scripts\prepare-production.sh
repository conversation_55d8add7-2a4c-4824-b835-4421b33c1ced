#!/bin/bash

# Script de preparación para producción
# Ejecutar antes de subir a cPanel

echo "🚀 Preparando aplicación para producción..."

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Crear directorio de build
BUILD_DIR="build-production"
echo -e "${YELLOW}Creando directorio de build...${NC}"
rm -rf $BUILD_DIR
mkdir -p $BUILD_DIR

# Copiar archivos necesarios
echo -e "${YELLOW}Copiando archivos del proyecto...${NC}"
cp -r src/ $BUILD_DIR/
cp -r templates/ $BUILD_DIR/
cp -r public/ $BUILD_DIR/
cp -r database/ $BUILD_DIR/
cp -r config/ $BUILD_DIR/
cp -r scripts/ $BUILD_DIR/

# Crear estructura de directorios para cPanel
echo -e "${YELLOW}Creando estructura para cPanel...${NC}"
mkdir -p $BUILD_DIR/logs
mkdir -p $BUILD_DIR/storage/cache
mkdir -p $BUILD_DIR/storage/sessions
mkdir -p $BUILD_DIR/storage/uploads
mkdir -p $BUILD_DIR/storage/backups

# Crear archivo .htaccess para public
echo -e "${YELLOW}Creando .htaccess...${NC}"
cat > $BUILD_DIR/public/.htaccess << 'EOF'
# Control de Gastos - .htaccess para producción

# Habilitar rewrite engine
RewriteEngine On

# Redirigir todo a index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Seguridad
# Prevenir acceso a archivos sensibles
<FilesMatch "\.(env|log|sql|md|txt)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevenir acceso a directorios
Options -Indexes

# Headers de seguridad
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compresión GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache de archivos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
EOF

# Crear .htaccess para proteger directorios
echo -e "${YELLOW}Protegiendo directorios sensibles...${NC}"

# Proteger src/
cat > $BUILD_DIR/src/.htaccess << 'EOF'
Order allow,deny
Deny from all
EOF

# Proteger config/
cat > $BUILD_DIR/config/.htaccess << 'EOF'
Order allow,deny
Deny from all
EOF

# Proteger logs/
cat > $BUILD_DIR/logs/.htaccess << 'EOF'
Order allow,deny
Deny from all
EOF

# Proteger storage/
cat > $BUILD_DIR/storage/.htaccess << 'EOF'
Order allow,deny
Deny from all
EOF

# Crear archivo de configuración de producción
echo -e "${YELLOW}Creando configuración de producción...${NC}"
cat > $BUILD_DIR/.env.production << 'EOF'
# Configuración de Producción - Control de Gastos
# IMPORTANTE: Cambiar todos los valores por los reales

# Entorno
APP_ENV=production
APP_DEBUG=false
APP_URL=https://tudominio.com

# Base de datos
DB_HOST=localhost
DB_NAME=tu_base_datos
DB_USER=tu_usuario
DB_PASS=tu_password

# Seguridad
APP_KEY=CAMBIAR_POR_CLAVE_SEGURA_32_CARACTERES
ENCRYPTION_KEY=CAMBIAR_POR_CLAVE_CIFRADO_32_CHARS

# Email SMTP
SMTP_HOST=mail.tudominio.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=tu_password_email
SMTP_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Control de Gastos"

# Cache (si tienes Redis disponible)
CACHE_DRIVER=file
REDIS_HOST=127.0.0.1
REDIS_PORT=6379

# Logs
LOG_LEVEL=error
LOG_FILE=/home/<USER>/logs/app.log

# Backup
BACKUP_PATH=/home/<USER>/backups/
BACKUP_ENCRYPTION_KEY=CAMBIAR_POR_CLAVE_BACKUP_32_CHARS
EOF

# Crear script de instalación para cPanel
echo -e "${YELLOW}Creando script de instalación...${NC}"
cat > $BUILD_DIR/install-cpanel.php << 'EOF'
<?php
/**
 * Script de instalación para cPanel
 * Ejecutar una sola vez después de subir archivos
 */

echo "<h1>🚀 Instalación Control de Gastos</h1>";

// Verificar PHP
if (version_compare(PHP_VERSION, '8.0.0') < 0) {
    die("❌ Error: Se requiere PHP 8.0 o superior. Versión actual: " . PHP_VERSION);
}

echo "✅ PHP Version: " . PHP_VERSION . "<br>";

// Verificar extensiones
$required_extensions = ['pdo', 'pdo_mysql', 'openssl', 'mbstring', 'json'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        die("❌ Error: Extensión PHP requerida no encontrada: $ext");
    }
    echo "✅ Extensión $ext: OK<br>";
}

// Crear directorios con permisos
$directories = [
    'logs',
    'storage/cache',
    'storage/sessions',
    'storage/uploads',
    'storage/backups'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Directorio creado: $dir<br>";
    }
    
    if (!is_writable($dir)) {
        chmod($dir, 0755);
        echo "⚠️ Permisos ajustados: $dir<br>";
    }
}

// Verificar archivo .env
if (!file_exists('.env')) {
    if (file_exists('.env.production')) {
        echo "⚠️ Renombra .env.production a .env y configura los valores<br>";
    } else {
        echo "❌ Error: Archivo .env no encontrado<br>";
    }
} else {
    echo "✅ Archivo .env encontrado<br>";
}

// Verificar conexión a base de datos
try {
    if (file_exists('.env')) {
        $env = parse_ini_file('.env');
        $pdo = new PDO(
            "mysql:host={$env['DB_HOST']};dbname={$env['DB_NAME']}",
            $env['DB_USER'],
            $env['DB_PASS']
        );
        echo "✅ Conexión a base de datos: OK<br>";
    }
} catch (Exception $e) {
    echo "❌ Error de base de datos: " . $e->getMessage() . "<br>";
}

echo "<h2>📋 Próximos pasos:</h2>";
echo "<ol>";
echo "<li>Renombra .env.production a .env</li>";
echo "<li>Configura los valores en .env</li>";
echo "<li>Ejecuta las migraciones de base de datos</li>";
echo "<li>Elimina este archivo (install-cpanel.php)</li>";
echo "</ol>";

echo "<h2>🔧 Comandos útiles:</h2>";
echo "<p><strong>Ejecutar migraciones:</strong><br>";
echo "<code>php database/run-migrations.php</code></p>";

echo "<p><strong>Crear usuario admin:</strong><br>";
echo "<code>php scripts/create-admin.php</code></p>";
?>
EOF

# Crear script para ejecutar migraciones
echo -e "${YELLOW}Creando script de migraciones...${NC}"
cat > $BUILD_DIR/database/run-migrations.php << 'EOF'
<?php
/**
 * Ejecutor de migraciones para cPanel
 */

require_once '../src/bootstrap.php';

echo "<h1>📊 Ejecutando Migraciones</h1>";

try {
    $database = $container->get('database');
    
    // Leer archivos de migración
    $migration_files = glob(__DIR__ . '/migrations/*.sql');
    sort($migration_files);
    
    foreach ($migration_files as $file) {
        $filename = basename($file);
        echo "Ejecutando: $filename<br>";
        
        $sql = file_get_contents($file);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $database->execute($statement);
            }
        }
        
        echo "✅ $filename completado<br>";
    }
    
    echo "<h2>✅ Todas las migraciones ejecutadas correctamente</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error ejecutando migraciones:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
EOF

# Crear script para crear usuario admin
echo -e "${YELLOW}Creando script de usuario admin...${NC}"
cat > $BUILD_DIR/scripts/create-admin.php << 'EOF'
<?php
/**
 * Crear usuario administrador
 */

require_once '../src/bootstrap.php';

echo "<h1>👤 Crear Usuario Administrador</h1>";

if ($_POST) {
    try {
        $userService = $container->get('userService');
        
        $userData = [
            'first_name' => $_POST['first_name'],
            'last_name' => $_POST['last_name'],
            'email' => $_POST['email'],
            'password' => $_POST['password'],
            'role' => 'admin',
            'is_verified' => true
        ];
        
        $userId = $userService->createUser($userData);
        
        echo "<h2>✅ Usuario administrador creado exitosamente</h2>";
        echo "<p>ID: $userId</p>";
        echo "<p>Email: {$userData['email']}</p>";
        echo "<p><a href='../public/'>Ir a la aplicación</a></p>";
        
    } catch (Exception $e) {
        echo "<h2>❌ Error:</h2>";
        echo "<p>" . $e->getMessage() . "</p>";
    }
} else {
?>
<form method="POST">
    <table>
        <tr>
            <td>Nombre:</td>
            <td><input type="text" name="first_name" required></td>
        </tr>
        <tr>
            <td>Apellido:</td>
            <td><input type="text" name="last_name" required></td>
        </tr>
        <tr>
            <td>Email:</td>
            <td><input type="email" name="email" required></td>
        </tr>
        <tr>
            <td>Contraseña:</td>
            <td><input type="password" name="password" required minlength="8"></td>
        </tr>
        <tr>
            <td colspan="2">
                <button type="submit">Crear Administrador</button>
            </td>
        </tr>
    </table>
</form>
<?php } ?>
EOF

# Crear archivo de información
echo -e "${YELLOW}Creando archivo README...${NC}"
cat > $BUILD_DIR/README-PRODUCCION.md << 'EOF'
# 🚀 Control de Gastos - Instalación en Producción

## 📋 Requisitos del Servidor

- PHP 8.0 o superior
- MySQL 5.7 o superior
- Extensiones PHP: PDO, PDO_MySQL, OpenSSL, mbstring, JSON
- Espacio en disco: mínimo 100MB
- Memoria PHP: mínimo 128MB

## 🔧 Pasos de Instalación

### 1. Subir Archivos
- Sube todos los archivos a tu hosting
- El contenido de `public/` debe ir en `public_html/`
- Los demás directorios van en el directorio raíz de tu cuenta

### 2. Configurar Base de Datos
- Crea una base de datos MySQL en cPanel
- Anota: nombre de BD, usuario, contraseña

### 3. Configurar Aplicación
- Renombra `.env.production` a `.env`
- Edita `.env` con tus datos reales
- Genera claves seguras para APP_KEY y ENCRYPTION_KEY

### 4. Ejecutar Instalación
- Visita: `tudominio.com/install-cpanel.php`
- Sigue las instrucciones
- Ejecuta migraciones: `tudominio.com/database/run-migrations.php`
- Crea admin: `tudominio.com/scripts/create-admin.php`

### 5. Seguridad Post-Instalación
- Elimina `install-cpanel.php`
- Elimina `database/run-migrations.php`
- Elimina `scripts/create-admin.php`
- Verifica permisos de directorios

## 🔒 Configuración de Seguridad

### Permisos de Archivos
```
Directorios: 755
Archivos PHP: 644
.env: 600
logs/: 755 (writable)
storage/: 755 (writable)
```

### Variables de Entorno Importantes
- `APP_KEY`: Clave de 32 caracteres para cifrado
- `DB_*`: Credenciales de base de datos
- `SMTP_*`: Configuración de email

## 📧 Configuración de Email

Para que funcionen los emails:
1. Configura SMTP en cPanel
2. Actualiza variables SMTP_* en .env
3. Verifica que el puerto 587 esté abierto

## 🔄 Mantenimiento

### Backup Automático
- Configura cron jobs para backup
- Ruta: `/scripts/cron-setup.sh`

### Logs
- Revisa logs en `/logs/`
- Configura rotación de logs

### Actualizaciones
- Siempre haz backup antes de actualizar
- Prueba en staging primero

## 🆘 Solución de Problemas

### Error 500
- Revisa logs de PHP en cPanel
- Verifica permisos de archivos
- Comprueba configuración .env

### Error de Base de Datos
- Verifica credenciales en .env
- Comprueba que la BD existe
- Revisa permisos del usuario de BD

### Emails no se envían
- Verifica configuración SMTP
- Comprueba logs de email
- Revisa firewall del servidor

## 📞 Soporte

Si necesitas ayuda:
1. Revisa los logs de error
2. Verifica la configuración
3. Consulta la documentación
EOF

# Crear archivo ZIP para subir
echo -e "${YELLOW}Creando archivo ZIP...${NC}"
cd $BUILD_DIR
zip -r ../control-gastos-produccion.zip . -x "*.git*" "*.DS_Store*"
cd ..

echo -e "${GREEN}✅ Preparación completada!${NC}"
echo -e "${YELLOW}Archivos listos en:${NC}"
echo "  📁 Directorio: $BUILD_DIR/"
echo "  📦 ZIP: control-gastos-produccion.zip"
echo ""
echo -e "${YELLOW}Próximos pasos:${NC}"
echo "  1. Sube control-gastos-produccion.zip a tu cPanel"
echo "  2. Extrae los archivos"
echo "  3. Sigue las instrucciones en README-PRODUCCION.md"
