<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Services\ReminderService;
use ControlGastos\Services\AccountService;
use ControlGastos\Services\CategoryService;
use ControlGastos\Core\Session;

/**
 * Controlador de recordatorios
 * Maneja todas las rutas relacionadas con gestión de recordatorios
 */
class ReminderController
{
    private Container $container;
    private ReminderService $reminderService;
    private AccountService $accountService;
    private CategoryService $categoryService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->reminderService = $container->get('reminderService');
        $this->accountService = $container->get('accountService');
        $this->categoryService = $container->get('categoryService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Mostrar lista de recordatorios
     */
    public function index(): string
    {
        $filters = [
            'page' => (int) ($_GET['page'] ?? 1),
            'per_page' => (int) ($_GET['per_page'] ?? 20),
            'status' => $_GET['status'] ?? '',
            'type' => $_GET['type'] ?? '',
            'priority' => $_GET['priority'] ?? '',
            'search' => $_GET['search'] ?? ''
        ];

        $remindersResult = $this->reminderService->getUserReminders($this->userId, $filters);
        $statsResult = $this->reminderService->getReminderStats($this->userId);
        $overdueResult = $this->reminderService->getOverdueReminders($this->userId);
        $dueSoonResult = $this->reminderService->getDueSoonReminders($this->userId, 7);

        $data = [
            'title' => 'Recordatorios y Compromisos',
            'reminders' => $remindersResult['success'] ? $remindersResult['reminders'] : [],
            'pagination' => $remindersResult['success'] ? $remindersResult['pagination'] : [],
            'stats' => $statsResult['success'] ? $statsResult['stats'] : [],
            'overdue_reminders' => $overdueResult['success'] ? $overdueResult['reminders'] : [],
            'due_soon_reminders' => $dueSoonResult['success'] ? $dueSoonResult['reminders'] : [],
            'filters' => $filters,
            'reminder_types' => $this->reminderService->getReminderTypes(),
            'priorities' => $this->reminderService->getPriorities(),
            'statuses' => $this->reminderService->getStatuses(),
            'recurring_types' => $this->reminderService->getRecurringTypes(),
            'csrf_token' => $this->session->getCsrfToken(),
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reminders/index', $data);
    }

    /**
     * Mostrar formulario de creación
     */
    public function create(): string
    {
        $accountsResult = $this->accountService->getUserAccounts($this->userId);
        $categoriesResult = $this->categoryService->getUserCategories($this->userId);

        $data = [
            'title' => 'Nuevo Recordatorio',
            'accounts' => $accountsResult['success'] ? $accountsResult['accounts'] : [],
            'categories' => $categoriesResult['success'] ? $categoriesResult['categories'] : [],
            'reminder_types' => $this->reminderService->getReminderTypes(),
            'priorities' => $this->reminderService->getPriorities(),
            'statuses' => $this->reminderService->getStatuses(),
            'recurring_types' => $this->reminderService->getRecurringTypes(),
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error'),
            'validation_errors' => $this->session->getFlash('validation_errors', [])
        ];

        return $this->render('reminders/create', $data);
    }

    /**
     * Procesar creación de recordatorio
     */
    public function store(): void
    {
        $data = [
            'title' => $_POST['title'] ?? '',
            'description' => $_POST['description'] ?? '',
            'type' => $_POST['type'] ?? '',
            'amount' => $_POST['amount'] ?? '',
            'category_id' => (int) ($_POST['category_id'] ?? 0),
            'subcategory_id' => !empty($_POST['subcategory_id']) ? (int) $_POST['subcategory_id'] : null,
            'account_id' => !empty($_POST['account_id']) ? (int) $_POST['account_id'] : null,
            'due_date' => $_POST['due_date'] ?? '',
            'priority' => $_POST['priority'] ?? 'medium',
            'status' => $_POST['status'] ?? 'pending',
            'notes' => $_POST['notes'] ?? '',
            'notification_enabled' => isset($_POST['notification_enabled']),
            'notification_days_before' => (int) ($_POST['notification_days_before'] ?? 1),
            'auto_create_transaction' => isset($_POST['auto_create_transaction']),
            'is_recurring' => isset($_POST['is_recurring']),
            'recurring_type' => $_POST['recurring_type'] ?? '',
            'recurring_interval' => (int) ($_POST['recurring_interval'] ?? 1),
            'recurring_end_date' => $_POST['recurring_end_date'] ?? ''
        ];

        $result = $this->reminderService->createReminder($data, $this->userId);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
            header('Location: /reminders');
            exit;
        }

        if (isset($result['errors'])) {
            $this->session->flash('error', 'Errores de validación');
            $this->session->flash('validation_errors', $result['errors']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /reminders/create');
        exit;
    }

    /**
     * Mostrar detalles de recordatorio
     */
    public function show(int $id): string
    {
        $result = $this->reminderService->getReminder($id, $this->userId);

        if (!$result['success']) {
            $this->session->flash('error', $result['message']);
            header('Location: /reminders');
            exit;
        }

        $data = [
            'title' => 'Detalles del Recordatorio',
            'reminder' => $result['reminder'],
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reminders/show', $data);
    }

    /**
     * Mostrar formulario de edición
     */
    public function edit(int $id): string
    {
        $reminderResult = $this->reminderService->getReminder($id, $this->userId);

        if (!$reminderResult['success']) {
            $this->session->flash('error', $reminderResult['message']);
            header('Location: /reminders');
            exit;
        }

        $accountsResult = $this->accountService->getUserAccounts($this->userId);
        $categoriesResult = $this->categoryService->getUserCategories($this->userId);

        $data = [
            'title' => 'Editar Recordatorio',
            'reminder' => $reminderResult['reminder'],
            'accounts' => $accountsResult['success'] ? $accountsResult['accounts'] : [],
            'categories' => $categoriesResult['success'] ? $categoriesResult['categories'] : [],
            'reminder_types' => $this->reminderService->getReminderTypes(),
            'priorities' => $this->reminderService->getPriorities(),
            'statuses' => $this->reminderService->getStatuses(),
            'recurring_types' => $this->reminderService->getRecurringTypes(),
            'csrf_token' => $this->session->getCsrfToken(),
            'error' => $this->session->getFlash('error'),
            'validation_errors' => $this->session->getFlash('validation_errors', [])
        ];

        return $this->render('reminders/edit', $data);
    }

    /**
     * Procesar actualización de recordatorio
     */
    public function update(int $id): void
    {
        $data = [
            'title' => $_POST['title'] ?? '',
            'description' => $_POST['description'] ?? '',
            'type' => $_POST['type'] ?? '',
            'amount' => $_POST['amount'] ?? '',
            'category_id' => (int) ($_POST['category_id'] ?? 0),
            'subcategory_id' => !empty($_POST['subcategory_id']) ? (int) $_POST['subcategory_id'] : null,
            'account_id' => !empty($_POST['account_id']) ? (int) $_POST['account_id'] : null,
            'due_date' => $_POST['due_date'] ?? '',
            'priority' => $_POST['priority'] ?? '',
            'status' => $_POST['status'] ?? '',
            'notes' => $_POST['notes'] ?? '',
            'notification_enabled' => isset($_POST['notification_enabled']),
            'notification_days_before' => (int) ($_POST['notification_days_before'] ?? 1),
            'auto_create_transaction' => isset($_POST['auto_create_transaction']),
            'is_recurring' => isset($_POST['is_recurring']),
            'recurring_type' => $_POST['recurring_type'] ?? '',
            'recurring_interval' => (int) ($_POST['recurring_interval'] ?? 1),
            'recurring_end_date' => $_POST['recurring_end_date'] ?? ''
        ];

        $result = $this->reminderService->updateReminder($id, $data, $this->userId);

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
            header('Location: /reminders/' . $id);
            exit;
        }

        if (isset($result['errors'])) {
            $this->session->flash('error', 'Errores de validación');
            $this->session->flash('validation_errors', $result['errors']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /reminders/' . $id . '/edit');
        exit;
    }

    /**
     * Eliminar recordatorio
     */
    public function delete(int $id): void
    {
        $result = $this->reminderService->deleteReminder($id, $this->userId);

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /reminders');
        exit;
    }

    /**
     * Marcar como completado
     */
    public function markCompleted(int $id): void
    {
        $createTransaction = isset($_POST['create_transaction']) && $_POST['create_transaction'] === '1';
        $result = $this->reminderService->markAsCompleted($id, $this->userId, $createTransaction);

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /reminders');
        exit;
    }

    /**
     * Buscar recordatorios (AJAX)
     */
    public function search(): void
    {
        $search = $_GET['q'] ?? '';

        if (strlen($search) < 2) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'El término de búsqueda debe tener al menos 2 caracteres'
            ]);
            exit;
        }

        $result = $this->reminderService->searchReminders($this->userId, $search);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Obtener estadísticas (AJAX)
     */
    public function getStats(): void
    {
        $result = $this->reminderService->getReminderStats($this->userId);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Vista de calendario
     */
    public function calendar(): string
    {
        $month = $_GET['month'] ?? date('Y-m');
        $startDate = new \DateTime($month . '-01');
        $endDate = clone $startDate;
        $endDate->modify('last day of this month')->setTime(23, 59, 59);

        $result = $this->reminderService->getCalendarReminders($this->userId, $startDate, $endDate);

        $data = [
            'title' => 'Calendario de Recordatorios',
            'reminders' => $result['success'] ? $result['reminders'] : [],
            'current_month' => $month,
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reminders/calendar', $data);
    }

    /**
     * Dashboard de recordatorios
     */
    public function dashboard(): string
    {
        $statsResult = $this->reminderService->getReminderStats($this->userId);
        $overdueResult = $this->reminderService->getOverdueReminders($this->userId);
        $dueSoonResult = $this->reminderService->getDueSoonReminders($this->userId, 7);

        $data = [
            'title' => 'Dashboard de Recordatorios',
            'stats' => $statsResult['success'] ? $statsResult['stats'] : [],
            'overdue_reminders' => $overdueResult['success'] ? $overdueResult['reminders'] : [],
            'due_soon_reminders' => $dueSoonResult['success'] ? $dueSoonResult['reminders'] : [],
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('reminders/dashboard', $data);
    }

    /**
     * Exportar recordatorios
     */
    public function export(): void
    {
        $filters = [
            'status' => $_GET['status'] ?? '',
            'type' => $_GET['type'] ?? '',
            'priority' => $_GET['priority'] ?? ''
        ];

        $result = $this->reminderService->getUserReminders($this->userId, $filters);
        
        if ($result['success']) {
            $filename = 'recordatorios_' . date('Y-m-d_H-i-s') . '.json';
            
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            echo json_encode([
                'export_date' => date('Y-m-d H:i:s'),
                'user_id' => $this->userId,
                'filters' => $filters,
                'reminders' => $result['reminders']
            ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        } else {
            $this->session->flash('error', 'Error al exportar recordatorios');
            header('Location: /reminders');
        }
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Verificar si es una request AJAX
     */
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
