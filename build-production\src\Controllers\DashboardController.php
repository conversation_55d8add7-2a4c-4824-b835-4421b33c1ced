<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Core\Database;
use ControlGastos\Core\Logger;
use ControlGastos\Services\TransactionService;
use ControlGastos\Services\AccountService;
use ControlGastos\Services\ReminderService;
use ControlGastos\Services\ReportService;
use ControlGastos\Services\AdvancedReportService;
use ControlGastos\Services\EmailService;
use ControlGastos\Core\Session;

/**
 * Controlador del Dashboard
 * Maneja la página principal con resumen financiero
 */
class DashboardController
{
    private Database $database;
    private Logger $logger;
    private TransactionService $transactionService;
    private AccountService $accountService;
    private ReminderService $reminderService;
    private ReportService $reportService;
    private AdvancedReportService $advancedReportService;
    private EmailService $emailService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->database = $container->get('database');
        $this->logger = $container->get('logger');
        $this->transactionService = $container->get('transactionService');
        $this->accountService = $container->get('accountService');
        $this->reminderService = $container->get('reminderService');
        $this->reportService = $container->get('reportService');
        $this->advancedReportService = $container->get('advancedReportService');
        $this->emailService = $container->get('emailService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Mostrar dashboard principal
     */
    public function index(): string
    {
        try {
            // Usar el servicio de reportes avanzados para obtener todos los datos
            $dashboardData = $this->advancedReportService->getDashboardData($this->userId);

            // Obtener cuentas del usuario
            $accounts = $this->accountService->getUserAccounts($this->userId);

            // Obtener transacciones recientes
            $recent_transactions = $this->getRecentTransactions($this->userId, 10);

            // Obtener recordatorios próximos
            $upcoming_reminders = $this->getUpcomingReminders($this->userId, 5);

            $data = [
                'title' => 'Dashboard Financiero',
                'breadcrumb' => [
                    ['title' => 'Dashboard']
                ],
                'financial_summary' => $dashboardData['financial_summary'] ?? [],
                'comparison' => $dashboardData['comparison'] ?? [],
                'trends' => $dashboardData['trends'] ?? [],
                'category_analysis' => $dashboardData['category_analysis'] ?? [],
                'spending_patterns' => $dashboardData['spending_patterns'] ?? [],
                'financial_health' => $dashboardData['financial_health'] ?? [],
                'predictions' => $dashboardData['predictions'] ?? [],
                'goals_progress' => $dashboardData['goals_progress'] ?? [],
                'alerts' => $dashboardData['alerts'] ?? [],
                'accounts' => $accounts,
                'recent_transactions' => $recent_transactions,
                'upcoming_reminders' => $upcoming_reminders,
                'success' => $this->session->getFlash('success'),
                'error' => $this->session->getFlash('error')
            ];

            return $this->render('dashboard/index', $data);

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error cargando el dashboard: ' . $e->getMessage());

            $data = [
                'title' => 'Dashboard Financiero',
                'breadcrumb' => [
                    ['title' => 'Dashboard']
                ],
                'financial_summary' => [
                    'total_balance' => 0,
                    'monthly_income' => 0,
                    'monthly_expenses' => 0,
                    'savings_rate' => 0
                ],
                'comparison' => [],
                'trends' => [],
                'category_analysis' => [],
                'spending_patterns' => [],
                'financial_health' => [],
                'predictions' => [],
                'goals_progress' => [],
                'alerts' => [],
                'accounts' => [],
                'recent_transactions' => [],
                'upcoming_reminders' => [],
                'error' => $this->session->getFlash('error')
            ];

            return $this->render('dashboard/index', $data);
        }
    }

    /**
     * Obtener resumen financiero
     */
    private function getFinancialSummary(): array
    {
        try {
            // Fechas del mes actual
            $startOfMonth = date('Y-m-01');
            $endOfMonth = date('Y-m-t');
            
            // Balance total de todas las cuentas
            $accounts = $this->accountService->getUserAccounts($this->userId);
            $total_balance = array_sum(array_column($accounts, 'balance'));
            
            // Ingresos y gastos del mes actual
            $monthly_transactions = $this->getTransactionsByDateRange(
                $this->userId,
                $startOfMonth,
                $endOfMonth
            );
            
            $monthly_income = 0;
            $monthly_expenses = 0;
            
            foreach ($monthly_transactions as $transaction) {
                if ($transaction['type'] === 'income') {
                    $monthly_income += $transaction['amount'];
                } else {
                    $monthly_expenses += $transaction['amount'];
                }
            }
            
            // Calcular tasa de ahorro
            $savings_rate = $monthly_income > 0 ? (($monthly_income - $monthly_expenses) / $monthly_income) * 100 : 0;
            
            return [
                'total_balance' => $total_balance,
                'monthly_income' => $monthly_income,
                'monthly_expenses' => $monthly_expenses,
                'savings_rate' => max(0, $savings_rate),
                'net_income' => $monthly_income - $monthly_expenses
            ];

        } catch (\Exception $e) {
            return [
                'total_balance' => 0,
                'monthly_income' => 0,
                'monthly_expenses' => 0,
                'savings_rate' => 0,
                'net_income' => 0
            ];
        }
    }

    /**
     * Obtener datos para gráficos (AJAX)
     */
    public function getChartData(): void
    {
        try {
            $type = $_GET['type'] ?? 'income_expenses';
            $period = $_GET['period'] ?? '6months';
            
            switch ($type) {
                case 'income_expenses':
                    $data = $this->getIncomeExpensesChartData($period);
                    break;
                case 'category_distribution':
                    $data = $this->getCategoryDistributionData($period);
                    break;
                default:
                    throw new \Exception('Tipo de gráfico no válido');
            }

            $this->jsonResponse([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Obtener datos para gráfico de ingresos vs gastos
     */
    private function getIncomeExpensesChartData(string $period): array
    {
        $months = $period === '1year' ? 12 : 6;
        $data = [
            'labels' => [],
            'income' => [],
            'expenses' => []
        ];

        for ($i = $months - 1; $i >= 0; $i--) {
            $date = date('Y-m-01', strtotime("-{$i} months"));
            $startDate = $date;
            $endDate = date('Y-m-t', strtotime($date));
            
            $data['labels'][] = date('M Y', strtotime($date));
            
            $transactions = $this->transactionService->getTransactionsByDateRange(
                $this->userId,
                $startDate,
                $endDate
            );
            
            $income = 0;
            $expenses = 0;
            
            foreach ($transactions as $transaction) {
                if ($transaction['type'] === 'income') {
                    $income += $transaction['amount'];
                } else {
                    $expenses += $transaction['amount'];
                }
            }
            
            $data['income'][] = $income;
            $data['expenses'][] = $expenses;
        }

        return $data;
    }

    /**
     * Obtener datos para gráfico de distribución por categorías
     */
    private function getCategoryDistributionData(string $period): array
    {
        $months = $period === '1year' ? 12 : 6;
        $startDate = date('Y-m-01', strtotime("-{$months} months"));
        $endDate = date('Y-m-t');
        
        $categoryExpenses = $this->reportService->getCategoryExpenses(
            $this->userId,
            $startDate,
            $endDate
        );
        
        $data = [
            'labels' => [],
            'values' => [],
            'colors' => []
        ];
        
        foreach ($categoryExpenses as $category) {
            $data['labels'][] = $category['category_name'];
            $data['values'][] = $category['total_amount'];
            $data['colors'][] = $category['category_color'] ?? '#007bff';
        }
        
        return $data;
    }

    /**
     * Actualizar dashboard (AJAX)
     */
    public function refresh(): void
    {
        try {
            $financial_summary = $this->getFinancialSummary();
            $accounts = $this->accountService->getUserAccounts($this->userId);
            $recent_transactions = $this->transactionService->getRecentTransactions($this->userId, 5);
            
            $this->jsonResponse([
                'success' => true,
                'data' => [
                    'financial_summary' => $financial_summary,
                    'accounts' => $accounts,
                    'recent_transactions' => $recent_transactions,
                    'timestamp' => time()
                ]
            ]);

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error actualizando dashboard: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Obtener transacciones recientes
     */
    private function getRecentTransactions(int $userId, int $limit = 10): array
    {
        try {
            $query = "
                SELECT
                    t.*,
                    c.name as category_name,
                    c.color as category_color,
                    c.icon as category_icon,
                    a.name as account_name
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                LEFT JOIN accounts a ON t.account_id = a.id
                WHERE t.user_id = ?
                ORDER BY t.created_at DESC
                LIMIT ?
            ";

            return $this->database->select($query, [$userId, $limit]);

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo transacciones recientes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener recordatorios próximos
     */
    private function getUpcomingReminders(int $userId, int $limit = 5): array
    {
        try {
            $query = "
                SELECT *
                FROM reminders
                WHERE user_id = ?
                AND due_date >= CURDATE()
                AND is_completed = 0
                ORDER BY due_date ASC
                LIMIT ?
            ";

            return $this->database->select($query, [$userId, $limit]);

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo recordatorios próximos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener transacciones por rango de fechas
     */
    private function getTransactionsByDateRange(int $userId, string $startDate, string $endDate): array
    {
        try {
            $query = "
                SELECT *
                FROM transactions
                WHERE user_id = ?
                AND transaction_date BETWEEN ? AND ?
                ORDER BY transaction_date DESC
            ";

            return $this->database->select($query, [$userId, $startDate, $endDate]);

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo transacciones por rango: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Enviar respuesta JSON
     */
    private function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
