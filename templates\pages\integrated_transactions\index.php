<?php
// La autenticación ya se verifica en el controlador
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>Control de Gastos Integrado
                    </h1>
                    <p class="text-muted">Gestiona tus ingresos y gastos vinculados a cuentas bancarias y tarjetas de crédito</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#incomeModal">
                        <i class="fas fa-plus me-2"></i>Nuevo Ingreso
                    </button>
                    <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#expenseModal">
                        <i class="fas fa-minus me-2"></i>Nuevo Gasto
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas del Mes -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Ingresos del Mes</h6>
                            <h3 class="mb-0">$<?= number_format($monthly_stats['total_income'] ?? 0, 0) ?></h3>
                            <small><?= $monthly_stats['income_count'] ?? 0 ?> transacciones</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Gastos del Mes</h6>
                            <h3 class="mb-0">$<?= number_format($monthly_stats['total_expenses'] ?? 0, 0) ?></h3>
                            <small><?= $monthly_stats['expense_count'] ?? 0 ?> transacciones</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Balance del Mes</h6>
                            <?php 
                            $balance = ($monthly_stats['total_income'] ?? 0) - ($monthly_stats['total_expenses'] ?? 0);
                            $balanceClass = $balance >= 0 ? 'text-white' : 'text-warning';
                            ?>
                            <h3 class="mb-0 <?= $balanceClass ?>">
                                <?= $balance >= 0 ? '+' : '' ?>$<?= number_format($balance, 0) ?>
                            </h3>
                            <small><?= $balance >= 0 ? 'Superávit' : 'Déficit' ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-balance-scale fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Cuentas Activas</h6>
                            <h3 class="mb-0"><?= count($bank_accounts) + count($credit_cards) ?></h3>
                            <small><?= count($bank_accounts) ?> bancarias, <?= count($credit_cards) ?> tarjetas</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Timeline de Movimientos -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Timeline de Movimientos
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($timeline)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No hay movimientos registrados</h5>
                            <p class="text-muted">Comienza registrando tu primer ingreso o gasto</p>
                        </div>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($timeline as $movement): ?>
                            <div class="timeline-item mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="timeline-icon me-3">
                                        <?php
                                        $iconClass = '';
                                        $bgClass = '';
                                        switch ($movement['movement_type']) {
                                            case 'income':
                                                $iconClass = 'fas fa-arrow-down';
                                                $bgClass = 'bg-success';
                                                break;
                                            case 'expense':
                                                $iconClass = 'fas fa-arrow-up';
                                                $bgClass = 'bg-danger';
                                                break;
                                            case 'credit_payment':
                                                $iconClass = 'fas fa-credit-card';
                                                $bgClass = 'bg-primary';
                                                break;
                                            case 'bank_movement':
                                                $iconClass = 'fas fa-university';
                                                $bgClass = 'bg-info';
                                                break;
                                            case 'credit_transaction':
                                                $iconClass = 'fas fa-shopping-cart';
                                                $bgClass = 'bg-warning';
                                                break;
                                        }
                                        ?>
                                        <div class="rounded-circle <?= $bgClass ?> text-white d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="<?= $iconClass ?>"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1"><?= htmlspecialchars($movement['description']) ?></h6>
                                                <div class="small text-muted">
                                                    <?php if ($movement['bank_account_name']): ?>
                                                        <i class="fas fa-university me-1"></i><?= htmlspecialchars($movement['bank_account_name']) ?>
                                                    <?php elseif ($movement['credit_card_name']): ?>
                                                        <i class="fas fa-credit-card me-1"></i><?= htmlspecialchars($movement['credit_card_name']) ?>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($movement['category_name']): ?>
                                                        • <i class="fas fa-tag me-1"></i><?= htmlspecialchars($movement['category_name']) ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="small text-muted">
                                                    <i class="fas fa-calendar me-1"></i><?= date('d/m/Y', strtotime($movement['movement_date'])) ?>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <strong class="<?= in_array($movement['movement_type'], ['income']) ? 'text-success' : 'text-danger' ?>">
                                                    <?= in_array($movement['movement_type'], ['income']) ? '+' : '-' ?>$<?= number_format($movement['amount'], 0) ?>
                                                </strong>
                                                <div class="small text-muted">
                                                    <?php
                                                    $typeLabels = [
                                                        'income' => 'Ingreso',
                                                        'expense' => 'Gasto',
                                                        'credit_payment' => 'Pago Tarjeta',
                                                        'bank_movement' => 'Mov. Bancario',
                                                        'credit_transaction' => 'Transacción'
                                                    ];
                                                    echo $typeLabels[$movement['movement_type']] ?? ucfirst($movement['movement_type']);
                                                    ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Panel de Cuentas y Tarjetas -->
        <div class="col-lg-4">
            <!-- Cuentas Bancarias -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-university me-2"></i>Cuentas Bancarias
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($bank_accounts)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-university fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No hay cuentas bancarias</p>
                            <a href="/controlGastos/public/?route=bank-accounts/create" class="btn btn-sm btn-outline-primary mt-2">
                                Crear cuenta
                            </a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($bank_accounts as $account): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <div class="fw-bold"><?= htmlspecialchars($account['account_name']) ?></div>
                                <small class="text-muted"><?= htmlspecialchars($account['bank_name']) ?></small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold text-success">$<?= number_format($account['current_balance'], 0) ?></div>
                                <small class="text-muted"><?= ucfirst($account['account_type']) ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Tarjetas de Crédito -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i>Tarjetas de Crédito
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($credit_cards)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No hay tarjetas de crédito</p>
                            <a href="/controlGastos/public/?route=credit-cards/create" class="btn btn-sm btn-outline-primary mt-2">
                                Crear tarjeta
                            </a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($credit_cards as $card): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <div class="fw-bold"><?= htmlspecialchars($card['card_name']) ?></div>
                                <small class="text-muted"><?= htmlspecialchars($card['bank_name']) ?></small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold text-danger">$<?= number_format($card['current_balance'], 0) ?></div>
                                <small class="text-success">Disponible: $<?= number_format($card['available_credit'], 0) ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Nuevo Ingreso -->
<div class="modal fade" id="incomeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Nuevo Ingreso
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/controlGastos/public/?route=accounts/store">
                <div class="modal-body">
                    <input type="hidden" name="type" value="income">
                    
                    <div class="mb-3">
                        <label for="income_amount" class="form-label">Monto *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="income_amount" name="amount"
                                   placeholder="100000" required min="1" step="0.01">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="income_description" class="form-label">Descripción *</label>
                        <input type="text" class="form-control" id="income_description" name="description" 
                               placeholder="Ej: Salario, Freelance, Venta" required maxlength="255">
                    </div>

                    <div class="mb-3">
                        <label for="income_category" class="form-label">Categoría</label>
                        <select class="form-select" id="income_category" name="category_id">
                            <option value="">Sin categoría</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="income_destination" class="form-label">Destino *</label>
                        <select class="form-select" id="income_destination" name="payment_method" required>
                            <option value="">Seleccionar destino...</option>
                            <optgroup label="Cuentas Bancarias">
                                <?php foreach ($bank_accounts as $account): ?>
                                <option value="bank_account" data-account-id="<?= $account['id'] ?>">
                                    <?= htmlspecialchars($account['account_name']) ?> - <?= htmlspecialchars($account['bank_name']) ?>
                                </option>
                                <?php endforeach; ?>
                            </optgroup>
                            <optgroup label="Tarjetas de Crédito (Devoluciones/Cashback)">
                                <?php foreach ($credit_cards as $card): ?>
                                <option value="credit_card" data-card-id="<?= $card['id'] ?>">
                                    <?= htmlspecialchars($card['card_name']) ?> - <?= htmlspecialchars($card['bank_name']) ?>
                                </option>
                                <?php endforeach; ?>
                            </optgroup>
                        </select>
                        <input type="hidden" id="income_bank_account_id" name="bank_account_id">
                        <input type="hidden" id="income_credit_card_id" name="credit_card_id">
                    </div>

                    <div class="mb-3">
                        <label for="income_date" class="form-label">Fecha *</label>
                        <input type="date" class="form-control" id="income_date" name="transaction_date" 
                               value="<?= date('Y-m-d') ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="income_reference" class="form-label">Referencia</label>
                        <input type="text" class="form-control" id="income_reference" name="reference" 
                               placeholder="Número de comprobante o referencia" maxlength="100">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Registrar Ingreso
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Nuevo Gasto -->
<div class="modal fade" id="expenseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-minus me-2"></i>Nuevo Gasto
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/controlGastos/public/?route=accounts/store">
                <div class="modal-body">
                    <input type="hidden" name="type" value="expense">
                    
                    <div class="mb-3">
                        <label for="expense_amount" class="form-label">Monto *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="expense_amount" name="amount"
                                   placeholder="50000" required min="1" step="0.01">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="expense_description" class="form-label">Descripción *</label>
                        <input type="text" class="form-control" id="expense_description" name="description" 
                               placeholder="Ej: Supermercado, Gasolina, Restaurante" required maxlength="255">
                    </div>

                    <div class="mb-3">
                        <label for="expense_category" class="form-label">Categoría</label>
                        <select class="form-select" id="expense_category" name="category_id">
                            <option value="">Sin categoría</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="expense_payment_method" class="form-label">Método de Pago *</label>
                        <select class="form-select" id="expense_payment_method" name="payment_method" required>
                            <option value="">Seleccionar método...</option>
                            <optgroup label="Cuentas Bancarias">
                                <?php foreach ($bank_accounts as $account): ?>
                                <option value="bank_account" data-account-id="<?= $account['id'] ?>">
                                    <?= htmlspecialchars($account['account_name']) ?> - $<?= number_format($account['current_balance'], 0) ?>
                                </option>
                                <?php endforeach; ?>
                            </optgroup>
                            <optgroup label="Tarjetas de Crédito">
                                <?php foreach ($credit_cards as $card): ?>
                                <option value="credit_card" data-card-id="<?= $card['id'] ?>">
                                    <?= htmlspecialchars($card['card_name']) ?> - Disponible: $<?= number_format($card['available_credit'], 0) ?>
                                </option>
                                <?php endforeach; ?>
                            </optgroup>
                        </select>
                        <input type="hidden" id="expense_bank_account_id" name="bank_account_id">
                        <input type="hidden" id="expense_credit_card_id" name="credit_card_id">
                    </div>

                    <div class="mb-3">
                        <label for="expense_date" class="form-label">Fecha *</label>
                        <input type="date" class="form-control" id="expense_date" name="transaction_date" 
                               value="<?= date('Y-m-d') ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="expense_reference" class="form-label">Referencia</label>
                        <input type="text" class="form-control" id="expense_reference" name="reference" 
                               placeholder="Número de factura o comprobante" maxlength="100">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-save me-2"></i>Registrar Gasto
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.timeline-item {
    border-left: 2px solid #e9ecef;
    padding-left: 1rem;
    margin-left: 20px;
    position: relative;
}

.timeline-item:last-child {
    border-left: none;
}

.timeline-icon {
    margin-left: -32px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Manejar selección de destino para ingresos
    document.getElementById('income_destination').addEventListener('change', function() {
        const bankAccountInput = document.getElementById('income_bank_account_id');
        const creditCardInput = document.getElementById('income_credit_card_id');
        
        // Limpiar valores
        bankAccountInput.value = '';
        creditCardInput.value = '';
        
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.accountId) {
            bankAccountInput.value = selectedOption.dataset.accountId;
        } else if (selectedOption.dataset.cardId) {
            creditCardInput.value = selectedOption.dataset.cardId;
        }
    });

    // Manejar selección de método de pago para gastos
    document.getElementById('expense_payment_method').addEventListener('change', function() {
        const bankAccountInput = document.getElementById('expense_bank_account_id');
        const creditCardInput = document.getElementById('expense_credit_card_id');
        
        // Limpiar valores
        bankAccountInput.value = '';
        creditCardInput.value = '';
        
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.accountId) {
            bankAccountInput.value = selectedOption.dataset.accountId;
        } else if (selectedOption.dataset.cardId) {
            creditCardInput.value = selectedOption.dataset.cardId;
        }
    });
});
</script>
