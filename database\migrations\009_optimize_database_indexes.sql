-- Migración 009: Optimización de índices y rendimiento
-- Fecha: 2024-01-01
-- Descripción: Índices optimizados para mejorar el rendimiento de consultas

-- Índices optimizados para la tabla transactions
-- Índice compuesto para consultas por usuario y fecha (más común)
CREATE INDEX IF NOT EXISTS idx_transactions_user_date ON transactions(user_id, transaction_date DESC);

-- Índice para consultas por cuenta y fecha
CREATE INDEX IF NOT EXISTS idx_transactions_account_date ON transactions(account_id, transaction_date DESC);

-- Índice para consultas por categoría y fecha
CREATE INDEX IF NOT EXISTS idx_transactions_category_date ON transactions(category_id, transaction_date DESC);

-- Índice para consultas por tipo y fecha
CREATE INDEX IF NOT EXISTS idx_transactions_type_date ON transactions(type, transaction_date DESC);

-- Índice para búsquedas por monto (rangos)
CREATE INDEX IF NOT EXISTS idx_transactions_amount ON transactions(amount);

-- Índice para consultas por usuario, tipo y fecha (reportes)
CREATE INDEX IF NOT EXISTS idx_transactions_user_type_date ON transactions(user_id, type, transaction_date DESC);

-- Índices optimizados para la tabla reminders
-- Índice compuesto para recordatorios por usuario y fecha de vencimiento
CREATE INDEX IF NOT EXISTS idx_reminders_user_due ON reminders(user_id, due_date);

-- Índice para recordatorios por estado y fecha
CREATE INDEX IF NOT EXISTS idx_reminders_status_due ON reminders(status, due_date);

-- Índice para recordatorios por tipo y fecha
CREATE INDEX IF NOT EXISTS idx_reminders_type_due ON reminders(type, due_date);

-- Índice para recordatorios recurrentes
CREATE INDEX IF NOT EXISTS idx_reminders_recurring ON reminders(is_recurring, next_occurrence);

-- Índices optimizados para la tabla accounts
-- Índice para cuentas activas por usuario
CREATE INDEX IF NOT EXISTS idx_accounts_user_active ON accounts(user_id, is_active);

-- Índice para consultas por tipo de cuenta
CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(type);

-- Índices optimizados para la tabla categories
-- Índice para categorías activas por usuario
CREATE INDEX IF NOT EXISTS idx_categories_user_active ON categories(user_id, is_active);

-- Índice para categorías por tipo
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);

-- Índices optimizados para la tabla subcategories
-- Índice compuesto para subcategorías por categoría padre
CREATE INDEX IF NOT EXISTS idx_subcategories_category ON subcategories(category_id, is_active);

-- Índices optimizados para tablas de auditoría y seguridad
-- Índice compuesto para log de auditoría
CREATE INDEX IF NOT EXISTS idx_audit_user_date ON audit_log(user_id, created_at DESC);

-- Índice para auditoría por acción y fecha
CREATE INDEX IF NOT EXISTS idx_audit_action_date ON audit_log(action, created_at DESC);

-- Índice para auditoría por recurso
CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_log(resource, resource_id);

-- Índice compuesto para intentos de login
CREATE INDEX IF NOT EXISTS idx_login_email_date ON login_attempts(email, attempted_at DESC);

-- Índice para intentos por IP y fecha
CREATE INDEX IF NOT EXISTS idx_login_ip_date ON login_attempts(ip_address, attempted_at DESC);

-- Índice para intentos exitosos por usuario
CREATE INDEX IF NOT EXISTS idx_login_user_success ON login_attempts(user_id, success, attempted_at DESC);

-- Índices para tablas de backup
-- Índice para backups por usuario y fecha
CREATE INDEX IF NOT EXISTS idx_backups_user_date ON backups(user_id, created_at DESC);

-- Índice para backups por estado
CREATE INDEX IF NOT EXISTS idx_backups_status_date ON backups(status, created_at DESC);

-- Índice para backups programados
CREATE INDEX IF NOT EXISTS idx_scheduled_backups_next_run ON scheduled_backups(status, next_run);

-- Optimización de tablas existentes
OPTIMIZE TABLE users;
OPTIMIZE TABLE accounts;
OPTIMIZE TABLE categories;
OPTIMIZE TABLE subcategories;
OPTIMIZE TABLE transactions;
OPTIMIZE TABLE reminders;
OPTIMIZE TABLE audit_log;
OPTIMIZE TABLE login_attempts;
OPTIMIZE TABLE backups;
OPTIMIZE TABLE scheduled_backups;

-- Análisis de tablas para actualizar estadísticas
ANALYZE TABLE users;
ANALYZE TABLE accounts;
ANALYZE TABLE categories;
ANALYZE TABLE subcategories;
ANALYZE TABLE transactions;
ANALYZE TABLE reminders;
ANALYZE TABLE audit_log;
ANALYZE TABLE login_attempts;
ANALYZE TABLE backups;
ANALYZE TABLE scheduled_backups;

-- Crear vista optimizada para transacciones con información relacionada
CREATE OR REPLACE VIEW transaction_details AS
SELECT 
    t.id,
    t.user_id,
    t.account_id,
    t.category_id,
    t.subcategory_id,
    t.type,
    t.amount,
    t.description,
    t.transaction_date,
    t.created_at,
    t.updated_at,
    a.name as account_name,
    a.type as account_type,
    c.name as category_name,
    c.color as category_color,
    c.icon as category_icon,
    s.name as subcategory_name,
    u.name as user_name
FROM transactions t
INNER JOIN accounts a ON t.account_id = a.id
INNER JOIN categories c ON t.category_id = c.id
LEFT JOIN subcategories s ON t.subcategory_id = s.id
INNER JOIN users u ON t.user_id = u.id;

-- Crear vista para estadísticas de usuario
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.email,
    COUNT(DISTINCT a.id) as total_accounts,
    COUNT(DISTINCT c.id) as total_categories,
    COUNT(DISTINCT t.id) as total_transactions,
    COUNT(DISTINCT r.id) as total_reminders,
    COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END), 0) as total_income,
    COALESCE(SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END), 0) as total_expenses,
    COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE -t.amount END), 0) as net_balance,
    MAX(t.transaction_date) as last_transaction_date,
    u.created_at as user_since
FROM users u
LEFT JOIN accounts a ON u.id = a.user_id AND a.is_active = 1
LEFT JOIN categories c ON u.id = c.user_id AND c.is_active = 1
LEFT JOIN transactions t ON u.id = t.user_id
LEFT JOIN reminders r ON u.id = r.user_id
GROUP BY u.id, u.name, u.email, u.created_at;

-- Crear vista para métricas de rendimiento
CREATE OR REPLACE VIEW performance_metrics AS
SELECT 
    'transactions' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_id) as unique_users,
    MIN(created_at) as oldest_record,
    MAX(created_at) as newest_record,
    AVG(amount) as avg_amount,
    SUM(amount) as total_amount
FROM transactions
UNION ALL
SELECT 
    'reminders' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_id) as unique_users,
    MIN(created_at) as oldest_record,
    MAX(created_at) as newest_record,
    0 as avg_amount,
    0 as total_amount
FROM reminders
UNION ALL
SELECT 
    'audit_log' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_id) as unique_users,
    MIN(created_at) as oldest_record,
    MAX(created_at) as newest_record,
    0 as avg_amount,
    0 as total_amount
FROM audit_log;

-- Procedimiento para limpiar datos antiguos y optimizar rendimiento
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS OptimizePerformance()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Limpiar intentos de login antiguos (más de 30 días)
    DELETE FROM login_attempts 
    WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

    -- Limpiar log de auditoría antiguo (más de 90 días)
    DELETE FROM audit_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

    -- Optimizar tablas principales
    OPTIMIZE TABLE transactions;
    OPTIMIZE TABLE reminders;
    OPTIMIZE TABLE audit_log;
    OPTIMIZE TABLE login_attempts;

    -- Actualizar estadísticas
    ANALYZE TABLE transactions;
    ANALYZE TABLE reminders;
    ANALYZE TABLE audit_log;
    ANALYZE TABLE login_attempts;

    COMMIT;
END //

DELIMITER ;

-- Función para calcular el balance de una cuenta de forma optimizada
DELIMITER //

CREATE FUNCTION IF NOT EXISTS GetAccountBalanceOptimized(account_id INT)
RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE balance DECIMAL(15,2) DEFAULT 0;
    
    SELECT COALESCE(SUM(
        CASE 
            WHEN type = 'income' THEN amount 
            ELSE -amount 
        END
    ), 0) INTO balance
    FROM transactions 
    WHERE account_id = account_id;
    
    RETURN balance;
END //

DELIMITER ;

-- Función para obtener estadísticas de transacciones de un usuario
DELIMITER //

CREATE FUNCTION IF NOT EXISTS GetUserTransactionStats(user_id INT, start_date DATE, end_date DATE)
RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result JSON;
    
    SELECT JSON_OBJECT(
        'total_income', COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0),
        'total_expenses', COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0),
        'transaction_count', COUNT(*),
        'avg_transaction', COALESCE(AVG(amount), 0),
        'period_start', start_date,
        'period_end', end_date
    ) INTO result
    FROM transactions 
    WHERE user_id = user_id 
    AND transaction_date BETWEEN start_date AND end_date;
    
    RETURN result;
END //

DELIMITER ;

-- Crear evento para optimización automática (comentado por defecto)
-- SET GLOBAL event_scheduler = ON;

-- CREATE EVENT IF NOT EXISTS auto_optimize_performance
-- ON SCHEDULE EVERY 1 WEEK
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL OptimizePerformance();

-- Configuración de variables de MySQL para optimización
-- Estas configuraciones deben ser ajustadas según el servidor

-- SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB (ajustar según RAM disponible)
-- SET GLOBAL query_cache_size = 67108864; -- 64MB
-- SET GLOBAL query_cache_type = ON;
-- SET GLOBAL tmp_table_size = 67108864; -- 64MB
-- SET GLOBAL max_heap_table_size = 67108864; -- 64MB

-- Comentarios de documentación
/*
Optimizaciones implementadas:

1. Índices Compuestos:
   - Optimizados para consultas más comunes
   - Incluyen ordenamiento DESC para fechas
   - Combinan múltiples columnas frecuentemente consultadas juntas

2. Vistas Optimizadas:
   - transaction_details: JOIN precompilado para consultas frecuentes
   - user_stats: Estadísticas agregadas por usuario
   - performance_metrics: Métricas del sistema

3. Procedimientos de Mantenimiento:
   - OptimizePerformance(): Limpieza y optimización automática
   - Funciones optimizadas para cálculos frecuentes

4. Configuraciones Recomendadas:
   - Buffer pool de InnoDB aumentado
   - Query cache habilitado
   - Tablas temporales optimizadas

5. Monitoreo:
   - Índices para consultas de auditoría
   - Métricas de rendimiento
   - Estadísticas de uso

Consultas optimizadas más comunes:
- Transacciones por usuario y fecha
- Balances de cuenta
- Recordatorios próximos a vencer
- Estadísticas de categorías
- Log de auditoría por usuario
*/
