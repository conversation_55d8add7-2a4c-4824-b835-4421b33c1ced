<?php

declare(strict_types=1);

namespace ControlGastos\Repositories;

use ControlGastos\Core\Database;
use ControlGastos\Models\Account;
use Exception;

/**
 * Repositorio de cuentas financieras
 * Maneja todas las operaciones de base de datos relacionadas con cuentas
 */
class AccountRepository
{
    private Database $database;

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Crear nueva cuenta
     */
    public function create(Account $account): Account
    {
        $query = "
            INSERT INTO accounts (
                user_id, name, type, balance, currency, 
                description, is_active, created_at, updated_at
            ) VALUES (
                :user_id, :name, :type, :balance, :currency,
                :description, :is_active, :created_at, :updated_at
            )
        ";

        $params = [
            'user_id' => $account->getUserId(),
            'name' => $account->getName(),
            'type' => $account->getType(),
            'balance' => $account->getBalance(),
            'currency' => $account->getCurrency(),
            'description' => $account->getDescription(),
            'is_active' => $account->isActive() ? 1 : 0,
            'created_at' => $account->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $account->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        $id = $this->database->insert($query, $params);
        $account->setId($id);

        return $account;
    }

    /**
     * Buscar cuenta por ID
     */
    public function findById(int $id): ?Account
    {
        $query = "SELECT * FROM accounts WHERE id = :id";
        $result = $this->database->selectOne($query, ['id' => $id]);

        return $result ? new Account($result) : null;
    }

    /**
     * Buscar cuenta por ID y usuario
     */
    public function findByIdAndUser(int $id, int $userId): ?Account
    {
        $query = "SELECT * FROM accounts WHERE id = :id AND user_id = :user_id";
        $result = $this->database->selectOne($query, ['id' => $id, 'user_id' => $userId]);

        return $result ? new Account($result) : null;
    }

    /**
     * Obtener todas las cuentas de un usuario
     */
    public function findByUser(int $userId, bool $activeOnly = false): array
    {
        $query = "SELECT * FROM accounts WHERE user_id = :user_id";
        $params = ['user_id' => $userId];

        if ($activeOnly) {
            $query .= " AND is_active = 1";
        }

        $query .= " ORDER BY name ASC";

        $results = $this->database->select($query, $params);
        return array_map(fn($row) => new Account($row), $results);
    }

    /**
     * Obtener cuentas por tipo
     */
    public function findByType(int $userId, string $type): array
    {
        $query = "
            SELECT * FROM accounts 
            WHERE user_id = :user_id AND type = :type AND is_active = 1
            ORDER BY name ASC
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'type' => $type
        ]);

        return array_map(fn($row) => new Account($row), $results);
    }

    /**
     * Actualizar cuenta
     */
    public function update(Account $account): bool
    {
        $query = "
            UPDATE accounts SET
                name = :name,
                type = :type,
                balance = :balance,
                currency = :currency,
                description = :description,
                is_active = :is_active,
                updated_at = :updated_at
            WHERE id = :id
        ";

        $params = [
            'id' => $account->getId(),
            'name' => $account->getName(),
            'type' => $account->getType(),
            'balance' => $account->getBalance(),
            'currency' => $account->getCurrency(),
            'description' => $account->getDescription(),
            'is_active' => $account->isActive() ? 1 : 0,
            'updated_at' => $account->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        return $this->database->update($query, $params) > 0;
    }

    /**
     * Actualizar solo el balance de una cuenta
     */
    public function updateBalance(int $accountId, float $newBalance): bool
    {
        $query = "
            UPDATE accounts SET 
                balance = :balance,
                updated_at = NOW()
            WHERE id = :id
        ";

        return $this->database->update($query, [
            'id' => $accountId,
            'balance' => $newBalance
        ]) > 0;
    }

    /**
     * Eliminar cuenta (soft delete)
     */
    public function delete(int $id): bool
    {
        $query = "
            UPDATE accounts SET 
                is_active = 0,
                updated_at = NOW()
            WHERE id = :id
        ";

        return $this->database->update($query, ['id' => $id]) > 0;
    }

    /**
     * Eliminar cuenta permanentemente
     */
    public function forceDelete(int $id): bool
    {
        $query = "DELETE FROM accounts WHERE id = :id";
        return $this->database->delete($query, ['id' => $id]) > 0;
    }

    /**
     * Verificar si el nombre de cuenta ya existe para el usuario
     */
    public function nameExists(string $name, int $userId, ?int $excludeId = null): bool
    {
        $query = "SELECT COUNT(*) as count FROM accounts WHERE name = :name AND user_id = :user_id";
        $params = ['name' => $name, 'user_id' => $userId];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $result = $this->database->selectOne($query, $params);
        return $result['count'] > 0;
    }

    /**
     * Obtener resumen de cuentas por usuario
     */
    public function getSummaryByUser(int $userId): array
    {
        $query = "
            SELECT 
                type,
                COUNT(*) as count,
                SUM(balance) as total_balance,
                currency
            FROM accounts 
            WHERE user_id = :user_id AND is_active = 1
            GROUP BY type, currency
            ORDER BY type ASC
        ";

        return $this->database->select($query, ['user_id' => $userId]);
    }

    /**
     * Obtener balance total por usuario
     */
    public function getTotalBalance(int $userId, ?string $currency = null): float
    {
        $query = "
            SELECT COALESCE(SUM(balance), 0) as total 
            FROM accounts 
            WHERE user_id = :user_id AND is_active = 1
        ";
        
        $params = ['user_id' => $userId];

        if ($currency) {
            $query .= " AND currency = :currency";
            $params['currency'] = $currency;
        }

        $result = $this->database->selectOne($query, $params);
        return (float) $result['total'];
    }

    /**
     * Obtener cuentas con paginación
     */
    public function getPaginated(int $userId, int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ['user_id = :user_id'];
        $params = ['user_id' => $userId];

        // Filtros
        if (!empty($filters['type'])) {
            $whereConditions[] = "type = :type";
            $params['type'] = $filters['type'];
        }

        if (isset($filters['is_active'])) {
            $whereConditions[] = "is_active = :is_active";
            $params['is_active'] = $filters['is_active'] ? 1 : 0;
        }

        if (!empty($filters['currency'])) {
            $whereConditions[] = "currency = :currency";
            $params['currency'] = $filters['currency'];
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = "(name LIKE :search OR description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Consulta principal
        $query = "
            SELECT * FROM accounts 
            {$whereClause}
            ORDER BY name ASC 
            LIMIT :limit OFFSET :offset
        ";

        $params['limit'] = $perPage;
        $params['offset'] = $offset;

        $results = $this->database->select($query, $params);
        $accounts = array_map(fn($row) => new Account($row), $results);

        // Contar total
        $countQuery = "SELECT COUNT(*) as total FROM accounts {$whereClause}";
        unset($params['limit'], $params['offset']);
        $totalResult = $this->database->selectOne($countQuery, $params);
        $total = $totalResult['total'];

        return [
            'data' => $accounts,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }

    /**
     * Obtener estadísticas de cuentas
     */
    public function getStats(int $userId): array
    {
        $query = "
            SELECT 
                COUNT(*) as total_accounts,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_accounts,
                SUM(CASE WHEN type = 'cash' THEN balance ELSE 0 END) as cash_balance,
                SUM(CASE WHEN type IN ('bank', 'savings', 'debit_card') THEN balance ELSE 0 END) as bank_balance,
                SUM(CASE WHEN type = 'credit_card' THEN balance ELSE 0 END) as credit_balance,
                SUM(CASE WHEN type = 'investment' THEN balance ELSE 0 END) as investment_balance,
                SUM(CASE WHEN is_active = 1 THEN balance ELSE 0 END) as total_balance
            FROM accounts
            WHERE user_id = :user_id
        ";

        return $this->database->selectOne($query, ['user_id' => $userId]) ?: [];
    }

    /**
     * Transferir dinero entre cuentas
     */
    public function transfer(int $fromAccountId, int $toAccountId, float $amount): bool
    {
        return $this->database->transaction(function($db) use ($fromAccountId, $toAccountId, $amount) {
            // Verificar cuenta origen
            $fromAccount = $this->findById($fromAccountId);
            if (!$fromAccount || !$fromAccount->hasSufficientBalance($amount)) {
                throw new Exception('Saldo insuficiente en cuenta origen');
            }

            // Verificar cuenta destino
            $toAccount = $this->findById($toAccountId);
            if (!$toAccount || !$toAccount->isActive()) {
                throw new Exception('Cuenta destino no válida');
            }

            // Actualizar balances
            $fromAccount->updateBalance(-$amount);
            $toAccount->updateBalance($amount);

            // Guardar cambios
            $this->update($fromAccount);
            $this->update($toAccount);

            return true;
        });
    }

    /**
     * Obtener cuentas por moneda
     */
    public function findByCurrency(int $userId, string $currency): array
    {
        $query = "
            SELECT * FROM accounts 
            WHERE user_id = :user_id AND currency = :currency AND is_active = 1
            ORDER BY name ASC
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'currency' => $currency
        ]);

        return array_map(fn($row) => new Account($row), $results);
    }
}
