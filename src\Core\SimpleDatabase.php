<?php

declare(strict_types=1);

namespace ControlGastos\Core;

use PDO;
use PDOException;
use Exception;

/**
 * Clase de base de datos simplificada para pruebas
 */
class SimpleDatabase
{
    private ?PDO $connection = null;
    private array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * Obtener conexión PDO
     */
    public function getConnection(): PDO
    {
        if ($this->connection === null) {
            $this->connect();
        }

        return $this->connection;
    }

    /**
     * Conectar a la base de datos
     */
    private function connect(): void
    {
        try {
            $dbConfig = $this->config['database']['connections']['mysql'];
            
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                $dbConfig['host'],
                $dbConfig['port'],
                $dbConfig['database'],
                $dbConfig['charset']
            );

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $this->connection = new PDO(
                $dsn,
                $dbConfig['username'],
                $dbConfig['password'],
                $options
            );

        } catch (PDOException $e) {
            throw new Exception("Error de conexión a la base de datos: " . $e->getMessage());
        }
    }

    /**
     * Ejecutar una consulta SELECT que devuelve un solo resultado
     */
    public function selectOne(string $query, array $params = []): ?array
    {
        $stmt = $this->getConnection()->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result ?: null;
    }

    /**
     * Ejecutar una consulta SELECT que devuelve múltiples resultados
     */
    public function select(string $query, array $params = []): array
    {
        $stmt = $this->getConnection()->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }

    /**
     * Ejecutar una consulta INSERT y devolver el ID insertado
     */
    public function insert(string $query, array $params = []): int
    {
        $stmt = $this->getConnection()->prepare($query);
        $stmt->execute($params);
        
        return (int) $this->getConnection()->lastInsertId();
    }

    /**
     * Ejecutar una consulta UPDATE o DELETE y devolver filas afectadas
     */
    public function execute(string $query, array $params = []): int
    {
        $stmt = $this->getConnection()->prepare($query);
        $stmt->execute($params);
        
        return $stmt->rowCount();
    }

    /**
     * Ejecutar una consulta directa (para SHOW TABLES, etc.)
     */
    public function query(string $query): \PDOStatement
    {
        return $this->getConnection()->query($query);
    }

    /**
     * Iniciar una transacción
     */
    public function beginTransaction(): bool
    {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * Confirmar una transacción
     */
    public function commit(): bool
    {
        return $this->getConnection()->commit();
    }

    /**
     * Revertir una transacción
     */
    public function rollback(): bool
    {
        return $this->getConnection()->rollback();
    }

    /**
     * Verificar si estamos en una transacción
     */
    public function inTransaction(): bool
    {
        return $this->getConnection()->inTransaction();
    }

    /**
     * Obtener el último ID insertado
     */
    public function lastInsertId(): string
    {
        return $this->getConnection()->lastInsertId();
    }

    /**
     * Preparar una consulta
     */
    public function prepare(string $query): \PDOStatement
    {
        return $this->getConnection()->prepare($query);
    }

    /**
     * Escapar un valor para uso en consultas
     */
    public function quote(string $value): string
    {
        return $this->getConnection()->quote($value);
    }

    /**
     * Obtener información de la conexión
     */
    public function getConnectionInfo(): array
    {
        $pdo = $this->getConnection();
        
        return [
            'driver' => $pdo->getAttribute(PDO::ATTR_DRIVER_NAME),
            'version' => $pdo->getAttribute(PDO::ATTR_SERVER_VERSION),
            'connection_status' => $pdo->getAttribute(PDO::ATTR_CONNECTION_STATUS),
        ];
    }

    /**
     * Verificar si una tabla existe
     */
    public function tableExists(string $tableName): bool
    {
        try {
            $stmt = $this->query("SHOW TABLES LIKE '{$tableName}'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Obtener la estructura de una tabla
     */
    public function getTableStructure(string $tableName): array
    {
        try {
            $stmt = $this->query("DESCRIBE {$tableName}");
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Obtener todas las tablas de la base de datos
     */
    public function getTables(): array
    {
        try {
            $stmt = $this->query("SHOW TABLES");
            $tables = [];
            
            while ($row = $stmt->fetch()) {
                $tables[] = array_values($row)[0];
            }
            
            return $tables;
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Ejecutar múltiples consultas (para migraciones)
     */
    public function executeMultiple(string $sql): bool
    {
        try {
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $this->getConnection()->exec($statement);
                }
            }
            
            return true;
        } catch (Exception $e) {
            throw new Exception("Error ejecutando múltiples consultas: " . $e->getMessage());
        }
    }

    /**
     * Cerrar la conexión
     */
    public function close(): void
    {
        $this->connection = null;
    }

    /**
     * Destructor - cerrar conexión
     */
    public function __destruct()
    {
        $this->close();
    }
}
