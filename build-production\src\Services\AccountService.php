<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Repositories\AccountRepository;
use ControlGastos\Models\Account;
use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de gestión de cuentas financieras
 * Maneja la lógica de negocio para cuentas
 */
class AccountService
{
    private AccountRepository $accountRepository;
    private Logger $logger;

    public function __construct(AccountRepository $accountRepository, Logger $logger)
    {
        $this->accountRepository = $accountRepository;
        $this->logger = $logger;
    }

    /**
     * Crear nueva cuenta
     */
    public function createAccount(array $data, int $userId): array
    {
        try {
            // Crear instancia de cuenta
            $account = new Account();
            $account->setUserId($userId);
            $account->setName($data['name']);
            $account->setType($data['type']);
            $account->setCurrency($data['currency'] ?? 'COP');
            $account->setDescription($data['description'] ?? null);
            
            // Establecer balance inicial si se proporciona
            if (isset($data['initial_balance'])) {
                $account->setBalance((float) $data['initial_balance']);
            }

            // Validar datos
            $errors = $account->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Verificar si el nombre ya existe
            if ($this->accountRepository->nameExists($account->getName(), $userId)) {
                return [
                    'success' => false,
                    'message' => 'Ya existe una cuenta con ese nombre'
                ];
            }

            // Crear cuenta
            $account = $this->accountRepository->create($account);

            $this->logger->info('Cuenta creada', [
                'account_id' => $account->getId(),
                'user_id' => $userId,
                'name' => $account->getName(),
                'type' => $account->getType()
            ]);

            return [
                'success' => true,
                'message' => 'Cuenta creada exitosamente',
                'account' => $account->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al crear cuenta: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Actualizar cuenta existente
     */
    public function updateAccount(int $accountId, array $data, int $userId): array
    {
        try {
            // Buscar cuenta
            $account = $this->accountRepository->findByIdAndUser($accountId, $userId);
            if (!$account) {
                return [
                    'success' => false,
                    'message' => 'Cuenta no encontrada'
                ];
            }

            // Actualizar datos
            if (isset($data['name'])) {
                $account->setName($data['name']);
            }
            
            if (isset($data['type'])) {
                $account->setType($data['type']);
            }
            
            if (isset($data['currency'])) {
                $account->setCurrency($data['currency']);
            }
            
            if (isset($data['description'])) {
                $account->setDescription($data['description']);
            }

            // Validar datos
            $errors = $account->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Verificar si el nombre ya existe (excluyendo la cuenta actual)
            if ($this->accountRepository->nameExists($account->getName(), $userId, $accountId)) {
                return [
                    'success' => false,
                    'message' => 'Ya existe una cuenta con ese nombre'
                ];
            }

            // Actualizar cuenta
            $this->accountRepository->update($account);

            $this->logger->info('Cuenta actualizada', [
                'account_id' => $account->getId(),
                'user_id' => $userId,
                'name' => $account->getName()
            ]);

            return [
                'success' => true,
                'message' => 'Cuenta actualizada exitosamente',
                'account' => $account->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al actualizar cuenta: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Eliminar cuenta
     */
    public function deleteAccount(int $accountId, int $userId): array
    {
        try {
            // Buscar cuenta
            $account = $this->accountRepository->findByIdAndUser($accountId, $userId);
            if (!$account) {
                return [
                    'success' => false,
                    'message' => 'Cuenta no encontrada'
                ];
            }

            // Verificar si la cuenta tiene transacciones
            if ($this->hasTransactions($accountId)) {
                // Solo desactivar si tiene transacciones
                $account->deactivate();
                $this->accountRepository->update($account);
                
                $message = 'Cuenta desactivada (tiene transacciones asociadas)';
            } else {
                // Eliminar permanentemente si no tiene transacciones
                $this->accountRepository->forceDelete($accountId);
                $message = 'Cuenta eliminada exitosamente';
            }

            $this->logger->info('Cuenta eliminada/desactivada', [
                'account_id' => $accountId,
                'user_id' => $userId,
                'name' => $account->getName()
            ]);

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al eliminar cuenta: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener cuenta por ID
     */
    public function getAccount(int $accountId, int $userId): array
    {
        try {
            $account = $this->accountRepository->findByIdAndUser($accountId, $userId);
            
            if (!$account) {
                return [
                    'success' => false,
                    'message' => 'Cuenta no encontrada'
                ];
            }

            return [
                'success' => true,
                'account' => $account->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener cuenta: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener todas las cuentas del usuario
     */
    public function getUserAccounts(int $userId, bool $activeOnly = true): array
    {
        try {
            $accounts = $this->accountRepository->findByUser($userId, $activeOnly);
            
            return [
                'success' => true,
                'accounts' => array_map(fn($account) => $account->toDetailedArray(), $accounts)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener cuentas: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener resumen financiero del usuario
     */
    public function getFinancialSummary(int $userId): array
    {
        try {
            $stats = $this->accountRepository->getStats($userId);
            $summary = $this->accountRepository->getSummaryByUser($userId);
            
            return [
                'success' => true,
                'summary' => [
                    'total_accounts' => $stats['total_accounts'],
                    'active_accounts' => $stats['active_accounts'],
                    'total_balance' => $stats['total_balance'],
                    'cash_balance' => $stats['cash_balance'],
                    'bank_balance' => $stats['bank_balance'],
                    'credit_balance' => $stats['credit_balance'],
                    'investment_balance' => $stats['investment_balance'],
                    'by_type' => $summary
                ]
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener resumen financiero: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Transferir dinero entre cuentas
     */
    public function transferBetweenAccounts(int $fromAccountId, int $toAccountId, float $amount, int $userId): array
    {
        try {
            // Validar amount
            if ($amount <= 0) {
                return [
                    'success' => false,
                    'message' => 'El monto debe ser mayor a cero'
                ];
            }

            // Verificar que ambas cuentas pertenezcan al usuario
            $fromAccount = $this->accountRepository->findByIdAndUser($fromAccountId, $userId);
            $toAccount = $this->accountRepository->findByIdAndUser($toAccountId, $userId);

            if (!$fromAccount || !$toAccount) {
                return [
                    'success' => false,
                    'message' => 'Una o ambas cuentas no fueron encontradas'
                ];
            }

            // Verificar que las cuentas estén activas
            if (!$fromAccount->isActive() || !$toAccount->isActive()) {
                return [
                    'success' => false,
                    'message' => 'Las cuentas deben estar activas para realizar transferencias'
                ];
            }

            // Verificar saldo suficiente
            if (!$fromAccount->hasSufficientBalance($amount)) {
                return [
                    'success' => false,
                    'message' => 'Saldo insuficiente en la cuenta origen'
                ];
            }

            // Realizar transferencia
            $this->accountRepository->transfer($fromAccountId, $toAccountId, $amount);

            $this->logger->info('Transferencia realizada', [
                'from_account_id' => $fromAccountId,
                'to_account_id' => $toAccountId,
                'amount' => $amount,
                'user_id' => $userId
            ]);

            return [
                'success' => true,
                'message' => 'Transferencia realizada exitosamente'
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en transferencia: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error al realizar la transferencia'
            ];
        }
    }

    /**
     * Activar/Desactivar cuenta
     */
    public function toggleAccountStatus(int $accountId, int $userId): array
    {
        try {
            $account = $this->accountRepository->findByIdAndUser($accountId, $userId);
            
            if (!$account) {
                return [
                    'success' => false,
                    'message' => 'Cuenta no encontrada'
                ];
            }

            // Cambiar estado
            if ($account->isActive()) {
                $account->deactivate();
                $message = 'Cuenta desactivada';
            } else {
                $account->activate();
                $message = 'Cuenta activada';
            }

            $this->accountRepository->update($account);

            $this->logger->info('Estado de cuenta cambiado', [
                'account_id' => $accountId,
                'user_id' => $userId,
                'new_status' => $account->isActive() ? 'active' : 'inactive'
            ]);

            return [
                'success' => true,
                'message' => $message,
                'account' => $account->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al cambiar estado de cuenta: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Verificar si una cuenta tiene transacciones
     */
    private function hasTransactions(int $accountId): bool
    {
        // Esta función se implementará cuando tengamos el repositorio de transacciones
        // Por ahora retornamos false
        return false;
    }

    /**
     * Obtener tipos de cuenta disponibles
     */
    public function getAccountTypes(): array
    {
        return Account::TYPES;
    }

    /**
     * Obtener monedas disponibles
     */
    public function getCurrencies(): array
    {
        return Account::CURRENCIES;
    }
}
