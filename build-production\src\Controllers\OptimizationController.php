<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Services\CacheService;
use ControlGastos\Services\DatabaseOptimizationService;
use ControlGastos\Services\PerformanceMonitorService;
use ControlGastos\Services\SecurityService;
use ControlGastos\Core\Session;

/**
 * Controlador de optimización y rendimiento
 * Maneja todas las rutas relacionadas con optimización y monitoreo
 */
class OptimizationController
{
    private Container $container;
    private CacheService $cacheService;
    private DatabaseOptimizationService $dbOptimizationService;
    private PerformanceMonitorService $performanceMonitorService;
    private SecurityService $securityService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->cacheService = $container->get('cacheService');
        $this->dbOptimizationService = $container->get('dbOptimizationService');
        $this->performanceMonitorService = $container->get('performanceMonitorService');
        $this->securityService = $container->get('securityService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Dashboard de optimización
     */
    public function index(): string
    {
        // Verificar permisos de administrador
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->session->flash('error', 'No tienes permisos para acceder a esta sección');
            header('Location: /dashboard');
            exit;
        }

        // Obtener métricas de performance
        $performanceReport = $this->performanceMonitorService->generatePerformanceReport();
        
        // Obtener estadísticas de cache
        $cacheStats = $this->cacheService->getStats();
        
        // Obtener estadísticas de base de datos
        $dbStats = $this->dbOptimizationService->getDatabaseStats();
        
        // Obtener análisis de consultas
        $queryAnalysis = $this->dbOptimizationService->analyzeQueryPerformance();

        $data = [
            'title' => 'Optimización y Rendimiento',
            'performance_report' => $performanceReport,
            'cache_stats' => $cacheStats,
            'db_stats' => $dbStats,
            'query_analysis' => $queryAnalysis,
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('optimization/index', $data);
    }

    /**
     * Monitoreo de performance en tiempo real
     */
    public function performance(): string
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->session->flash('error', 'No tienes permisos para acceder a esta sección');
            header('Location: /dashboard');
            exit;
        }

        // Obtener estadísticas históricas
        $historicalStats = $this->performanceMonitorService->getHistoricalStats(24);
        
        // Obtener métricas actuales
        $currentMetrics = $this->performanceMonitorService->getMetrics();

        $data = [
            'title' => 'Monitoreo de Performance',
            'historical_stats' => $historicalStats,
            'current_metrics' => $currentMetrics,
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('optimization/performance', $data);
    }

    /**
     * Gestión de cache
     */
    public function cache(): string
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->session->flash('error', 'No tienes permisos para acceder a esta sección');
            header('Location: /dashboard');
            exit;
        }

        $cacheStats = $this->cacheService->getStats();

        $data = [
            'title' => 'Gestión de Cache',
            'cache_stats' => $cacheStats,
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('optimization/cache', $data);
    }

    /**
     * Optimización de base de datos
     */
    public function database(): string
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->session->flash('error', 'No tienes permisos para acceder a esta sección');
            header('Location: /dashboard');
            exit;
        }

        $dbStats = $this->dbOptimizationService->getDatabaseStats();
        $indexAnalysis = $this->dbOptimizationService->analyzeIndexUsage();
        $queryAnalysis = $this->dbOptimizationService->analyzeQueryPerformance();

        $data = [
            'title' => 'Optimización de Base de Datos',
            'db_stats' => $dbStats,
            'index_analysis' => $indexAnalysis,
            'query_analysis' => $queryAnalysis,
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('optimization/database', $data);
    }

    /**
     * Limpiar cache
     */
    public function clearCache(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para esta acción'
            ], 403);
            return;
        }

        $type = $_POST['type'] ?? 'all';
        
        try {
            switch ($type) {
                case 'all':
                    $result = $this->cacheService->clear();
                    $message = 'Todo el cache ha sido limpiado';
                    break;
                case 'expired':
                    $cleared = $this->cacheService->clearExpired();
                    $result = true;
                    $message = "Se limpiaron {$cleared} entradas de cache expiradas";
                    break;
                case 'query':
                    $result = $this->dbOptimizationService->clearQueryCache();
                    $message = 'Cache de consultas limpiado';
                    break;
                default:
                    $result = false;
                    $message = 'Tipo de cache no válido';
            }

            // Auditar acción
            $this->securityService->auditUserAction(
                $this->userId,
                'clear_cache',
                'system',
                ['type' => $type]
            );

            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'success' => $result,
                    'message' => $message
                ]);
                return;
            }

            if ($result) {
                $this->session->flash('success', $message);
            } else {
                $this->session->flash('error', $message);
            }

        } catch (\Exception $e) {
            $message = 'Error al limpiar cache: ' . $e->getMessage();
            
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $message
                ]);
                return;
            }

            $this->session->flash('error', $message);
        }

        header('Location: /optimization/cache');
        exit;
    }

    /**
     * Optimizar base de datos
     */
    public function optimizeDatabase(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para esta acción'
            ], 403);
            return;
        }

        $action = $_POST['action'] ?? 'optimize_indexes';
        
        try {
            switch ($action) {
                case 'optimize_indexes':
                    $result = $this->dbOptimizationService->optimizeIndexes();
                    $message = 'Índices optimizados exitosamente';
                    break;
                case 'optimize_table':
                    $table = $_POST['table'] ?? '';
                    if (empty($table)) {
                        throw new \Exception('Nombre de tabla requerido');
                    }
                    $result = $this->dbOptimizationService->optimizeTable($table);
                    $message = "Tabla {$table} optimizada exitosamente";
                    break;
                default:
                    throw new \Exception('Acción no válida');
            }

            // Auditar acción
            $this->securityService->auditUserAction(
                $this->userId,
                'optimize_database',
                'system',
                ['action' => $action, 'table' => $_POST['table'] ?? null]
            );

            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $message,
                    'result' => $result
                ]);
                return;
            }

            $this->session->flash('success', $message);

        } catch (\Exception $e) {
            $message = 'Error en optimización: ' . $e->getMessage();
            
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $message
                ]);
                return;
            }

            $this->session->flash('error', $message);
        }

        header('Location: /optimization/database');
        exit;
    }

    /**
     * Obtener métricas en tiempo real (AJAX)
     */
    public function getRealTimeMetrics(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para esta acción'
            ], 403);
            return;
        }

        try {
            $metrics = $this->performanceMonitorService->getMetrics();
            $cacheStats = $this->cacheService->getStats();
            
            $this->jsonResponse([
                'success' => true,
                'timestamp' => time(),
                'performance_metrics' => $metrics,
                'cache_stats' => $cacheStats
            ]);

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error obteniendo métricas: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generar reporte de optimización
     */
    public function generateReport(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para esta acción'
            ], 403);
            return;
        }

        try {
            $report = [
                'timestamp' => date('Y-m-d H:i:s'),
                'performance' => $this->performanceMonitorService->generatePerformanceReport(),
                'cache' => $this->cacheService->getStats(),
                'database' => [
                    'stats' => $this->dbOptimizationService->getDatabaseStats(),
                    'query_analysis' => $this->dbOptimizationService->analyzeQueryPerformance(),
                    'index_analysis' => $this->dbOptimizationService->analyzeIndexUsage()
                ]
            ];

            // Auditar acción
            $this->securityService->auditUserAction(
                $this->userId,
                'generate_optimization_report',
                'system'
            );

            $filename = 'optimization_report_' . date('Y-m-d_H-i-s') . '.json';
            
            header('Content-Type: application/json; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            echo json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            exit;

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error generando reporte: ' . $e->getMessage());
            header('Location: /optimization');
            exit;
        }
    }

    /**
     * Configurar alertas de performance
     */
    public function configureAlerts(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_settings')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para esta acción'
            ], 403);
            return;
        }

        try {
            $config = [
                'memory_threshold' => (int) ($_POST['memory_threshold'] ?? 80),
                'response_time_threshold' => (float) ($_POST['response_time_threshold'] ?? 2.0),
                'disk_usage_threshold' => (int) ($_POST['disk_usage_threshold'] ?? 90),
                'enable_email_alerts' => isset($_POST['enable_email_alerts']),
                'alert_email' => $_POST['alert_email'] ?? ''
            ];

            // Guardar configuración en cache
            $this->cacheService->set('performance_alert_config', $config, 86400 * 30); // 30 días

            // Auditar acción
            $this->securityService->auditUserAction(
                $this->userId,
                'configure_performance_alerts',
                'system',
                $config
            );

            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => 'Configuración de alertas guardada'
                ]);
                return;
            }

            $this->session->flash('success', 'Configuración de alertas guardada');

        } catch (\Exception $e) {
            $message = 'Error guardando configuración: ' . $e->getMessage();
            
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $message
                ]);
                return;
            }

            $this->session->flash('error', $message);
        }

        header('Location: /optimization/performance');
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Verificar si es una request AJAX
     */
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Enviar respuesta JSON
     */
    private function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
