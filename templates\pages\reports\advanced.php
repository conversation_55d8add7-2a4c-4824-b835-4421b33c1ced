<?php
$content = ob_start();
?>

<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Reportes Avanzados</h1>
        <p class="text-muted mb-0">Análisis detallado de tus finanzas personales</p>
    </div>
    <div class="d-flex gap-2">
        <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-calendar"></i>
                <span class="d-none d-md-inline ms-1">Período</span>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?period=current_month">Mes Actual</a></li>
                <li><a class="dropdown-item" href="?period=last_month">Mes Anterior</a></li>
                <li><a class="dropdown-item" href="?period=quarter">Trimestre</a></li>
                <li><a class="dropdown-item" href="?period=year">Año</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#customPeriodModal">Personalizado</a></li>
            </ul>
        </div>
        <button class="btn btn-outline-info" onclick="exportReport()">
            <i class="fas fa-download"></i>
            <span class="d-none d-md-inline ms-1">Exportar</span>
        </button>
        <button class="btn btn-outline-success" onclick="scheduleReport()">
            <i class="fas fa-clock"></i>
            <span class="d-none d-md-inline ms-1">Programar</span>
        </button>
    </div>
</div>

<!-- Financial Health Score -->
<div class="row g-4 mb-4">
    <div class="col-lg-4">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat text-danger me-2"></i>
                    Salud Financiera
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="position-relative d-inline-block mb-3">
                    <canvas id="healthScoreChart" width="150" height="150"></canvas>
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <h2 class="mb-0 fw-bold" id="healthScoreValue">
                            <?= round($financial_health['overall_score'] ?? 0, 1) ?>
                        </h2>
                        <small class="text-muted">de 100</small>
                    </div>
                </div>
                <h6 class="mb-3 <?= $this->getHealthScoreClass($financial_health['overall_score'] ?? 0) ?>">
                    <?= htmlspecialchars($financial_health['rating'] ?? 'Sin datos') ?>
                </h6>
                
                <!-- Score Breakdown -->
                <div class="row g-2 text-start">
                    <?php if (!empty($financial_health['scores'])): ?>
                        <?php foreach ($financial_health['scores'] as $key => $score): ?>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted"><?= $this->getScoreLabel($key) ?></small>
                                    <small class="fw-semibold"><?= round($score, 1) ?></small>
                                </div>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar <?= $this->getScoreProgressClass($score) ?>" 
                                         style="width: <?= $score ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Alerts -->
    <div class="col-lg-8">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Alertas y Recomendaciones
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($alerts)): ?>
                    <div class="row g-3">
                        <?php foreach ($alerts as $alert): ?>
                            <div class="col-md-6">
                                <div class="alert alert-<?= $alert['type'] ?> mb-0">
                                    <h6 class="alert-heading mb-2">
                                        <i class="fas fa-<?= $this->getAlertIcon($alert['type']) ?> me-1"></i>
                                        <?= htmlspecialchars($alert['title']) ?>
                                    </h6>
                                    <p class="mb-2"><?= htmlspecialchars($alert['message']) ?></p>
                                    <small class="fw-semibold">💡 <?= htmlspecialchars($alert['action']) ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h6 class="text-success">¡Excelente!</h6>
                        <p class="text-muted mb-0">No hay alertas financieras en este momento</p>
                    </div>
                <?php endif; ?>

                <!-- Recommendations -->
                <?php if (!empty($financial_health['recommendations'])): ?>
                    <hr class="my-3">
                    <h6 class="mb-3">📋 Recomendaciones Personalizadas</h6>
                    <ul class="list-unstyled">
                        <?php foreach ($financial_health['recommendations'] as $recommendation): ?>
                            <li class="mb-2">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                <?= htmlspecialchars($recommendation) ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Trends and Predictions -->
<div class="row g-4 mb-4">
    <!-- Trend Analysis -->
    <div class="col-lg-8">
        <div class="card h-100">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        Análisis de Tendencias
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="trendPeriod" id="trend6months" checked>
                        <label class="btn btn-outline-primary" for="trend6months">6M</label>
                        
                        <input type="radio" class="btn-check" name="trendPeriod" id="trend1year">
                        <label class="btn btn-outline-primary" for="trend1year">1A</label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="trendsChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Predictions -->
    <div class="col-lg-4">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-crystal-ball text-info me-2"></i>
                    Predicciones
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($predictions['predictions'])): ?>
                    <?php foreach ($predictions['predictions'] as $prediction): ?>
                        <div class="mb-4">
                            <h6 class="mb-2"><?= date('F Y', strtotime($prediction['month'] . '-01')) ?></h6>
                            
                            <div class="row g-2 mb-2">
                                <div class="col-6">
                                    <div class="text-center p-2 bg-success bg-opacity-10 rounded">
                                        <small class="text-muted d-block">Ingresos</small>
                                        <span class="fw-bold text-success">
                                            $<?= number_format($prediction['predicted_income'], 0) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 bg-danger bg-opacity-10 rounded">
                                        <small class="text-muted d-block">Gastos</small>
                                        <span class="fw-bold text-danger">
                                            $<?= number_format($prediction['predicted_expenses'], 0) ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center p-2 bg-info bg-opacity-10 rounded">
                                <small class="text-muted d-block">Balance Proyectado</small>
                                <span class="fw-bold <?= $prediction['predicted_savings'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                    <?= $prediction['predicted_savings'] >= 0 ? '+' : '' ?>$<?= number_format($prediction['predicted_savings'], 0) ?>
                                </span>
                            </div>
                            
                            <div class="mt-2">
                                <small class="text-muted">
                                    Confianza: <?= round($prediction['confidence_level']) ?>%
                                </small>
                                <div class="progress mt-1" style="height: 3px;">
                                    <div class="progress-bar bg-info" style="width: <?= $prediction['confidence_level'] ?>%"></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-2x text-muted mb-3"></i>
                        <p class="text-muted mb-0">Datos insuficientes para predicciones</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Category Analysis and Spending Patterns -->
<div class="row g-4 mb-4">
    <!-- Category Analysis -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie text-warning me-2"></i>
                    Análisis por Categorías
                </h5>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" height="250"></canvas>
                
                <?php if (!empty($category_analysis['categories'])): ?>
                    <div class="mt-3">
                        <h6 class="mb-3">Top Categorías</h6>
                        <?php foreach (array_slice($category_analysis['categories'], 0, 5) as $category): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="rounded-circle me-2" 
                                          style="width: 12px; height: 12px; background-color: <?= htmlspecialchars($category['category_color']) ?>;"></span>
                                    <span><?= htmlspecialchars($category['category_name']) ?></span>
                                </div>
                                <div class="text-end">
                                    <span class="fw-bold">$<?= number_format($category['total_amount'], 0) ?></span>
                                    <small class="text-muted d-block"><?= round($category['percentage'], 1) ?>%</small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Spending Patterns -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock text-secondary me-2"></i>
                    Patrones de Gasto
                </h5>
            </div>
            <div class="card-body">
                <!-- Weekday Patterns -->
                <h6 class="mb-3">Por Día de la Semana</h6>
                <canvas id="weekdayChart" height="150"></canvas>
                
                <!-- Insights -->
                <?php if (!empty($spending_patterns['insights'])): ?>
                    <div class="mt-4">
                        <h6 class="mb-3">💡 Insights</h6>
                        <?php foreach ($spending_patterns['insights'] as $insight): ?>
                            <div class="alert alert-info py-2 mb-2">
                                <small><?= htmlspecialchars($insight) ?></small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Peak Times -->
                <div class="row g-2 mt-3">
                    <?php if (!empty($spending_patterns['peak_spending_day'])): ?>
                        <div class="col-6">
                            <div class="text-center p-2 bg-warning bg-opacity-10 rounded">
                                <small class="text-muted d-block">Día Pico</small>
                                <span class="fw-bold"><?= htmlspecialchars($spending_patterns['peak_spending_day']['day_name']) ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($spending_patterns['peak_spending_hour'])): ?>
                        <div class="col-6">
                            <div class="text-center p-2 bg-info bg-opacity-10 rounded">
                                <small class="text-muted d-block">Hora Pico</small>
                                <span class="fw-bold"><?= $spending_patterns['peak_spending_hour']['hour'] ?>:00</span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Goals Progress -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-target text-success me-2"></i>
                    Progreso de Metas Financieras
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($goals_progress)): ?>
                    <div class="row g-4">
                        <?php foreach ($goals_progress as $goalKey => $goal): ?>
                            <div class="col-lg-4">
                                <div class="text-center">
                                    <h6 class="mb-3"><?= $this->getGoalTitle($goalKey) ?></h6>
                                    
                                    <div class="position-relative d-inline-block mb-3">
                                        <canvas class="goal-chart" data-percentage="<?= $goal['percentage'] ?>" width="100" height="100"></canvas>
                                        <div class="position-absolute top-50 start-50 translate-middle">
                                            <span class="fw-bold"><?= round($goal['percentage']) ?>%</span>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <div class="mb-2">
                                            <span class="fw-bold"><?= $goal['unit'] === '$' ? '$' . number_format($goal['current'], 0) : round($goal['current'], 1) . $goal['unit'] ?></span>
                                            <span class="text-muted">de</span>
                                            <span class="fw-bold"><?= $goal['unit'] === '$' ? '$' . number_format($goal['target'], 0) : round($goal['target'], 1) . $goal['unit'] ?></span>
                                        </div>
                                        <span class="badge bg-<?= $this->getGoalStatusColor($goal['status']) ?>">
                                            <?= $this->getGoalStatusText($goal['status']) ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-target fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No hay metas configuradas</h6>
                        <p class="text-muted mb-3">Configura tus metas financieras para hacer seguimiento de tu progreso</p>
                        <a href="/goals/create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Crear Meta
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Custom Period Modal -->
<div class="modal fade" id="customPeriodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Período Personalizado</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customPeriodForm">
                    <div class="row g-3">
                        <div class="col-6">
                            <label for="startDate" class="form-label">Fecha Inicio</label>
                            <input type="date" class="form-control" id="startDate" name="start_date" required>
                        </div>
                        <div class="col-6">
                            <label for="endDate" class="form-label">Fecha Fin</label>
                            <input type="date" class="form-control" id="endDate" name="end_date" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="applyCustomPeriod()">Aplicar</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeAdvancedReports();
});

function initializeAdvancedReports() {
    // Initialize all charts
    initializeHealthScoreChart();
    initializeTrendsChart();
    initializeCategoryChart();
    initializeWeekdayChart();
    initializeGoalCharts();
}

function initializeHealthScoreChart() {
    const ctx = document.getElementById('healthScoreChart').getContext('2d');
    const score = <?= $financial_health['overall_score'] ?? 0 ?>;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [score, 100 - score],
                backgroundColor: [
                    getHealthScoreColor(score),
                    '#e9ecef'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: false,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function initializeTrendsChart() {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    const trends = <?= json_encode($trends ?? []) ?>;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: trends.months || [],
            datasets: [{
                label: 'Ingresos',
                data: trends.income_data || [],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }, {
                label: 'Gastos',
                data: trends.expense_data || [],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }, {
                label: 'Balance Neto',
                data: trends.net_data || [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

function initializeCategoryChart() {
    const ctx = document.getElementById('categoryChart').getContext('2d');
    const categories = <?= json_encode($category_analysis['categories'] ?? []) ?>;
    
    if (categories.length === 0) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: categories.map(c => c.category_name),
            datasets: [{
                data: categories.map(c => c.total_amount),
                backgroundColor: categories.map(c => c.category_color),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

function initializeWeekdayChart() {
    const ctx = document.getElementById('weekdayChart').getContext('2d');
    const patterns = <?= json_encode($spending_patterns['weekday_patterns'] ?? []) ?>;
    
    if (patterns.length === 0) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: patterns.map(p => p.day_name),
            datasets: [{
                label: 'Gastos',
                data: patterns.map(p => p.total_amount),
                backgroundColor: 'rgba(255, 193, 7, 0.8)',
                borderColor: '#ffc107',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

function initializeGoalCharts() {
    document.querySelectorAll('.goal-chart').forEach(canvas => {
        const ctx = canvas.getContext('2d');
        const percentage = parseFloat(canvas.dataset.percentage);
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [percentage, 100 - percentage],
                    backgroundColor: [
                        getGoalColor(percentage),
                        '#e9ecef'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    });
}

function getHealthScoreColor(score) {
    if (score >= 80) return '#28a745';
    if (score >= 60) return '#ffc107';
    return '#dc3545';
}

function getGoalColor(percentage) {
    if (percentage >= 100) return '#28a745';
    if (percentage >= 75) return '#17a2b8';
    if (percentage >= 50) return '#ffc107';
    return '#dc3545';
}

function exportReport() {
    // Implementar exportación
    showToast('Función de exportación en desarrollo', 'info');
}

function scheduleReport() {
    // Implementar programación de reportes
    showToast('Función de programación en desarrollo', 'info');
}

function applyCustomPeriod() {
    const form = document.getElementById('customPeriodForm');
    const formData = new FormData(form);
    
    const params = new URLSearchParams();
    params.append('period', 'custom');
    params.append('start_date', formData.get('start_date'));
    params.append('end_date', formData.get('end_date'));
    
    window.location.href = '?' + params.toString();
}
</script>

<?php
$content = ob_get_clean();

// Helper methods for the template
function getHealthScoreClass($score) {
    if ($score >= 80) return 'text-success';
    if ($score >= 60) return 'text-warning';
    return 'text-danger';
}

function getScoreLabel($key) {
    $labels = [
        'savings_rate_score' => 'Tasa de Ahorro',
        'expense_volatility_score' => 'Estabilidad',
        'trend_score' => 'Tendencias',
        'consistency_score' => 'Consistencia',
        'diversification_score' => 'Diversificación'
    ];
    return $labels[$key] ?? ucfirst(str_replace('_', ' ', $key));
}

function getScoreProgressClass($score) {
    if ($score >= 80) return 'bg-success';
    if ($score >= 60) return 'bg-warning';
    return 'bg-danger';
}

function getAlertIcon($type) {
    $icons = [
        'danger' => 'exclamation-triangle',
        'warning' => 'exclamation-circle',
        'info' => 'info-circle',
        'success' => 'check-circle'
    ];
    return $icons[$type] ?? 'info-circle';
}

function getGoalTitle($key) {
    $titles = [
        'savings_rate' => 'Tasa de Ahorro',
        'expense_limit' => 'Límite de Gastos',
        'emergency_fund' => 'Fondo de Emergencia'
    ];
    return $titles[$key] ?? ucfirst(str_replace('_', ' ', $key));
}

function getGoalStatusColor($status) {
    $colors = [
        'completed' => 'success',
        'on_track' => 'info',
        'behind' => 'warning',
        'critical' => 'danger'
    ];
    return $colors[$status] ?? 'secondary';
}

function getGoalStatusText($status) {
    $texts = [
        'completed' => 'Completado',
        'on_track' => 'En Progreso',
        'behind' => 'Atrasado',
        'critical' => 'Crítico'
    ];
    return $texts[$status] ?? 'Desconocido';
}

include __DIR__ . '/../../layouts/app.php';
?>
