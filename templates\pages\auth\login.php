<!DOCTYPE html>
<html lang="es" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar <PERSON><PERSON><PERSON> - <PERSON> de Gastos</title>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome 6 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        body {
            font-family: 'Inter', '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 2rem;
        }

        .login-left {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            min-height: 500px;
        }

        .login-right {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .brand-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .brand-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            text-align: left;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
        }

        .feature-list i {
            margin-right: 0.75rem;
            color: #38ef7d;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-group-text {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background-color: #f8fafc;
            border-right: none;
        }

        .input-group .form-control {
            border-left: none;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-outline-secondary {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background-color: #f8fafc;
            border-color: #cbd5e1;
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.25rem;
        }

        .alert-danger {
            background-color: #fef2f2;
            color: #991b1b;
            border-left: 4px solid #ef4444;
        }

        .alert-success {
            background-color: #ecfdf5;
            color: #065f46;
            border-left: 4px solid #10b981;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .text-decoration-none:hover {
            text-decoration: underline !important;
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
            }
            
            .login-left {
                padding: 2rem;
                min-height: auto;
            }
            
            .login-right {
                padding: 2rem;
            }
            
            .brand-title {
                font-size: 1.5rem;
            }
            
            .brand-logo {
                font-size: 2rem;
            }
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
    </style>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/img/favicon.ico">
</head>
<body>
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container">
        <div class="row g-0 h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex">
                <div class="login-left">
                    <div>
                        <div class="brand-logo">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <h1 class="brand-title">Control de Gastos</h1>
                        <p class="brand-subtitle">
                            Gestiona tus finanzas personales de manera inteligente y segura
                        </p>
                        
                        <ul class="feature-list">
                            <li>
                                <i class="fas fa-check-circle"></i>
                                <span>Seguimiento de ingresos y gastos</span>
                            </li>
                            <li>
                                <i class="fas fa-check-circle"></i>
                                <span>Reportes y análisis detallados</span>
                            </li>
                            <li>
                                <i class="fas fa-check-circle"></i>
                                <span>Recordatorios de pagos</span>
                            </li>
                            <li>
                                <i class="fas fa-check-circle"></i>
                                <span>Múltiples cuentas y categorías</span>
                            </li>
                            <li>
                                <i class="fas fa-check-circle"></i>
                                <span>Backup automático y seguridad</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="col-lg-6">
                <div class="login-right">
                    <!-- Mobile Brand -->
                    <div class="d-lg-none text-center mb-4">
                        <div class="brand-logo text-primary">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <h2 class="brand-title text-primary">Control de Gastos</h2>
                    </div>

                    <div class="mb-4">
                        <h3 class="fw-bold mb-2">Bienvenido de vuelta</h3>
                        <p class="text-muted">Ingresa tus credenciales para acceder a tu cuenta</p>
                    </div>

                    <!-- Flash Messages -->
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($success)): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= htmlspecialchars($success) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Login Form -->
                    <form method="POST" action="/login" id="loginForm">
                        <div class="mb-3">
                            <label for="email" class="form-label fw-semibold">Correo Electrónico</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope text-muted"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="<EMAIL>" required 
                                       value="<?= htmlspecialchars($email ?? '') ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label fw-semibold">Contraseña</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock text-muted"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="Tu contraseña" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        Recordarme
                                    </label>
                                </div>
                            </div>
                            <div class="col-6 text-end">
                                <a href="/forgot-password" class="text-decoration-none">
                                    ¿Olvidaste tu contraseña?
                                </a>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mb-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Iniciar Sesión
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="text-muted mb-0">
                                ¿No tienes una cuenta? 
                                <a href="/register" class="text-decoration-none fw-semibold">
                                    Regístrate aquí
                                </a>
                            </p>
                        </div>
                    </form>

                    <!-- Demo Account -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="fw-semibold mb-2">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            Cuenta de Demostración
                        </h6>
                        <p class="small text-muted mb-2">
                            Prueba la aplicación con datos de ejemplo:
                        </p>
                        <div class="row g-2">
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="fillDemoCredentials()">
                                    <i class="fas fa-user me-1"></i>
                                    Demo Usuario
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="fillAdminCredentials()">
                                    <i class="fas fa-user-shield me-1"></i>
                                    Demo Admin
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginForm();
        });

        function initializeLoginForm() {
            // Toggle password visibility
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');

            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });

            // Form submission
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Iniciando sesión...';
                submitBtn.disabled = true;
                
                // Re-enable button after 5 seconds as fallback
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            });

            // Auto-focus email field
            document.getElementById('email').focus();
        }

        function fillDemoCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'demo123';
        }

        function fillAdminCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'admin123';
        }

        // Add floating animation to shapes
        document.querySelectorAll('.shape').forEach((shape, index) => {
            shape.style.animationDelay = `${index * 2}s`;
        });
    </script>
</body>
</html>
