<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Core\Database;
use ControlGastos\Core\Logger;
use ControlGastos\Core\Session;
use Exception;

/**
 * Servicio de seguridad
 * Maneja autenticación, autorización, auditoría y protección de datos
 */
class SecurityService
{
    private Database $database;
    private Logger $logger;
    private Session $session;
    private array $config;

    public function __construct(Database $database, Logger $logger, Session $session)
    {
        $this->database = $database;
        $this->logger = $logger;
        $this->session = $session;
        $this->config = [
            'max_login_attempts' => 5,
            'lockout_duration' => 900, // 15 minutos
            'session_timeout' => 3600, // 1 hora
            'password_min_length' => 8,
            'require_2fa' => false
        ];
    }

    /**
     * Verificar intentos de login
     */
    public function checkLoginAttempts(string $email, string $ip): array
    {
        try {
            // Verificar intentos por email
            $emailAttempts = $this->getFailedAttempts('email', $email);
            
            // Verificar intentos por IP
            $ipAttempts = $this->getFailedAttempts('ip', $ip);

            $isLocked = $emailAttempts >= $this->config['max_login_attempts'] || 
                       $ipAttempts >= $this->config['max_login_attempts'];

            if ($isLocked) {
                $lockoutEnd = $this->getLockoutEndTime($email, $ip);
                
                return [
                    'allowed' => false,
                    'locked' => true,
                    'attempts_remaining' => 0,
                    'lockout_end' => $lockoutEnd,
                    'message' => 'Cuenta bloqueada por múltiples intentos fallidos'
                ];
            }

            $attemptsRemaining = $this->config['max_login_attempts'] - max($emailAttempts, $ipAttempts);

            return [
                'allowed' => true,
                'locked' => false,
                'attempts_remaining' => $attemptsRemaining,
                'email_attempts' => $emailAttempts,
                'ip_attempts' => $ipAttempts
            ];

        } catch (Exception $e) {
            $this->logger->error('Error verificando intentos de login: ' . $e->getMessage());
            return [
                'allowed' => false,
                'error' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Registrar intento de login fallido
     */
    public function recordFailedLogin(string $email, string $ip, string $userAgent = ''): void
    {
        try {
            $query = "
                INSERT INTO login_attempts (email, ip_address, user_agent, success, attempted_at)
                VALUES (:email, :ip, :user_agent, 0, NOW())
            ";

            $this->database->insert($query, [
                'email' => $email,
                'ip' => $ip,
                'user_agent' => $userAgent
            ]);

            $this->logger->warning('Intento de login fallido', [
                'email' => $email,
                'ip' => $ip,
                'user_agent' => $userAgent
            ]);

        } catch (Exception $e) {
            $this->logger->error('Error registrando intento fallido: ' . $e->getMessage());
        }
    }

    /**
     * Registrar login exitoso
     */
    public function recordSuccessfulLogin(int $userId, string $email, string $ip, string $userAgent = ''): void
    {
        try {
            // Registrar intento exitoso
            $query = "
                INSERT INTO login_attempts (email, ip_address, user_agent, success, user_id, attempted_at)
                VALUES (:email, :ip, :user_agent, 1, :user_id, NOW())
            ";

            $this->database->insert($query, [
                'email' => $email,
                'ip' => $ip,
                'user_agent' => $userAgent,
                'user_id' => $userId
            ]);

            // Limpiar intentos fallidos anteriores
            $this->clearFailedAttempts($email, $ip);

            // Actualizar última actividad del usuario
            $this->updateUserLastActivity($userId, $ip);

            $this->logger->info('Login exitoso', [
                'user_id' => $userId,
                'email' => $email,
                'ip' => $ip
            ]);

        } catch (Exception $e) {
            $this->logger->error('Error registrando login exitoso: ' . $e->getMessage());
        }
    }

    /**
     * Validar fortaleza de contraseña
     */
    public function validatePasswordStrength(string $password): array
    {
        $errors = [];
        $score = 0;

        // Longitud mínima
        if (strlen($password) < $this->config['password_min_length']) {
            $errors[] = "La contraseña debe tener al menos {$this->config['password_min_length']} caracteres";
        } else {
            $score += 1;
        }

        // Contiene mayúsculas
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'La contraseña debe contener al menos una letra mayúscula';
        } else {
            $score += 1;
        }

        // Contiene minúsculas
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'La contraseña debe contener al menos una letra minúscula';
        } else {
            $score += 1;
        }

        // Contiene números
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'La contraseña debe contener al menos un número';
        } else {
            $score += 1;
        }

        // Contiene caracteres especiales
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'La contraseña debe contener al menos un carácter especial';
        } else {
            $score += 1;
        }

        // Verificar patrones comunes
        $commonPatterns = [
            '/123456/', '/password/', '/qwerty/', '/admin/', '/letmein/',
            '/welcome/', '/monkey/', '/dragon/', '/master/', '/shadow/'
        ];

        foreach ($commonPatterns as $pattern) {
            if (preg_match($pattern, strtolower($password))) {
                $errors[] = 'La contraseña contiene patrones comunes que son fáciles de adivinar';
                $score -= 1;
                break;
            }
        }

        $strength = 'Muy débil';
        if ($score >= 4) $strength = 'Fuerte';
        elseif ($score >= 3) $strength = 'Media';
        elseif ($score >= 2) $strength = 'Débil';

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'score' => max(0, $score),
            'strength' => $strength
        ];
    }

    /**
     * Generar token seguro
     */
    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Verificar token CSRF
     */
    public function verifyCsrfToken(string $token): bool
    {
        return hash_equals($this->session->getCsrfToken(), $token);
    }

    /**
     * Auditar acción del usuario
     */
    public function auditUserAction(int $userId, string $action, string $resource, array $details = []): void
    {
        try {
            $query = "
                INSERT INTO audit_log (user_id, action, resource, details, ip_address, user_agent, created_at)
                VALUES (:user_id, :action, :resource, :details, :ip, :user_agent, NOW())
            ";

            $this->database->insert($query, [
                'user_id' => $userId,
                'action' => $action,
                'resource' => $resource,
                'details' => json_encode($details),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

        } catch (Exception $e) {
            $this->logger->error('Error en auditoría: ' . $e->getMessage());
        }
    }

    /**
     * Obtener log de auditoría
     */
    public function getAuditLog(int $userId = null, int $limit = 100): array
    {
        try {
            $query = "
                SELECT al.*, u.name as user_name, u.email as user_email
                FROM audit_log al
                LEFT JOIN users u ON al.user_id = u.id
            ";

            $params = [];

            if ($userId) {
                $query .= " WHERE al.user_id = :user_id";
                $params['user_id'] = $userId;
            }

            $query .= " ORDER BY al.created_at DESC LIMIT :limit";
            $params['limit'] = $limit;

            $logs = $this->database->select($query, $params);

            // Decodificar detalles JSON
            foreach ($logs as &$log) {
                $log['details'] = json_decode($log['details'], true) ?: [];
                $log['created_at_formatted'] = date('d/m/Y H:i:s', strtotime($log['created_at']));
            }

            return [
                'success' => true,
                'logs' => $logs
            ];

        } catch (Exception $e) {
            $this->logger->error('Error obteniendo log de auditoría: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error al obtener log de auditoría'
            ];
        }
    }

    /**
     * Verificar permisos de usuario
     */
    public function checkUserPermission(int $userId, string $permission): bool
    {
        try {
            // Por ahora, implementación básica
            // En el futuro se puede expandir con roles y permisos más granulares
            
            $query = "SELECT is_admin FROM users WHERE id = :user_id";
            $user = $this->database->selectOne($query, ['user_id' => $userId]);

            if (!$user) {
                return false;
            }

            // Permisos básicos para todos los usuarios autenticados
            $basicPermissions = [
                'view_own_data', 'create_transaction', 'edit_own_transaction',
                'create_account', 'edit_own_account', 'create_category',
                'edit_own_category', 'create_reminder', 'edit_own_reminder'
            ];

            if (in_array($permission, $basicPermissions)) {
                return true;
            }

            // Permisos de administrador
            $adminPermissions = [
                'view_all_data', 'manage_users', 'system_backup',
                'view_audit_log', 'system_settings'
            ];

            if (in_array($permission, $adminPermissions)) {
                return (bool) $user['is_admin'];
            }

            return false;

        } catch (Exception $e) {
            $this->logger->error('Error verificando permisos: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Detectar actividad sospechosa
     */
    public function detectSuspiciousActivity(int $userId): array
    {
        try {
            $suspiciousActivities = [];

            // Verificar múltiples IPs en poco tiempo
            $query = "
                SELECT COUNT(DISTINCT ip_address) as ip_count
                FROM login_attempts 
                WHERE user_id = :user_id 
                AND success = 1 
                AND attempted_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ";

            $result = $this->database->selectOne($query, ['user_id' => $userId]);
            if ($result['ip_count'] > 3) {
                $suspiciousActivities[] = [
                    'type' => 'multiple_ips',
                    'description' => 'Múltiples IPs en la última hora',
                    'severity' => 'medium',
                    'count' => $result['ip_count']
                ];
            }

            // Verificar actividad fuera de horario normal
            $query = "
                SELECT COUNT(*) as night_logins
                FROM login_attempts 
                WHERE user_id = :user_id 
                AND success = 1 
                AND (HOUR(attempted_at) < 6 OR HOUR(attempted_at) > 23)
                AND attempted_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
            ";

            $result = $this->database->selectOne($query, ['user_id' => $userId]);
            if ($result['night_logins'] > 5) {
                $suspiciousActivities[] = [
                    'type' => 'unusual_hours',
                    'description' => 'Actividad frecuente en horarios inusuales',
                    'severity' => 'low',
                    'count' => $result['night_logins']
                ];
            }

            // Verificar transacciones inusuales
            $query = "
                SELECT COUNT(*) as large_transactions
                FROM transactions 
                WHERE user_id = :user_id 
                AND amount > (
                    SELECT AVG(amount) * 5 
                    FROM transactions 
                    WHERE user_id = :user_id 
                    AND created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
                )
                AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
            ";

            $result = $this->database->selectOne($query, ['user_id' => $userId]);
            if ($result['large_transactions'] > 0) {
                $suspiciousActivities[] = [
                    'type' => 'unusual_transactions',
                    'description' => 'Transacciones con montos inusuales',
                    'severity' => 'high',
                    'count' => $result['large_transactions']
                ];
            }

            return [
                'success' => true,
                'has_suspicious_activity' => !empty($suspiciousActivities),
                'activities' => $suspiciousActivities
            ];

        } catch (Exception $e) {
            $this->logger->error('Error detectando actividad sospechosa: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error al detectar actividad sospechosa'
            ];
        }
    }

    /**
     * Obtener intentos fallidos
     */
    private function getFailedAttempts(string $type, string $value): int
    {
        $column = $type === 'email' ? 'email' : 'ip_address';
        
        $query = "
            SELECT COUNT(*) as attempts
            FROM login_attempts 
            WHERE {$column} = :value 
            AND success = 0 
            AND attempted_at > DATE_SUB(NOW(), INTERVAL {$this->config['lockout_duration']} SECOND)
        ";

        $result = $this->database->selectOne($query, ['value' => $value]);
        return (int) $result['attempts'];
    }

    /**
     * Obtener tiempo de fin de bloqueo
     */
    private function getLockoutEndTime(string $email, string $ip): string
    {
        $query = "
            SELECT MAX(attempted_at) as last_attempt
            FROM login_attempts 
            WHERE (email = :email OR ip_address = :ip) 
            AND success = 0
        ";

        $result = $this->database->selectOne($query, ['email' => $email, 'ip' => $ip]);
        
        if ($result['last_attempt']) {
            $lockoutEnd = new \DateTime($result['last_attempt']);
            $lockoutEnd->modify("+{$this->config['lockout_duration']} seconds");
            return $lockoutEnd->format('Y-m-d H:i:s');
        }

        return date('Y-m-d H:i:s');
    }

    /**
     * Limpiar intentos fallidos
     */
    private function clearFailedAttempts(string $email, string $ip): void
    {
        $query = "
            DELETE FROM login_attempts 
            WHERE (email = :email OR ip_address = :ip) 
            AND success = 0
        ";

        $this->database->delete($query, ['email' => $email, 'ip' => $ip]);
    }

    /**
     * Actualizar última actividad del usuario
     */
    private function updateUserLastActivity(int $userId, string $ip): void
    {
        $query = "
            UPDATE users 
            SET last_login = NOW(), last_ip = :ip 
            WHERE id = :user_id
        ";

        $this->database->update($query, ['user_id' => $userId, 'ip' => $ip]);
    }
}
