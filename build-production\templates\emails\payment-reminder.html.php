<?php
$content = ob_start();

// Determinar urgencia y estilo
$urgency_class = 'card-info';
$urgency_icon = '🔔';
$urgency_text = 'Recordatorio';

if ($days_until_due <= 0) {
    $urgency_class = 'card-danger';
    $urgency_icon = '🚨';
    $urgency_text = '¡VENCIDO!';
} elseif ($days_until_due <= 1) {
    $urgency_class = 'card-warning';
    $urgency_icon = '⚠️';
    $urgency_text = '¡Urgente!';
} elseif ($days_until_due <= 3) {
    $urgency_class = 'card-warning';
    $urgency_icon = '⏰';
    $urgency_text = 'Próximo a vencer';
}
?>

<h2><?= $urgency_icon ?> <?= $urgency_text ?> - Recordatorio de Pago</h2>

<p>Hola <strong><?= htmlspecialchars($user_name) ?></strong>,</p>

<?php if ($days_until_due <= 0): ?>
    <p>
        <strong style="color: #dc3545;">
            Tu recordatorio de pago ha vencido. Te recomendamos que realices el pago lo antes posible 
            para evitar recargos o penalizaciones.
        </strong>
    </p>
<?php elseif ($days_until_due <= 1): ?>
    <p>
        <strong style="color: #fd7e14;">
            Tu recordatorio de pago vence <?= $days_until_due === 0 ? 'hoy' : 'mañana' ?>. 
            Es importante que realices el pago a tiempo.
        </strong>
    </p>
<?php else: ?>
    <p>
        Te recordamos que tienes un pago próximo a vencer en <strong><?= $days_until_due ?> días</strong>.
    </p>
<?php endif; ?>

<div class="card <?= $urgency_class ?>">
    <h3>📋 Detalles del Recordatorio</h3>
    
    <table class="table">
        <tr>
            <td><strong>Concepto:</strong></td>
            <td><?= htmlspecialchars($reminder_title) ?></td>
        </tr>
        <?php if (!empty($reminder_description)): ?>
        <tr>
            <td><strong>Descripción:</strong></td>
            <td><?= htmlspecialchars($reminder_description) ?></td>
        </tr>
        <?php endif; ?>
        <tr>
            <td><strong>Fecha de vencimiento:</strong></td>
            <td>
                <strong><?= htmlspecialchars($due_date) ?></strong>
                <?php if ($days_until_due <= 0): ?>
                    <span style="color: #dc3545;">(Vencido)</span>
                <?php elseif ($days_until_due <= 3): ?>
                    <span style="color: #fd7e14;">(<?= $days_until_due ?> día<?= $days_until_due !== 1 ? 's' : '' ?>)</span>
                <?php endif; ?>
            </td>
        </tr>
        <?php if (!empty($amount)): ?>
        <tr>
            <td><strong>Monto:</strong></td>
            <td><strong style="font-size: 18px; color: #dc3545;"><?= htmlspecialchars($amount) ?></strong></td>
        </tr>
        <?php endif; ?>
    </table>
</div>

<?php if ($days_until_due <= 1): ?>
<div class="card card-warning">
    <h3>⚡ Acción Requerida</h3>
    <p>
        <strong>Este recordatorio requiere tu atención inmediata.</strong> 
        Te sugerimos que realices el pago hoy para evitar:
    </p>
    <ul>
        <li>Recargos por mora</li>
        <li>Intereses adicionales</li>
        <li>Afectación en tu historial crediticio</li>
        <li>Suspensión de servicios</li>
    </ul>
</div>
<?php endif; ?>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?= htmlspecialchars($dashboard_url) ?>" class="btn <?= $days_until_due <= 1 ? 'btn-danger' : 'btn' ?>">
        📊 Ver en Dashboard
    </a>
</div>

<div class="card">
    <h3>💡 Consejos para gestionar tus pagos</h3>
    <ul>
        <li><strong>Configura recordatorios múltiples:</strong> Crea alertas 7, 3 y 1 día antes del vencimiento</li>
        <li><strong>Automatiza pagos recurrentes:</strong> Configura débitos automáticos para servicios fijos</li>
        <li><strong>Mantén un fondo de emergencia:</strong> Ten siempre dinero disponible para pagos inesperados</li>
        <li><strong>Revisa tu calendario financiero:</strong> Planifica tus pagos al inicio de cada mes</li>
    </ul>
</div>

<?php if ($days_until_due > 1): ?>
<div class="card card-success">
    <h3>✅ ¿Ya realizaste este pago?</h3>
    <p>
        Si ya realizaste este pago, puedes marcar el recordatorio como completado en tu dashboard 
        para mantener tu registro actualizado.
    </p>
</div>
<?php endif; ?>

<h3>📱 Acciones rápidas desde tu dashboard:</h3>
<ul>
    <li>✅ Marcar recordatorio como completado</li>
    <li>📝 Registrar la transacción de pago</li>
    <li>⏰ Reprogramar el recordatorio</li>
    <li>🔄 Configurar recordatorio recurrente</li>
    <li>📊 Ver historial de pagos</li>
</ul>

<p>
    Recuerda que mantener tus pagos al día es fundamental para una buena salud financiera. 
    <?= htmlspecialchars($app_name) ?> está aquí para ayudarte a nunca olvidar un pago importante.
</p>

<p>
    Si tienes alguna pregunta sobre este recordatorio o necesitas ayuda con la aplicación, 
    no dudes en contactarnos.
</p>

<p>
    Saludos,<br>
    <strong>El equipo de <?= htmlspecialchars($app_name) ?></strong>
</p>

<hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">

<p style="font-size: 14px; color: #6c757d;">
    <strong>💡 Tip:</strong> Puedes configurar la frecuencia de estos recordatorios desde tu dashboard 
    en la sección de Configuración > Notificaciones.
</p>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.html.php';
?>
