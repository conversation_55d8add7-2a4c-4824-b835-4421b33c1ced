<?php
// La autenticación ya se verifica en el controlador
$cardInfo = $card['card'];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <span class="icon-emoji me-2">💳</span><?= htmlspecialchars($cardInfo['card_name'] ?? 'Tarjeta sin nombre') ?>
                        <span class="badge bg-<?= $cardInfo['status'] === 'active' ? 'success' : ($cardInfo['status'] === 'blocked' ? 'warning' : 'danger') ?> ms-2">
                            <?php
                            $statusLabels = [
                                'active' => 'Activa',
                                'cancelled' => 'Cancelada',
                                'expired' => 'Vencida',
                                'blocked' => 'Bloqueada',
                                'pending' => 'Pendiente'
                            ];
                            echo $statusLabels[$cardInfo['status']] ?? ucfirst($cardInfo['status']);
                            ?>
                        </span>
                    </h1>
                    <p class="text-muted">
                        <?= htmlspecialchars($cardInfo['bank_name'] ?? 'Banco no especificado') ?>
                        <?php if (!empty($cardInfo['card_type'])): ?>
                        • <?= ucfirst(str_replace('_', ' ', $cardInfo['card_type'])) ?>
                        <?php endif; ?>
                        <?php if (!empty($cardInfo['card_number_last4'])): ?>
                        • **** <?= htmlspecialchars($cardInfo['card_number_last4'] ?? '0000') ?>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <?php if ($cardInfo['status'] === 'active'): ?>
                    <div class="dropdown">
                        <button class="btn btn-outline-warning dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-2"></i>Gestionar
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeCardStatus('<?= $cardInfo['id'] ?>', 'blocked')">
                                <i class="fas fa-lock me-2"></i>Bloquear Tarjeta
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeCardStatus('<?= $cardInfo['id'] ?>', 'cancelled')">
                                <i class="fas fa-times-circle me-2"></i>Cancelar Tarjeta
                            </a></li>
                        </ul>
                    </div>
                    <?php elseif ($cardInfo['status'] === 'blocked'): ?>
                    <button class="btn btn-outline-success" onclick="changeCardStatus('<?= $cardInfo['id'] ?>', 'active')">
                        <i class="fas fa-unlock me-2"></i>Reactivar
                    </button>
                    <?php endif; ?>

                    <a href="/controlGastos/public/?route=credit-cards/edit&id=<?= $cardInfo['id'] ?>" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Editar
                    </a>
                    <a href="/controlGastos/public/?route=credit-cards/reports&id=<?= $cardInfo['id'] ?>" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar me-2"></i>Reportes
                    </a>
                    <a href="/controlGastos/public/?route=credit-cards" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alertas -->
    <?php
    $alerts = [];

    // Verificar si la tarjeta está próxima a vencer
    $expiryDate = new DateTime($cardInfo['expiry_date']);
    $today = new DateTime();
    $daysToExpiry = $today->diff($expiryDate)->days;

    if ($cardInfo['status'] === 'active' && $daysToExpiry <= 30) {
        $alerts[] = [
            'type' => 'warning',
            'icon' => 'fas fa-exclamation-triangle',
            'message' => "Tu tarjeta vence en {$daysToExpiry} días ({$expiryDate->format('d/m/Y')}). Contacta a tu banco para renovarla."
        ];
    }

    // Verificar utilización alta del cupo
    if ($card['credit_utilization'] > 80) {
        $alerts[] = [
            'type' => 'danger',
            'icon' => 'fas fa-exclamation-circle',
            'message' => "Estás utilizando {$card['credit_utilization']}% de tu cupo. Considera hacer un pago para mejorar tu score crediticio."
        ];
    } elseif ($card['credit_utilization'] > 50) {
        $alerts[] = [
            'type' => 'warning',
            'icon' => 'fas fa-info-circle',
            'message' => "Estás utilizando {$card['credit_utilization']}% de tu cupo. Se recomienda mantenerlo bajo el 30%."
        ];
    }

    // Verificar próxima fecha de pago
    $nextPaymentDate = new DateTime($card['next_payment_date']);
    $daysToPayment = $today->diff($nextPaymentDate)->days;

    if ($daysToPayment <= 3 && $card['current_balance'] > 0) {
        $alerts[] = [
            'type' => 'info',
            'icon' => 'fas fa-calendar-alt',
            'message' => "Tu próximo pago vence en {$daysToPayment} días ({$nextPaymentDate->format('d/m/Y')})."
        ];
    }
    ?>

    <?php if (!empty($alerts)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <?php foreach ($alerts as $alert): ?>
            <div class="alert alert-<?= $alert['type'] ?> alert-dismissible fade show" role="alert">
                <i class="<?= $alert['icon'] ?> me-2"></i>
                <?= $alert['message'] ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Resumen Financiero -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Cupo Consumido</h5>
                    <h3 class="<?= $card['current_balance'] > 0 ? 'text-danger' : 'text-success' ?>">
                        $<?= number_format($card['current_balance'], 0) ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Cupo Total</h5>
                    <h3 class="text-primary">$<?= number_format($cardInfo['credit_limit'], 0) ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Disponible</h5>
                    <h3 class="text-success">$<?= number_format($card['available_credit'], 0) ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Utilización</h5>
                    <h3 class="<?= $card['credit_utilization'] > 80 ? 'text-danger' : ($card['credit_utilization'] > 50 ? 'text-warning' : 'text-success') ?>">
                        <?= number_format($card['credit_utilization'], 1) ?>%
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Barra de Utilización -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">Utilización del Cupo</h6>
                    <div class="progress mb-2" style="height: 20px;">
                        <div class="progress-bar <?= $card['credit_utilization'] > 80 ? 'bg-danger' : ($card['credit_utilization'] > 50 ? 'bg-warning' : 'bg-success') ?>" 
                             style="width: <?= min($card['credit_utilization'], 100) ?>%">
                            <?= number_format($card['credit_utilization'], 1) ?>%
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">$0</small>
                        <small class="text-muted">$<?= number_format($cardInfo['credit_limit'], 0) ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fechas Importantes -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title"><i class="fas fa-calendar-alt me-2"></i>Próximo Corte</h6>
                    <h4 class="text-primary"><?= date('d/m/Y', strtotime($card['next_cut_off_date'])) ?></h4>
                    <small class="text-muted">Día <?= $cardInfo['cut_off_day'] ?> de cada mes</small>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title"><i class="fas fa-clock me-2"></i>Fecha Límite de Pago</h6>
                    <h4 class="text-warning"><?= date('d/m/Y', strtotime($card['next_payment_date'])) ?></h4>
                    <small class="text-muted"><?= $cardInfo['payment_due_days'] ?> días después del corte</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones Rápidas -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Agregar Transacción
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="/controlGastos/public/?route=credit-cards/add-transaction">
                        <input type="hidden" name="credit_card_id" value="<?= $cardInfo['id'] ?>">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="amount" class="form-label">Monto *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" name="amount" required min="1" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="transaction_date" class="form-label">Fecha *</label>
                                <input type="date" class="form-control" name="transaction_date" value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Descripción *</label>
                            <input type="text" class="form-control" name="description" required placeholder="Ej: Compra en supermercado">
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="merchant" class="form-label">Comercio</label>
                                <input type="text" class="form-control" name="merchant" placeholder="Ej: Walmart">
                            </div>
                            <div class="col-md-6">
                                <label for="category_id" class="form-label">Categoría</label>
                                <select class="form-select" name="category_id">
                                    <option value="">Sin categoría</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="transaction_type" class="form-label">Tipo</label>
                                <select class="form-select" name="transaction_type">
                                    <option value="purchase">Compra</option>
                                    <option value="cash_advance">Avance en efectivo</option>
                                    <option value="fee">Comisión</option>
                                    <option value="interest">Interés</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="installments" class="form-label">Cuotas</label>
                                <select class="form-select" name="installments">
                                    <option value="1">Contado</option>
                                    <?php for ($i = 2; $i <= 36; $i++): ?>
                                    <option value="<?= $i ?>"><?= $i ?> cuotas</option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>Agregar Transacción
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>Registrar Pago
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="/controlGastos/public/?route=credit-cards/add-payment">
                        <input type="hidden" name="credit_card_id" value="<?= $cardInfo['id'] ?>">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="payment_amount" class="form-label">Monto *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" name="amount" required min="1" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="payment_date" class="form-label">Fecha *</label>
                                <input type="date" class="form-control" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="payment_type" class="form-label">Tipo de Pago</label>
                                <select class="form-select" name="payment_type">
                                    <option value="partial">Pago Parcial</option>
                                    <option value="minimum">Pago Mínimo</option>
                                    <option value="full">Pago Total</option>
                                    <option value="extra">Pago Extra</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="payment_method" class="form-label">Método</label>
                                <select class="form-select" name="payment_method">
                                    <option value="bank_transfer">Transferencia</option>
                                    <option value="online">Pago en línea</option>
                                    <option value="automatic_debit">Débito automático</option>
                                    <option value="cash">Efectivo</option>
                                    <option value="check">Cheque</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reference_number" class="form-label">Número de Referencia</label>
                            <input type="text" class="form-control" name="reference_number" placeholder="Número de confirmación">
                        </div>
                        
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-money-bill-wave me-2"></i>Registrar Pago
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Historial de Transacciones y Pagos -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Transacciones Recientes
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($transactions)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No hay transacciones registradas</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Fecha</th>
                                    <th>Descripción</th>
                                    <th>Comercio</th>
                                    <th>Categoría</th>
                                    <th class="text-end">Monto</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                <tr>
                                    <td><?= date('d/m/Y', strtotime($transaction['transaction_date'])) ?></td>
                                    <td><?= htmlspecialchars($transaction['description']) ?></td>
                                    <td><?= htmlspecialchars($transaction['merchant'] ?? '-') ?></td>
                                    <td>
                                        <?php if ($transaction['category_name']): ?>
                                        <span class="badge bg-secondary"><?= htmlspecialchars($transaction['category_name']) ?></span>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end text-danger">-$<?= number_format($transaction['amount'], 0) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>Pagos Recientes
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($payments)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No hay pagos registrados</p>
                    </div>
                    <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($payments as $payment): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">Pago <?= ucfirst($payment['payment_type']) ?></h6>
                                    <small class="text-muted"><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="text-success">+$<?= number_format($payment['amount'], 0) ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeCardStatus(cardId, newStatus) {
    const statusMessages = {
        'blocked': '¿Estás seguro de que quieres bloquear esta tarjeta?',
        'cancelled': '¿Estás seguro de que quieres cancelar esta tarjeta? Esta acción no se puede deshacer fácilmente.',
        'active': '¿Estás seguro de que quieres reactivar esta tarjeta?'
    };

    const statusLabels = {
        'blocked': 'Bloqueada',
        'cancelled': 'Cancelada',
        'active': 'Activa'
    };

    if (confirm(statusMessages[newStatus])) {
        // Crear formulario para enviar el cambio de estado
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/controlGastos/public/?route=credit-cards/change-status';

        const cardIdInput = document.createElement('input');
        cardIdInput.type = 'hidden';
        cardIdInput.name = 'card_id';
        cardIdInput.value = cardId;

        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;

        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'reason';
        reasonInput.value = `${statusLabels[newStatus]} por el usuario`;

        form.appendChild(cardIdInput);
        form.appendChild(statusInput);
        form.appendChild(reasonInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-refresh de alertas cada 5 minutos
setInterval(function() {
    // Solo refrescar si la página está visible
    if (!document.hidden) {
        location.reload();
    }
}, 300000); // 5 minutos
</script>
