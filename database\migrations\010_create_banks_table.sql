-- Migración 010: <PERSON>rear tabla de bancos/entidades financieras
-- Fecha: 2024-12-30
-- Descripción: Crear tabla para gestionar bancos y entidades financieras

-- Crear tabla de bancos
CREATE TABLE IF NOT EXISTS banks (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    bank_code VARCHAR(10) NULL,
    bank_type ENUM('commercial', 'cooperative', 'investment', 'digital', 'other') NOT NULL DEFAULT 'commercial',
    country VARCHAR(3) NOT NULL DEFAULT 'COL',
    website VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    email VARCHAR(100) NULL,
    logo_url VARCHAR(255) NULL,
    color VARCHAR(7) NOT NULL DEFAULT '#007bff',
    description TEXT NULL,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_bank_name (bank_name),
    INDEX idx_is_active (is_active),
    UNIQUE KEY unique_user_bank (user_id, bank_name),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertar bancos principales de Colombia
INSERT INTO banks (user_id, bank_name, bank_code, bank_type, country, website, color, description) VALUES
(1, 'Bancolombia', '007', 'commercial', 'COL', 'https://www.bancolombia.com', '#FFD700', 'Banco líder en Colombia'),
(1, 'Banco de Bogotá', '001', 'commercial', 'COL', 'https://www.bancodebogota.com', '#0066CC', 'Banco tradicional colombiano'),
(1, 'Banco Popular', '002', 'commercial', 'COL', 'https://www.bancopopular.com.co', '#FF6600', 'Banco Popular de Colombia'),
(1, 'BBVA Colombia', '013', 'commercial', 'COL', 'https://www.bbva.com.co', '#004481', 'BBVA Banco Internacional'),
(1, 'Banco Davivienda', '051', 'commercial', 'COL', 'https://www.davivienda.com', '#ED1C24', 'Banco Davivienda S.A.'),
(1, 'Banco de Occidente', '023', 'commercial', 'COL', 'https://www.bancodeoccidente.com.co', '#00A651', 'Banco de Occidente'),
(1, 'Banco Caja Social', '032', 'commercial', 'COL', 'https://www.bancocajasocial.com', '#8B4513', 'Banco Caja Social BCSC'),
(1, 'Banco AV Villas', '052', 'commercial', 'COL', 'https://www.avvillas.com.co', '#FF0000', 'Banco AV Villas'),
(1, 'Banco Agrario', '040', 'commercial', 'COL', 'https://www.bancoagrario.gov.co', '#228B22', 'Banco Agrario de Colombia'),
(1, 'Banco Falabella', '062', 'commercial', 'COL', 'https://www.bancofalabella.com.co', '#00B4A6', 'Banco Falabella Colombia'),
(1, 'Nequi', 'NEQ', 'digital', 'COL', 'https://www.nequi.com.co', '#FF1744', 'Banco digital de Bancolombia'),
(1, 'Daviplata', 'DAV', 'digital', 'COL', 'https://www.daviplata.com', '#ED1C24', 'Billetera digital de Davivienda'),
(1, 'Banco Pichincha', '1012', 'commercial', 'COL', 'https://www.pichincha.com.co', '#FFD700', 'Banco Pichincha Colombia'),
(1, 'Banco Santander', '065', 'commercial', 'COL', 'https://www.santander.com.co', '#EC0000', 'Banco Santander Colombia'),
(1, 'Banco Itaú', '006', 'commercial', 'COL', 'https://www.itau.com.co', '#FF6600', 'Banco Itaú Colombia');

-- Agregar columna bank_id a bank_accounts
ALTER TABLE bank_accounts ADD COLUMN bank_id INT(11) NULL AFTER user_id;
ALTER TABLE bank_accounts ADD INDEX idx_bank_id (bank_id);
ALTER TABLE bank_accounts ADD FOREIGN KEY (bank_id) REFERENCES banks(id) ON DELETE SET NULL;

-- Agregar columna bank_id a credit_cards
ALTER TABLE credit_cards ADD COLUMN bank_id INT(11) NULL AFTER user_id;
ALTER TABLE credit_cards ADD INDEX idx_bank_id (bank_id);
ALTER TABLE credit_cards ADD FOREIGN KEY (bank_id) REFERENCES banks(id) ON DELETE SET NULL;

-- Actualizar registros existentes para asociarlos con bancos
UPDATE bank_accounts ba 
SET bank_id = (
    SELECT b.id 
    FROM banks b 
    WHERE b.bank_name = ba.bank_name 
    AND b.user_id = ba.user_id 
    LIMIT 1
) 
WHERE ba.bank_id IS NULL;

UPDATE credit_cards cc 
SET bank_id = (
    SELECT b.id 
    FROM banks b 
    WHERE b.bank_name = cc.bank_name 
    AND b.user_id = cc.user_id 
    LIMIT 1
) 
WHERE cc.bank_id IS NULL;
