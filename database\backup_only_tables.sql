-- Base de datos: `control_gastos`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `accounts`
--

CREATE TABLE `accounts` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` enum('cash','bank','credit_card','debit_card','savings','investment') NOT NULL,
  `balance` decimal(15,2) DEFAULT 0.00,
  `currency` varchar(3) DEFAULT 'COP',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `backups`
--

CREATE TABLE `backups` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `backup_type` enum('full','data_only','user_data') DEFAULT 'user_data',
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `banks`
--

CREATE TABLE `banks` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `bank_name` varchar(100) NOT NULL,
  `bank_code` varchar(10) DEFAULT NULL,
  `bank_type` enum('commercial','cooperative','investment','digital','other') NOT NULL DEFAULT 'commercial',
  `country` varchar(3) NOT NULL DEFAULT 'COL',
  `website` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `logo_url` varchar(255) DEFAULT NULL,
  `color` varchar(7) NOT NULL DEFAULT '#007bff',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `bank_accounts`
--

CREATE TABLE `bank_accounts` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `bank_id` int(11) DEFAULT NULL,
  `account_name` varchar(100) NOT NULL COMMENT 'Nombre personalizado de la cuenta',
  `bank_name` varchar(100) NOT NULL COMMENT 'Nombre del banco o entidad',
  `account_number` varchar(50) DEFAULT NULL COMMENT 'N├║mero de cuenta bancaria',
  `debit_card_number` varchar(20) DEFAULT NULL COMMENT '├Ültimos 4 d├¡gitos de tarjeta d├®bito',
  `account_type` enum('savings','checking','business') NOT NULL DEFAULT 'savings' COMMENT 'Tipo de cuenta',
  `initial_balance` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Saldo inicial al crear la cuenta',
  `current_balance` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Saldo actual calculado',
  `status` enum('active','inactive','blocked','closed') NOT NULL DEFAULT 'active',
  `description` text DEFAULT NULL COMMENT 'Descripci├│n adicional',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Cuentas bancarias y de d├®bito de los usuarios';

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `bank_account_movements`
--

CREATE TABLE `bank_account_movements` (
  `id` int(11) NOT NULL,
  `bank_account_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `movement_type` enum('deposit','withdrawal','transfer_in','transfer_out','fee','interest') NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `description` varchar(255) NOT NULL,
  `reference` varchar(100) DEFAULT NULL COMMENT 'N├║mero de referencia o comprobante',
  `movement_date` date NOT NULL,
  `posting_date` date NOT NULL DEFAULT curdate(),
  `balance_after` decimal(15,2) NOT NULL COMMENT 'Saldo despu├®s del movimiento',
  `related_account_id` int(11) DEFAULT NULL COMMENT 'Cuenta relacionada en transferencias',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Movimientos de las cuentas bancarias (dep├│sitos, retiros, transferencias)';

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `color` varchar(7) DEFAULT '#007bff',
  `icon` varchar(50) DEFAULT 'fas fa-folder',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `commitments`
--

CREATE TABLE `commitments` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `subcategory_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `due_date` date NOT NULL,
  `frequency` enum('once','daily','weekly','monthly','quarterly','yearly') DEFAULT 'once',
  `is_recurring` tinyint(1) DEFAULT 0,
  `is_paid` tinyint(1) DEFAULT 0,
  `description` text DEFAULT NULL,
  `notification_enabled` tinyint(1) DEFAULT 1,
  `notification_days_before` int(11) DEFAULT 3,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `credit_cards`
--

CREATE TABLE `credit_cards` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `bank_id` int(11) DEFAULT NULL,
  `card_name` varchar(100) NOT NULL COMMENT 'Nombre personalizado de la tarjeta',
  `bank_name` varchar(100) NOT NULL COMMENT 'Nombre del banco emisor',
  `card_type` varchar(50) DEFAULT 'visa',
  `card_number_last4` varchar(4) DEFAULT NULL COMMENT 'Últimos 4 dígitos de la tarjeta',
  `credit_limit` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Cupo total de la tarjeta',
  `management_fee` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Cuota de manejo mensual de la tarjeta',
  `expiry_date` date NOT NULL COMMENT 'Fecha de expiración de la tarjeta',
  `cut_off_day` int(11) NOT NULL DEFAULT 1 COMMENT 'Día del mes de corte (1-31)',
  `payment_due_days` int(11) NOT NULL DEFAULT 20 COMMENT 'Días después del corte para pagar',
  `cvv` varchar(4) DEFAULT NULL COMMENT 'Código CCV (opcional, encriptado)',
  `description` text DEFAULT NULL COMMENT 'Descripción adicional de la tarjeta',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'Si la tarjeta está activa',
  `status` enum('active','cancelled','expired','blocked','pending') NOT NULL DEFAULT 'active' COMMENT 'Estado de la tarjeta: active, cancelled, expired, blocked, pending',
  `status_changed_at` timestamp NULL DEFAULT NULL COMMENT 'Fecha del ├║ltimo cambio de estado',
  `status_reason` varchar(255) DEFAULT NULL COMMENT 'Motivo del cambio de estado',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `credit_card_payments`
--

CREATE TABLE `credit_card_payments` (
  `id` int(11) NOT NULL,
  `credit_card_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `bank_account_id` int(11) DEFAULT NULL COMMENT 'Cuenta bancaria desde donde se hizo el pago',
  `payment_type` enum('minimum','full','partial','extra') NOT NULL DEFAULT 'partial',
  `amount` decimal(15,2) NOT NULL COMMENT 'Monto del pago',
  `payment_date` date NOT NULL COMMENT 'Fecha del pago',
  `payment_method` enum('bank_transfer','cash','check','online','automatic_debit','other') DEFAULT 'bank_transfer',
  `reference_number` varchar(50) DEFAULT NULL COMMENT 'Número de referencia del pago',
  `cut_off_period` date DEFAULT NULL COMMENT 'Período de corte al que aplica el pago',
  `notes` text DEFAULT NULL COMMENT 'Notas del pago',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `credit_card_transactions`
--

CREATE TABLE `credit_card_transactions` (
  `id` int(11) NOT NULL,
  `credit_card_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `transaction_type` enum('purchase','cash_advance','fee','interest','adjustment','refund') NOT NULL DEFAULT 'purchase',
  `amount` decimal(15,2) NOT NULL COMMENT 'Monto de la transacción (positivo para cargos)',
  `description` varchar(255) NOT NULL COMMENT 'Descripción de la transacción',
  `merchant` varchar(150) DEFAULT NULL COMMENT 'Comercio donde se realizó',
  `category_id` int(11) DEFAULT NULL COMMENT 'Categoría de gasto',
  `transaction_date` date NOT NULL COMMENT 'Fecha de la transacción',
  `posting_date` date DEFAULT NULL COMMENT 'Fecha de procesamiento',
  `reference_number` varchar(50) DEFAULT NULL COMMENT 'Número de referencia',
  `installments` int(11) DEFAULT 1 COMMENT 'Número de cuotas (1 = contado)',
  `installment_number` int(11) DEFAULT 1 COMMENT 'Número de cuota actual',
  `notes` text DEFAULT NULL COMMENT 'Notas adicionales',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `expense_account_links`
--

CREATE TABLE `expense_account_links` (
  `id` int(11) NOT NULL,
  `transaction_id` int(11) NOT NULL COMMENT 'ID de la transacci├│n de gasto',
  `bank_account_id` int(11) DEFAULT NULL COMMENT 'Cuenta bancaria usada',
  `credit_card_id` int(11) DEFAULT NULL COMMENT 'Tarjeta de cr├®dito usada',
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `commitment_id` int(11) DEFAULT NULL,
  `type` enum('payment_reminder','system','warning','info') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `is_sent` tinyint(1) DEFAULT 0,
  `sent_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `reminders`
--

CREATE TABLE `reminders` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `amount` decimal(15,2) DEFAULT NULL,
  `due_date` date NOT NULL,
  `is_completed` tinyint(1) DEFAULT 0,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `subcategories`
--

CREATE TABLE `subcategories` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `transactions`
--

CREATE TABLE `transactions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `payment_method` enum('bank_account','credit_card') DEFAULT NULL COMMENT 'M├®todo de pago utilizado',
  `bank_account_id` int(11) DEFAULT NULL COMMENT 'Cuenta bancaria utilizada',
  `credit_card_id` int(11) DEFAULT NULL COMMENT 'Tarjeta de cr├®dito utilizada',
  `subcategory_id` int(11) DEFAULT NULL,
  `type` enum('income','expense','transfer') NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `description` varchar(255) NOT NULL,
  `transaction_date` date NOT NULL,
  `reference` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ;

--
-- Disparadores `transactions`
--
DELIMITER $$
CREATE TRIGGER `sync_unified_movements_transactions` AFTER INSERT ON `transactions` FOR EACH ROW BEGIN
    INSERT INTO unified_movements (
        user_id, movement_type, amount, description, movement_date,
        transaction_id, bank_account_id, credit_card_id
    ) VALUES (
        NEW.user_id, NEW.type, NEW.amount, NEW.description, NEW.transaction_date,
        NEW.id, NEW.bank_account_id, NEW.credit_card_id
    );
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `tr_transactions_delete_update_balance` AFTER DELETE ON `transactions` FOR EACH ROW BEGIN
    IF OLD.type = 'income' THEN
        UPDATE accounts 
        SET balance = balance - OLD.amount 
        WHERE id = OLD.account_id;
    ELSEIF OLD.type = 'expense' THEN
        UPDATE accounts 
        SET balance = balance + OLD.amount 
        WHERE id = OLD.account_id;
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `tr_transactions_insert_update_balance` AFTER INSERT ON `transactions` FOR EACH ROW BEGIN
    IF NEW.type = 'income' THEN
        UPDATE accounts 
        SET balance = balance + NEW.amount 
        WHERE id = NEW.account_id;
    ELSEIF NEW.type = 'expense' THEN
        UPDATE accounts 
        SET balance = balance - NEW.amount 
        WHERE id = NEW.account_id;
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `tr_transactions_update_update_balance` AFTER UPDATE ON `transactions` FOR EACH ROW BEGIN
    -- Revertir transacción anterior
    IF OLD.type = 'income' THEN
        UPDATE accounts 
        SET balance = balance - OLD.amount 
        WHERE id = OLD.account_id;
    ELSEIF OLD.type = 'expense' THEN
        UPDATE accounts 
        SET balance = balance + OLD.amount 
        WHERE id = OLD.account_id;
    END IF;
    
    -- Aplicar nueva transacción
    IF NEW.type = 'income' THEN
        UPDATE accounts 
        SET balance = balance + NEW.amount 
        WHERE id = NEW.account_id;
    ELSEIF NEW.type = 'expense' THEN
        UPDATE accounts 
        SET balance = balance - NEW.amount 
        WHERE id = NEW.account_id;
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `unified_movements`
--

CREATE TABLE `unified_movements` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `movement_type` enum('expense','income','credit_payment','bank_movement','credit_transaction') NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `description` varchar(255) NOT NULL,
  `movement_date` date NOT NULL,
  `transaction_id` int(11) DEFAULT NULL COMMENT 'ID de transacci├│n (gasto o ingreso)',
  `credit_payment_id` int(11) DEFAULT NULL COMMENT 'ID de pago de tarjeta',
  `bank_movement_id` int(11) DEFAULT NULL COMMENT 'ID de movimiento bancario',
  `credit_transaction_id` int(11) DEFAULT NULL COMMENT 'ID de transacci├│n de tarjeta',
  `bank_account_id` int(11) DEFAULT NULL,
  `credit_card_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Timeline unificado de todos los movimientos financieros';

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `two_factor_enabled` tinyint(1) DEFAULT 0,
  `two_factor_secret` varchar(32) DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verification_token` varchar(64) DEFAULT NULL,
  `password_reset_token` varchar(64) DEFAULT NULL,
  `password_reset_expires` datetime DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `user_sessions`
--

CREATE TABLE `user_sessions` (
  `session_id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text NOT NULL,
  `last_activity` datetime DEFAULT current_timestamp(),
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `v_commitments_summary`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `v_commitments_summary` (
`id` int(11)
,`user_id` int(11)
,`first_name` varchar(100)
,`last_name` varchar(100)
,`account_name` varchar(100)
,`category_name` varchar(100)
,`subcategory_name` varchar(100)
,`name` varchar(255)
,`amount` decimal(15,2)
,`due_date` date
,`frequency` enum('once','daily','weekly','monthly','quarterly','yearly')
,`is_recurring` tinyint(1)
,`is_paid` tinyint(1)
,`description` text
,`notification_enabled` tinyint(1)
,`notification_days_before` int(11)
,`days_until_due` int(7)
,`created_at` datetime
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `v_transactions_summary`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `v_transactions_summary` (
`id` int(11)
,`user_id` int(11)
,`first_name` varchar(100)
,`last_name` varchar(100)
,`account_name` varchar(100)
,`account_type` enum('cash','bank','credit_card','debit_card','savings','investment')
,`category_name` varchar(100)
,`category_color` varchar(7)
,`category_icon` varchar(50)
,`subcategory_name` varchar(100)
,`type` enum('income','expense','transfer')
,`amount` decimal(15,2)
,`description` varchar(255)
,`transaction_date` date
,`reference` varchar(100)
,`notes` text
,`created_at` datetime
);

-- --------------------------------------------------------

--
-- Estructura para la vista `v_commitments_summary`
--
DROP TABLE IF EXISTS `v_commitments_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_commitments_summary`  AS SELECT `cm`.`id` AS `id`, `cm`.`user_id` AS `user_id`, `u`.`first_name` AS `first_name`, `u`.`last_name` AS `last_name`, `a`.`name` AS `account_name`, `c`.`name` AS `category_name`, `sc`.`name` AS `subcategory_name`, `cm`.`name` AS `name`, `cm`.`amount` AS `amount`, `cm`.`due_date` AS `due_date`, `cm`.`frequency` AS `frequency`, `cm`.`is_recurring` AS `is_recurring`, `cm`.`is_paid` AS `is_paid`, `cm`.`description` AS `description`, `cm`.`notification_enabled` AS `notification_enabled`, `cm`.`notification_days_before` AS `notification_days_before`, to_days(`cm`.`due_date`) - to_days(curdate()) AS `days_until_due`, `cm`.`created_at` AS `created_at` FROM ((((`commitments` `cm` join `users` `u` on(`cm`.`user_id` = `u`.`id`)) join `accounts` `a` on(`cm`.`account_id` = `a`.`id`)) join `categories` `c` on(`cm`.`category_id` = `c`.`id`)) left join `subcategories` `sc` on(`cm`.`subcategory_id` = `sc`.`id`)) ;

-- --------------------------------------------------------

--
-- Estructura para la vista `v_transactions_summary`
--
DROP TABLE IF EXISTS `v_transactions_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_transactions_summary`  AS SELECT `t`.`id` AS `id`, `t`.`user_id` AS `user_id`, `u`.`first_name` AS `first_name`, `u`.`last_name` AS `last_name`, `a`.`name` AS `account_name`, `a`.`type` AS `account_type`, `c`.`name` AS `category_name`, `c`.`color` AS `category_color`, `c`.`icon` AS `category_icon`, `sc`.`name` AS `subcategory_name`, `t`.`type` AS `type`, `t`.`amount` AS `amount`, `t`.`description` AS `description`, `t`.`transaction_date` AS `transaction_date`, `t`.`reference` AS `reference`, `t`.`notes` AS `notes`, `t`.`created_at` AS `created_at` FROM ((((`transactions` `t` join `users` `u` on(`t`.`user_id` = `u`.`id`)) join `accounts` `a` on(`t`.`account_id` = `a`.`id`)) join `categories` `c` on(`t`.`category_id` = `c`.`id`)) left join `subcategories` `sc` on(`t`.`subcategory_id` = `sc`.`id`)) ;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `accounts`
--
ALTER TABLE `accounts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indices de la tabla `backups`
--
ALTER TABLE `backups`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_backup_type` (`backup_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indices de la tabla `banks`
--
ALTER TABLE `banks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_bank` (`user_id`,`bank_name`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_bank_name` (`bank_name`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indices de la tabla `bank_accounts`
--
ALTER TABLE `bank_accounts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_bank_name` (`bank_name`),
  ADD KEY `idx_bank_id` (`bank_id`);

--
-- Indices de la tabla `bank_account_movements`
--
ALTER TABLE `bank_account_movements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_movement_date` (`movement_date`),
  ADD KEY `idx_movement_type` (`movement_type`),
  ADD KEY `idx_posting_date` (`posting_date`),
  ADD KEY `fk_movements_related_account` (`related_account_id`);

--
-- Indices de la tabla `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_category` (`user_id`,`name`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indices de la tabla `commitments`
--
ALTER TABLE `commitments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subcategory_id` (`subcategory_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_account_id` (`account_id`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_is_paid` (`is_paid`),
  ADD KEY `idx_is_recurring` (`is_recurring`),
  ADD KEY `idx_notification_enabled` (`notification_enabled`);

--
-- Indices de la tabla `credit_cards`
--
ALTER TABLE `credit_cards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_cut_off_day` (`cut_off_day`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_management_fee` (`management_fee`),
  ADD KEY `idx_bank_id` (`bank_id`);

--
-- Indices de la tabla `credit_card_payments`
--
ALTER TABLE `credit_card_payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_credit_card_id` (`credit_card_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_payment_date` (`payment_date`),
  ADD KEY `idx_payment_type` (`payment_type`),
  ADD KEY `idx_cut_off_period` (`cut_off_period`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`);

--
-- Indices de la tabla `credit_card_transactions`
--
ALTER TABLE `credit_card_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_credit_card_id` (`credit_card_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_transaction_date` (`transaction_date`),
  ADD KEY `idx_transaction_type` (`transaction_type`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_posting_date` (`posting_date`);

--
-- Indices de la tabla `expense_account_links`
--
ALTER TABLE `expense_account_links`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_transaction_id` (`transaction_id`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`),
  ADD KEY `idx_credit_card_id` (`credit_card_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- Indices de la tabla `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_commitment_id` (`commitment_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_is_sent` (`is_sent`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indices de la tabla `reminders`
--
ALTER TABLE `reminders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_due` (`user_id`,`due_date`),
  ADD KEY `idx_completed` (`is_completed`);

--
-- Indices de la tabla `subcategories`
--
ALTER TABLE `subcategories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_category_subcategory` (`category_id`,`name`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indices de la tabla `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_account_id` (`account_id`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_subcategory_id` (`subcategory_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_transaction_date` (`transaction_date`),
  ADD KEY `idx_amount` (`amount`),
  ADD KEY `idx_user_date` (`user_id`,`transaction_date`),
  ADD KEY `fk_transactions_bank_account` (`bank_account_id`),
  ADD KEY `fk_transactions_credit_card` (`credit_card_id`);

--
-- Indices de la tabla `unified_movements`
--
ALTER TABLE `unified_movements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_movement_date` (`movement_date`),
  ADD KEY `idx_movement_type` (`movement_type`),
  ADD KEY `idx_bank_account` (`bank_account_id`),
  ADD KEY `idx_credit_card` (`credit_card_id`),
  ADD KEY `fk_unified_movements_transaction` (`transaction_id`),
  ADD KEY `fk_unified_movements_credit_payment` (`credit_payment_id`),
  ADD KEY `fk_unified_movements_bank_movement` (`bank_movement_id`),
  ADD KEY `fk_unified_movements_credit_transaction` (`credit_transaction_id`);

--
-- Indices de la tabla `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_email_verification` (`email_verification_token`),
  ADD KEY `idx_password_reset` (`password_reset_token`);

--
-- Indices de la tabla `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`session_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_last_activity` (`last_activity`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `accounts`
--
ALTER TABLE `accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `backups`
--
ALTER TABLE `backups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `banks`
--
ALTER TABLE `banks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `bank_accounts`
--
ALTER TABLE `bank_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `bank_account_movements`
--
ALTER TABLE `bank_account_movements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `commitments`
--
ALTER TABLE `commitments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `credit_cards`
--
ALTER TABLE `credit_cards`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `credit_card_payments`
--
ALTER TABLE `credit_card_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `credit_card_transactions`
--
ALTER TABLE `credit_card_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `expense_account_links`
--
ALTER TABLE `expense_account_links`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `reminders`
--
ALTER TABLE `reminders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `subcategories`
--
ALTER TABLE `subcategories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `unified_movements`
--
ALTER TABLE `unified_movements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `accounts`
--
ALTER TABLE `accounts`
  ADD CONSTRAINT `accounts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `backups`
--
ALTER TABLE `backups`
  ADD CONSTRAINT `backups_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `banks`
--
ALTER TABLE `banks`
  ADD CONSTRAINT `banks_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `bank_accounts`
--
ALTER TABLE `bank_accounts`
  ADD CONSTRAINT `bank_accounts_ibfk_1` FOREIGN KEY (`bank_id`) REFERENCES `banks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_bank_accounts_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `bank_account_movements`
--
ALTER TABLE `bank_account_movements`
  ADD CONSTRAINT `fk_movements_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_movements_related_account` FOREIGN KEY (`related_account_id`) REFERENCES `bank_accounts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_movements_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `commitments`
--
ALTER TABLE `commitments`
  ADD CONSTRAINT `commitments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `commitments_ibfk_2` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`),
  ADD CONSTRAINT `commitments_ibfk_3` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`),
  ADD CONSTRAINT `commitments_ibfk_4` FOREIGN KEY (`subcategory_id`) REFERENCES `subcategories` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `credit_cards`
--
ALTER TABLE `credit_cards`
  ADD CONSTRAINT `credit_cards_ibfk_1` FOREIGN KEY (`bank_id`) REFERENCES `banks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_credit_cards_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `credit_card_payments`
--
ALTER TABLE `credit_card_payments`
  ADD CONSTRAINT `fk_cc_payments_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_cc_payments_card` FOREIGN KEY (`credit_card_id`) REFERENCES `credit_cards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_cc_payments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `credit_card_transactions`
--
ALTER TABLE `credit_card_transactions`
  ADD CONSTRAINT `fk_cc_transactions_card` FOREIGN KEY (`credit_card_id`) REFERENCES `credit_cards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_cc_transactions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `expense_account_links`
--
ALTER TABLE `expense_account_links`
  ADD CONSTRAINT `fk_expense_links_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_expense_links_credit_card` FOREIGN KEY (`credit_card_id`) REFERENCES `credit_cards` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_expense_links_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`commitment_id`) REFERENCES `commitments` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `reminders`
--
ALTER TABLE `reminders`
  ADD CONSTRAINT `reminders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `subcategories`
--
ALTER TABLE `subcategories`
  ADD CONSTRAINT `subcategories_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `transactions`
--
ALTER TABLE `transactions`
  ADD CONSTRAINT `fk_transactions_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_transactions_credit_card` FOREIGN KEY (`credit_card_id`) REFERENCES `credit_cards` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `transactions_ibfk_2` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`),
  ADD CONSTRAINT `transactions_ibfk_3` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`),
  ADD CONSTRAINT `transactions_ibfk_4` FOREIGN KEY (`subcategory_id`) REFERENCES `subcategories` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `unified_movements`
--
ALTER TABLE `unified_movements`
  ADD CONSTRAINT `fk_unified_movements_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_unified_movements_bank_movement` FOREIGN KEY (`bank_movement_id`) REFERENCES `bank_account_movements` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_unified_movements_credit_card` FOREIGN KEY (`credit_card_id`) REFERENCES `credit_cards` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_unified_movements_credit_payment` FOREIGN KEY (`credit_payment_id`) REFERENCES `credit_card_payments` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_unified_movements_credit_transaction` FOREIGN KEY (`credit_transaction_id`) REFERENCES `credit_card_transactions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_unified_movements_transaction` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_unified_movements_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;