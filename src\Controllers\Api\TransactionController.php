<?php

declare(strict_types=1);

namespace ControlGastos\Controllers\Api;

use ControlGastos\Core\Container;
use ControlGastos\Services\TransactionService;
use ControlGastos\Core\Session;

/**
 * Controlador API de transacciones
 * Maneja endpoints REST para transacciones
 */
class TransactionController
{
    private Container $container;
    private TransactionService $transactionService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->transactionService = $container->get('transactionService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * GET /api/v1/transactions
     * Obtener todas las transacciones del usuario
     */
    public function index(): void
    {
        try {
            $filters = [
                'page' => (int) ($_GET['page'] ?? 1),
                'per_page' => min((int) ($_GET['per_page'] ?? 20), 100), // Máximo 100
                'type' => $_GET['type'] ?? '',
                'account_id' => $_GET['account_id'] ?? '',
                'category_id' => $_GET['category_id'] ?? '',
                'search' => $_GET['search'] ?? '',
                'start_date' => $_GET['start_date'] ?? '',
                'end_date' => $_GET['end_date'] ?? ''
            ];

            $result = $this->transactionService->getUserTransactions($this->userId, $filters);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['transactions'],
                    'pagination' => $result['pagination']
                ]);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ], 500);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/transactions/{id}
     * Obtener transacción específica
     */
    public function show(int $id): void
    {
        try {
            $result = $this->transactionService->getTransaction($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['transaction']
                ]);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => $result['message']
                ], 404);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * POST /api/v1/transactions
     * Crear nueva transacción
     */
    public function store(): void
    {
        try {
            $input = $this->getJsonInput();

            $data = [
                'account_id' => (int) ($input['account_id'] ?? 0),
                'category_id' => (int) ($input['category_id'] ?? 0),
                'subcategory_id' => !empty($input['subcategory_id']) ? (int) $input['subcategory_id'] : null,
                'type' => $input['type'] ?? '',
                'amount' => $input['amount'] ?? '',
                'description' => $input['description'] ?? '',
                'transaction_date' => $input['transaction_date'] ?? date('Y-m-d H:i:s'),
                'reference' => $input['reference'] ?? '',
                'notes' => $input['notes'] ?? '',
                'tags' => $input['tags'] ?? '',
                'is_recurring' => $input['is_recurring'] ?? false,
                'recurring_type' => $input['recurring_type'] ?? '',
                'recurring_interval' => $input['recurring_interval'] ?? 1,
                'recurring_end_date' => $input['recurring_end_date'] ?? ''
            ];

            $result = $this->transactionService->createTransaction($data, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['transaction']
                ], 201);
            } else {
                $statusCode = isset($result['errors']) ? 422 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * PUT /api/v1/transactions/{id}
     * Actualizar transacción existente
     */
    public function update(int $id): void
    {
        try {
            $input = $this->getJsonInput();

            $data = [
                'account_id' => (int) ($input['account_id'] ?? 0),
                'category_id' => (int) ($input['category_id'] ?? 0),
                'subcategory_id' => !empty($input['subcategory_id']) ? (int) $input['subcategory_id'] : null,
                'type' => $input['type'] ?? '',
                'amount' => $input['amount'] ?? '',
                'description' => $input['description'] ?? '',
                'transaction_date' => $input['transaction_date'] ?? '',
                'reference' => $input['reference'] ?? '',
                'notes' => $input['notes'] ?? '',
                'tags' => $input['tags'] ?? '',
                'is_recurring' => $input['is_recurring'] ?? false,
                'recurring_type' => $input['recurring_type'] ?? '',
                'recurring_interval' => $input['recurring_interval'] ?? 1,
                'recurring_end_date' => $input['recurring_end_date'] ?? ''
            ];

            $result = $this->transactionService->updateTransaction($id, $data, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['transaction']
                ]);
            } else {
                $statusCode = isset($result['errors']) ? 422 : 
                            ($result['message'] === 'Transacción no encontrada' ? 404 : 400);
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * DELETE /api/v1/transactions/{id}
     * Eliminar transacción
     */
    public function delete(int $id): void
    {
        try {
            $result = $this->transactionService->deleteTransaction($id, $this->userId);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $result['message']
                ]);
            } else {
                $statusCode = $result['message'] === 'Transacción no encontrada' ? 404 : 400;
                $this->jsonResponse($result, $statusCode);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/transactions/search
     * Buscar transacciones
     */
    public function search(): void
    {
        try {
            $search = $_GET['q'] ?? '';
            $filters = [
                'type' => $_GET['type'] ?? '',
                'account_id' => $_GET['account_id'] ?? '',
                'category_id' => $_GET['category_id'] ?? '',
                'start_date' => $_GET['start_date'] ?? '',
                'end_date' => $_GET['end_date'] ?? ''
            ];

            if (strlen($search) < 2) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'El término de búsqueda debe tener al menos 2 caracteres'
                ], 400);
                return;
            }

            $result = $this->transactionService->searchTransactions($this->userId, $search, $filters);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['transactions'],
                    'count' => $result['count']
                ]);
            } else {
                $this->jsonResponse($result, 500);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/transactions/stats
     * Obtener estadísticas de transacciones
     */
    public function stats(): void
    {
        try {
            $startDate = !empty($_GET['start_date']) ? new \DateTime($_GET['start_date']) : null;
            $endDate = !empty($_GET['end_date']) ? new \DateTime($_GET['end_date']) : null;

            $result = $this->transactionService->getTransactionStats($this->userId, $startDate, $endDate);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'data' => $result['stats']
                ]);
            } else {
                $this->jsonResponse($result, 500);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/transactions/types
     * Obtener tipos de transacción disponibles
     */
    public function types(): void
    {
        $this->jsonResponse([
            'success' => true,
            'data' => $this->transactionService->getTransactionTypes()
        ]);
    }

    /**
     * GET /api/v1/transactions/recurring-types
     * Obtener tipos de recurrencia disponibles
     */
    public function recurringTypes(): void
    {
        $this->jsonResponse([
            'success' => true,
            'data' => $this->transactionService->getRecurringTypes()
        ]);
    }

    /**
     * POST /api/v1/transactions/process-recurring
     * Procesar transacciones recurrentes
     */
    public function processRecurring(): void
    {
        try {
            $result = $this->transactionService->processRecurringTransactions();

            $this->jsonResponse([
                'success' => true,
                'message' => 'Transacciones recurrentes procesadas',
                'data' => [
                    'processed' => $result['processed'],
                    'errors' => $result['errors']
                ]
            ]);

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * GET /api/v1/transactions/export
     * Exportar transacciones
     */
    public function export(): void
    {
        try {
            $filters = [
                'type' => $_GET['type'] ?? '',
                'account_id' => $_GET['account_id'] ?? '',
                'category_id' => $_GET['category_id'] ?? '',
                'start_date' => $_GET['start_date'] ?? '',
                'end_date' => $_GET['end_date'] ?? ''
            ];

            $result = $this->transactionService->getUserTransactions($this->userId, $filters);
            
            if ($result['success']) {
                $filename = 'transacciones_' . date('Y-m-d_H-i-s') . '.json';
                
                header('Content-Type: application/json');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                
                echo json_encode([
                    'export_date' => date('Y-m-d H:i:s'),
                    'user_id' => $this->userId,
                    'filters' => $filters,
                    'transactions' => $result['transactions']
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Error al exportar transacciones'
                ], 500);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error interno del servidor'
            ], 500);
        }
        exit;
    }

    /**
     * Obtener input JSON del request
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('JSON inválido');
        }
        
        return $data ?: [];
    }

    /**
     * Enviar respuesta JSON
     */
    private function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
