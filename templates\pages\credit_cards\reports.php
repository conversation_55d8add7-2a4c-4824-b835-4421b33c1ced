<?php
// La autenticación ya se verifica en el controlador
$cardInfo = $card['card'];
$monthNames = [
    1 => 'Enero', 2 => 'Febrero', 3 => '<PERSON><PERSON>', 4 => 'Abril',
    5 => 'Mayo', 6 => 'Jun<PERSON>', 7 => 'Julio', 8 => 'Agosto',
    9 => 'Septiembre', 10 => 'Octubre', 11 => 'Noviembre', 12 => 'Diciembre'
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Reportes - <?= htmlspecialchars($cardInfo['card_name']) ?>
                    </h1>
                    <p class="text-muted"><?= htmlspecialchars($cardInfo['bank_name']) ?> • <?= $monthNames[$month] ?> <?= $year ?></p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/controlGastos/public/?route=credit-cards/show&id=<?= $cardInfo['id'] ?>" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>Ver Detalles
                    </a>
                    <a href="/controlGastos/public/?route=credit-cards" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="/public/" class="row g-3">
                        <input type="hidden" name="route" value="credit-cards/reports">
                        <input type="hidden" name="id" value="<?= $cardInfo['id'] ?>">
                        
                        <div class="col-md-4">
                            <label for="year" class="form-label">Año</label>
                            <select class="form-select" name="year" id="year">
                                <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                <option value="<?= $y ?>" <?= $y == $year ? 'selected' : '' ?>><?= $y ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="month" class="form-label">Mes</label>
                            <select class="form-select" name="month" id="month">
                                <?php foreach ($monthNames as $num => $name): ?>
                                <option value="<?= $num ?>" <?= $num == $month ? 'selected' : '' ?>><?= $name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-search me-2"></i>Filtrar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumen del Período -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Saldo Actual</h5>
                    <h3 class="<?= $card['current_balance'] > 0 ? 'text-danger' : 'text-success' ?>">
                        $<?= number_format($card['current_balance'], 0) ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Gastos del Mes</h5>
                    <h3 class="text-danger">
                        $<?= number_format($card['monthly_spending'], 0) ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Utilización</h5>
                    <h3 class="<?= $card['credit_utilization'] > 80 ? 'text-danger' : ($card['credit_utilization'] > 50 ? 'text-warning' : 'text-success') ?>">
                        <?= number_format($card['credit_utilization'], 1) ?>%
                    </h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-muted">Disponible</h5>
                    <h3 class="text-success">
                        $<?= number_format($card['available_credit'], 0) ?>
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas de Transacciones -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Estadísticas de Transacciones
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($monthly_stats)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Tipo</th>
                                    <th class="text-center">Cantidad</th>
                                    <th class="text-end">Total</th>
                                    <th class="text-end">Promedio</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($monthly_stats as $stat): ?>
                                <tr>
                                    <td>
                                        <?php
                                        $typeNames = [
                                            'purchase' => 'Compras',
                                            'cash_advance' => 'Avances',
                                            'fee' => 'Comisiones',
                                            'interest' => 'Intereses'
                                        ];
                                        echo $typeNames[$stat['transaction_type']] ?? ucfirst($stat['transaction_type']);
                                        ?>
                                    </td>
                                    <td class="text-center"><?= $stat['count'] ?></td>
                                    <td class="text-end">$<?= number_format($stat['total'], 0) ?></td>
                                    <td class="text-end">$<?= number_format($stat['average'], 0) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No hay transacciones en este período</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>Estadísticas de Pagos
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($payment_stats)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Tipo</th>
                                    <th>Método</th>
                                    <th class="text-center">Cantidad</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($payment_stats as $stat): ?>
                                <tr>
                                    <td>
                                        <?php
                                        $paymentTypes = [
                                            'minimum' => 'Mínimo',
                                            'full' => 'Total',
                                            'partial' => 'Parcial',
                                            'extra' => 'Extra'
                                        ];
                                        echo $paymentTypes[$stat['payment_type']] ?? ucfirst($stat['payment_type']);
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        $methods = [
                                            'bank_transfer' => 'Transferencia',
                                            'online' => 'En línea',
                                            'automatic_debit' => 'Débito automático',
                                            'cash' => 'Efectivo',
                                            'check' => 'Cheque'
                                        ];
                                        echo $methods[$stat['payment_method']] ?? ucfirst($stat['payment_method']);
                                        ?>
                                    </td>
                                    <td class="text-center"><?= $stat['count'] ?></td>
                                    <td class="text-end">$<?= number_format($stat['total'], 0) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No hay pagos en este período</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Gastos por Categoría -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tags me-2"></i>Gastos por Categoría
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($spending_by_category)): ?>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Categoría</th>
                                            <th class="text-center">Transacciones</th>
                                            <th class="text-end">Total</th>
                                            <th class="text-end">Porcentaje</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $totalSpending = array_sum(array_column($spending_by_category, 'total_amount'));
                                        foreach ($spending_by_category as $category): 
                                        ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($category['category_name'] ?? 'Sin categoría') ?></strong>
                                            </td>
                                            <td class="text-center"><?= $category['transaction_count'] ?></td>
                                            <td class="text-end">$<?= number_format($category['total_amount'], 0) ?></td>
                                            <td class="text-end">
                                                <?php if ($totalSpending > 0): ?>
                                                <?= number_format(($category['total_amount'] / $totalSpending) * 100, 1) ?>%
                                                <?php else: ?>
                                                0%
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>Distribución de Gastos</h6>
                                <canvas id="categoryChart" width="300" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No hay gastos categorizados en este período</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($spending_by_category)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de categorías
    const ctx = document.getElementById('categoryChart').getContext('2d');
    const categoryData = <?= json_encode($spending_by_category) ?>;
    
    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: categoryData.map(item => item.category_name || 'Sin categoría'),
            datasets: [{
                data: categoryData.map(item => item.total_amount),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
<?php endif; ?>
