<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Services\BackupService;
use ControlGastos\Services\SecurityService;
use ControlGastos\Core\Session;

/**
 * Controlador de backup y seguridad
 * <PERSON>eja todas las rutas relacionadas con backups y seguridad
 */
class BackupController
{
    private Container $container;
    private BackupService $backupService;
    private SecurityService $securityService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->backupService = $container->get('backupService');
        $this->securityService = $container->get('securityService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Dashboard de backup y seguridad
     */
    public function index(): string
    {
        // Verificar permisos
        if (!$this->securityService->checkUserPermission($this->userId, 'system_backup')) {
            $this->session->flash('error', 'No tienes permisos para acceder a esta sección');
            header('Location: /dashboard');
            exit;
        }

        // Obtener lista de backups
        $backupsResult = $this->backupService->listBackups();
        
        // Obtener actividad sospechosa
        $suspiciousActivity = $this->securityService->detectSuspiciousActivity($this->userId);
        
        // Obtener log de auditoría reciente
        $auditLog = $this->securityService->getAuditLog($this->userId, 10);

        $data = [
            'title' => 'Backup y Seguridad',
            'backups' => $backupsResult['success'] ? $backupsResult['backups'] : [],
            'suspicious_activity' => $suspiciousActivity['success'] ? $suspiciousActivity : null,
            'audit_log' => $auditLog['success'] ? $auditLog['logs'] : [],
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('backup/index', $data);
    }

    /**
     * Crear backup manual
     */
    public function createBackup(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_backup')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para crear backups'
            ], 403);
            return;
        }

        $type = $_POST['type'] ?? 'user';
        $targetUserId = $type === 'user' ? $this->userId : null;

        // Auditar acción
        $this->securityService->auditUserAction(
            $this->userId,
            'create_backup',
            'backup',
            ['type' => $type, 'target_user_id' => $targetUserId]
        );

        $result = $this->backupService->createFullBackup($targetUserId);

        if ($this->isAjaxRequest()) {
            $this->jsonResponse($result);
            return;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /backup');
        exit;
    }

    /**
     * Restaurar backup
     */
    public function restoreBackup(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_backup')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para restaurar backups'
            ], 403);
            return;
        }

        $backupId = (int) ($_POST['backup_id'] ?? 0);
        
        if (!$backupId) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'ID de backup requerido'
            ], 400);
            return;
        }

        // Auditar acción
        $this->securityService->auditUserAction(
            $this->userId,
            'restore_backup',
            'backup',
            ['backup_id' => $backupId]
        );

        $result = $this->backupService->restoreBackup($backupId, $this->userId);

        if ($this->isAjaxRequest()) {
            $this->jsonResponse($result);
            return;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /backup');
        exit;
    }

    /**
     * Eliminar backup
     */
    public function deleteBackup(int $id): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_backup')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para eliminar backups'
            ], 403);
            return;
        }

        // Auditar acción
        $this->securityService->auditUserAction(
            $this->userId,
            'delete_backup',
            'backup',
            ['backup_id' => $id]
        );

        $result = $this->backupService->deleteBackup($id, $this->userId);

        if ($this->isAjaxRequest()) {
            $this->jsonResponse($result);
            return;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /backup');
        exit;
    }

    /**
     * Verificar integridad de backup
     */
    public function verifyBackup(int $id): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_backup')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para verificar backups'
            ], 403);
            return;
        }

        $result = $this->backupService->verifyBackupIntegrity($id);

        $this->jsonResponse($result);
    }

    /**
     * Descargar backup
     */
    public function downloadBackup(int $id): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_backup')) {
            $this->session->flash('error', 'No tienes permisos para descargar backups');
            header('Location: /backup');
            exit;
        }

        // Obtener información del backup
        $backupsResult = $this->backupService->listBackups($this->userId);
        
        if (!$backupsResult['success']) {
            $this->session->flash('error', 'Error al obtener información del backup');
            header('Location: /backup');
            exit;
        }

        $backup = null;
        foreach ($backupsResult['backups'] as $b) {
            if ($b['id'] === $id) {
                $backup = $b;
                break;
            }
        }

        if (!$backup) {
            $this->session->flash('error', 'Backup no encontrado');
            header('Location: /backup');
            exit;
        }

        if (!file_exists($backup['file_path'])) {
            $this->session->flash('error', 'Archivo de backup no encontrado');
            header('Location: /backup');
            exit;
        }

        // Auditar descarga
        $this->securityService->auditUserAction(
            $this->userId,
            'download_backup',
            'backup',
            ['backup_id' => $id, 'backup_name' => $backup['backup_name']]
        );

        // Enviar archivo
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $backup['backup_name'] . '.backup"');
        header('Content-Length: ' . filesize($backup['file_path']));
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: 0');

        readfile($backup['file_path']);
        exit;
    }

    /**
     * Configurar backup automático
     */
    public function scheduleBackup(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_backup')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para programar backups'
            ], 403);
            return;
        }

        $frequency = $_POST['frequency'] ?? 'daily';
        $type = $_POST['type'] ?? 'user';
        $targetUserId = $type === 'user' ? $this->userId : null;

        // Auditar acción
        $this->securityService->auditUserAction(
            $this->userId,
            'schedule_backup',
            'backup',
            ['frequency' => $frequency, 'type' => $type]
        );

        $result = $this->backupService->scheduleAutomaticBackup($targetUserId, $frequency);

        if ($this->isAjaxRequest()) {
            $this->jsonResponse($result);
            return;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /backup');
        exit;
    }

    /**
     * Log de auditoría
     */
    public function auditLog(): string
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'view_audit_log')) {
            $this->session->flash('error', 'No tienes permisos para ver el log de auditoría');
            header('Location: /dashboard');
            exit;
        }

        $limit = (int) ($_GET['limit'] ?? 50);
        $targetUserId = $_GET['user_id'] ?? null;

        // Solo administradores pueden ver logs de otros usuarios
        if ($targetUserId && !$this->securityService->checkUserPermission($this->userId, 'view_all_data')) {
            $targetUserId = $this->userId;
        }

        $auditLog = $this->securityService->getAuditLog($targetUserId ? (int) $targetUserId : null, $limit);

        $data = [
            'title' => 'Log de Auditoría',
            'audit_log' => $auditLog['success'] ? $auditLog['logs'] : [],
            'limit' => $limit,
            'target_user_id' => $targetUserId,
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('backup/audit-log', $data);
    }

    /**
     * Análisis de seguridad
     */
    public function securityAnalysis(): string
    {
        $suspiciousActivity = $this->securityService->detectSuspiciousActivity($this->userId);

        $data = [
            'title' => 'Análisis de Seguridad',
            'suspicious_activity' => $suspiciousActivity['success'] ? $suspiciousActivity : null,
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('backup/security-analysis', $data);
    }

    /**
     * Limpiar backups antiguos
     */
    public function cleanOldBackups(): void
    {
        if (!$this->securityService->checkUserPermission($this->userId, 'system_backup')) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'No tienes permisos para limpiar backups'
            ], 403);
            return;
        }

        $daysToKeep = (int) ($_POST['days_to_keep'] ?? 30);

        // Auditar acción
        $this->securityService->auditUserAction(
            $this->userId,
            'clean_old_backups',
            'backup',
            ['days_to_keep' => $daysToKeep]
        );

        $result = $this->backupService->cleanOldBackups($daysToKeep);

        if ($this->isAjaxRequest()) {
            $this->jsonResponse($result);
            return;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /backup');
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Verificar si es una request AJAX
     */
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Enviar respuesta JSON
     */
    private function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
