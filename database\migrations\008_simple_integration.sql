-- Migración 008: Integración simple del sistema financiero
-- Fecha: 2024-12-30
-- Descripción: Crear tabla de timeline unificado

-- Crear tabla para movimientos unificados (timeline) si no existe
CREATE TABLE IF NOT EXISTS unified_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    movement_type ENUM('expense', 'income', 'credit_payment', 'bank_movement', 'credit_transaction') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description VARCHAR(255) NOT NULL,
    movement_date DATE NOT NULL,
    
    -- Referencias a las tablas originales
    transaction_id INT NULL COMMENT 'ID de transacción (gasto o ingreso)',
    credit_payment_id INT NULL COMMENT 'ID de pago de tarjeta',
    bank_movement_id INT NULL COMMENT 'ID de movimiento bancario',
    credit_transaction_id INT NULL COMMENT 'ID de transacción de tarjeta',
    
    -- Información del método de pago/destino
    bank_account_id INT NULL,
    credit_card_id INT NULL,
    
    -- <PERSON>ada<PERSON>
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_user_id (user_id),
    INDEX idx_movement_date (movement_date),
    INDEX idx_movement_type (movement_type),
    INDEX idx_bank_account (bank_account_id),
    INDEX idx_credit_card (credit_card_id)
) COMMENT = 'Timeline unificado de todos los movimientos financieros';

-- Poblar el timeline con datos existentes solo si está vacío
INSERT IGNORE INTO unified_movements (
    user_id, movement_type, amount, description, movement_date,
    transaction_id, bank_account_id, credit_card_id
)
SELECT 
    user_id, type, amount, description, transaction_date,
    id, bank_account_id, credit_card_id
FROM transactions
WHERE NOT EXISTS (SELECT 1 FROM unified_movements WHERE transaction_id = transactions.id);

INSERT IGNORE INTO unified_movements (
    user_id, movement_type, amount, description, movement_date,
    credit_payment_id, bank_account_id, credit_card_id
)
SELECT 
    user_id, 'credit_payment', amount, 
    CONCAT('Pago tarjeta - ', COALESCE(notes, 'Sin descripción')), payment_date,
    id, bank_account_id, credit_card_id
FROM credit_card_payments
WHERE NOT EXISTS (SELECT 1 FROM unified_movements WHERE credit_payment_id = credit_card_payments.id);

INSERT IGNORE INTO unified_movements (
    user_id, movement_type, amount, description, movement_date,
    bank_movement_id, bank_account_id
)
SELECT 
    user_id, 'bank_movement', amount, description, movement_date,
    id, bank_account_id
FROM bank_account_movements
WHERE NOT EXISTS (SELECT 1 FROM unified_movements WHERE bank_movement_id = bank_account_movements.id);

INSERT IGNORE INTO unified_movements (
    user_id, movement_type, amount, description, movement_date,
    credit_transaction_id, credit_card_id
)
SELECT 
    user_id, 'credit_transaction', amount, description, transaction_date,
    id, credit_card_id
FROM credit_card_transactions
WHERE NOT EXISTS (SELECT 1 FROM unified_movements WHERE credit_transaction_id = credit_card_transactions.id);
