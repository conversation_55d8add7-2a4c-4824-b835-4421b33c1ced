<?php

namespace ControlGastos\Controllers;

use ControlGastos\Core\Session;
use ControlGastos\Core\SimpleDatabase;

class IntegratedTransactionController
{
    private $db;
    private $session;

    public function __construct(SimpleDatabase $database, Session $session)
    {
        $this->db = $database->getConnection();
        $this->session = $session;
    }

    /**
     * Dashboard integrado de control de gastos
     */
    public function index(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        try {
            // Obtener cuentas bancarias del usuario
            $bankAccounts = $this->getBankAccounts($userId);
            
            // Obtener tarjetas de crédito del usuario
            $creditCards = $this->getCreditCards($userId);
            
            // Obtener categorías
            $categories = $this->getCategories($userId);
            
            // Obtener timeline unificado (últimos 20 movimientos)
            $timeline = $this->getUnifiedTimeline($userId, 20);
            
            // Obtener estadísticas del mes
            $monthlyStats = $this->getMonthlyStats($userId);

            $this->render('integrated_transactions/index', [
                'title' => 'Control de Gastos Integrado',
                'bank_accounts' => $bankAccounts,
                'credit_cards' => $creditCards,
                'categories' => $categories,
                'timeline' => $timeline,
                'monthly_stats' => $monthlyStats
            ]);

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error al cargar datos: ' . $e->getMessage());
            $this->render('integrated_transactions/index', [
                'title' => 'Control de Gastos Integrado',
                'bank_accounts' => [],
                'credit_cards' => [],
                'categories' => [],
                'timeline' => [],
                'monthly_stats' => []
            ]);
        }
    }

    /**
     * Crear nueva transacción (gasto o ingreso)
     */
    public function store(): void
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            header('Location: /controlGastos/public/?route=auth/login');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /controlGastos/public/?route=accounts');
            exit;
        }

        $data = [
            'user_id' => $userId,
            'type' => trim($_POST['type'] ?? ''), // 'expense' o 'income'
            'amount' => (float) ($_POST['amount'] ?? 0),
            'description' => trim($_POST['description'] ?? ''),
            'category_id' => (int) ($_POST['category_id'] ?? 0),
            'payment_method' => trim($_POST['payment_method'] ?? ''), // 'bank_account' o 'credit_card'
            'bank_account_id' => !empty($_POST['bank_account_id']) ? (int) $_POST['bank_account_id'] : null,
            'credit_card_id' => !empty($_POST['credit_card_id']) ? (int) $_POST['credit_card_id'] : null,
            'transaction_date' => $_POST['transaction_date'] ?? date('Y-m-d'),
            'reference' => trim($_POST['reference'] ?? ''),
            'notes' => trim($_POST['notes'] ?? '')
        ];

        // Validaciones
        if (empty($data['type']) || $data['amount'] <= 0 || empty($data['description'])) {
            $this->session->flash('error', 'Todos los campos obligatorios deben estar completos');
            header('Location: /controlGastos/public/?route=accounts');
            exit;
        }

        if (empty($data['payment_method']) || 
            ($data['payment_method'] === 'bank_account' && !$data['bank_account_id']) ||
            ($data['payment_method'] === 'credit_card' && !$data['credit_card_id'])) {
            $this->session->flash('error', 'Debe seleccionar un método de pago válido');
            header('Location: /controlGastos/public/?route=accounts');
            exit;
        }

        try {
            $this->db->beginTransaction();

            // Insertar transacción
            $sql = "INSERT INTO transactions (
                user_id, account_id, category_id, payment_method, bank_account_id, credit_card_id,
                type, amount, description, transaction_date, reference, notes
            ) VALUES (?, 1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $data['user_id'],
                $data['category_id'],
                $data['payment_method'],
                $data['bank_account_id'],
                $data['credit_card_id'],
                $data['type'],
                $data['amount'],
                $data['description'],
                $data['transaction_date'],
                $data['reference'],
                $data['notes']
            ]);

            $transactionId = $this->db->lastInsertId();

            // Actualizar saldos según el método de pago
            if ($data['payment_method'] === 'bank_account') {
                $this->updateBankAccountBalance($data['bank_account_id'], $data['amount'], $data['type']);
            } elseif ($data['payment_method'] === 'credit_card') {
                $this->updateCreditCardBalance($data['credit_card_id'], $data['amount'], $data['type']);
            }

            $this->db->commit();

            $typeLabel = $data['type'] === 'expense' ? 'Gasto' : 'Ingreso';
            $this->session->flash('success', "{$typeLabel} registrado exitosamente");

        } catch (\Exception $e) {
            $this->db->rollBack();
            $this->session->flash('error', 'Error al registrar transacción: ' . $e->getMessage());
        }

        header('Location: /controlGastos/public/?route=accounts');
        exit;
    }

    /**
     * Obtener cuentas bancarias del usuario
     */
    private function getBankAccounts(int $userId): array
    {
        $sql = "SELECT id, account_name, bank_name, current_balance, account_type 
                FROM bank_accounts 
                WHERE user_id = ? AND status = 'active' 
                ORDER BY account_name";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        
        return $stmt->fetchAll();
    }

    /**
     * Obtener tarjetas de crédito del usuario
     */
    private function getCreditCards(int $userId): array
    {
        $sql = "SELECT cc.id, cc.card_name, cc.bank_name, cc.credit_limit,
                       COALESCE(SUM(cct.amount), 0) - COALESCE(SUM(ccp.amount), 0) as current_balance,
                       cc.credit_limit - (COALESCE(SUM(cct.amount), 0) - COALESCE(SUM(ccp.amount), 0)) as available_credit
                FROM credit_cards cc
                LEFT JOIN credit_card_transactions cct ON cc.id = cct.credit_card_id
                LEFT JOIN credit_card_payments ccp ON cc.id = ccp.credit_card_id
                WHERE cc.user_id = ? AND cc.status = 'active'
                GROUP BY cc.id
                ORDER BY cc.card_name";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        
        return $stmt->fetchAll();
    }

    /**
     * Obtener categorías del usuario
     */
    private function getCategories(int $userId): array
    {
        $sql = "SELECT id, name FROM categories WHERE user_id = ? AND is_active = 1 ORDER BY name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);

        return $stmt->fetchAll();
    }

    /**
     * Obtener timeline unificado
     */
    private function getUnifiedTimeline(int $userId, int $limit = 20): array
    {
        $sql = "SELECT um.*, 
                       ba.account_name as bank_account_name,
                       cc.card_name as credit_card_name,
                       c.name as category_name
                FROM unified_movements um
                LEFT JOIN bank_accounts ba ON um.bank_account_id = ba.id
                LEFT JOIN credit_cards cc ON um.credit_card_id = cc.id
                LEFT JOIN transactions t ON um.transaction_id = t.id
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE um.user_id = ?
                ORDER BY um.movement_date DESC, um.id DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $limit]);
        
        return $stmt->fetchAll();
    }

    /**
     * Obtener estadísticas del mes
     */
    private function getMonthlyStats(int $userId): array
    {
        // Obtener estadísticas de transacciones directas
        $sql = "SELECT
                    SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as transaction_expenses,
                    SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as transaction_income,
                    COUNT(CASE WHEN type = 'expense' THEN 1 END) as transaction_expense_count,
                    COUNT(CASE WHEN type = 'income' THEN 1 END) as transaction_income_count
                FROM transactions
                WHERE user_id = ?
                AND MONTH(transaction_date) = MONTH(CURDATE())
                AND YEAR(transaction_date) = YEAR(CURDATE())";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $transactionStats = $stmt->fetch() ?: [
            'transaction_expenses' => 0,
            'transaction_income' => 0,
            'transaction_expense_count' => 0,
            'transaction_income_count' => 0
        ];

        // Obtener estadísticas de transacciones de tarjetas de crédito
        $sql = "SELECT
                    SUM(CASE WHEN transaction_type IN ('purchase', 'fee') THEN amount ELSE 0 END) as credit_expenses,
                    SUM(CASE WHEN transaction_type IN ('refund', 'cashback', 'payment_reversal') THEN amount ELSE 0 END) as credit_income,
                    COUNT(CASE WHEN transaction_type IN ('purchase', 'fee') THEN 1 END) as credit_expense_count,
                    COUNT(CASE WHEN transaction_type IN ('refund', 'cashback', 'payment_reversal') THEN 1 END) as credit_income_count
                FROM credit_card_transactions
                WHERE user_id = ?
                AND MONTH(transaction_date) = MONTH(CURDATE())
                AND YEAR(transaction_date) = YEAR(CURDATE())";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $creditStats = $stmt->fetch() ?: [
            'credit_expenses' => 0,
            'credit_income' => 0,
            'credit_expense_count' => 0,
            'credit_income_count' => 0
        ];

        // Obtener estadísticas de movimientos bancarios (solo depósitos como ingresos)
        $sql = "SELECT
                    SUM(CASE WHEN movement_type IN ('withdrawal', 'transfer_out', 'fee') THEN amount ELSE 0 END) as bank_expenses,
                    SUM(CASE WHEN movement_type IN ('deposit', 'transfer_in', 'interest') THEN amount ELSE 0 END) as bank_income,
                    COUNT(CASE WHEN movement_type IN ('withdrawal', 'transfer_out', 'fee') THEN 1 END) as bank_expense_count,
                    COUNT(CASE WHEN movement_type IN ('deposit', 'transfer_in', 'interest') THEN 1 END) as bank_income_count
                FROM bank_account_movements
                WHERE user_id = ?
                AND MONTH(movement_date) = MONTH(CURDATE())
                AND YEAR(movement_date) = YEAR(CURDATE())
                AND description NOT LIKE '%registrado%'"; // Excluir movimientos automáticos del sistema

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $bankStats = $stmt->fetch() ?: [
            'bank_expenses' => 0,
            'bank_income' => 0,
            'bank_expense_count' => 0,
            'bank_income_count' => 0
        ];

        // Consolidar todas las estadísticas
        $totalExpenses = (float)$transactionStats['transaction_expenses'] +
                        (float)$creditStats['credit_expenses'] +
                        (float)$bankStats['bank_expenses'];

        $totalIncome = (float)$transactionStats['transaction_income'] +
                      (float)$creditStats['credit_income'] +
                      (float)$bankStats['bank_income'];

        $totalExpenseCount = (int)$transactionStats['transaction_expense_count'] +
                           (int)$creditStats['credit_expense_count'] +
                           (int)$bankStats['bank_expense_count'];

        $totalIncomeCount = (int)$transactionStats['transaction_income_count'] +
                          (int)$creditStats['credit_income_count'] +
                          (int)$bankStats['bank_income_count'];

        return [
            'total_expenses' => $totalExpenses,
            'total_income' => $totalIncome,
            'expense_count' => $totalExpenseCount,
            'income_count' => $totalIncomeCount,
            'balance' => $totalIncome - $totalExpenses
        ];
    }

    /**
     * Actualizar saldo de cuenta bancaria
     */
    private function updateBankAccountBalance(int $accountId, float $amount, string $type): void
    {
        // Obtener saldo actual
        $sql = "SELECT current_balance FROM bank_accounts WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$accountId]);
        $currentBalance = (float) $stmt->fetchColumn();

        // Calcular nuevo saldo
        $newBalance = $type === 'income' ? $currentBalance + $amount : $currentBalance - $amount;

        // Actualizar saldo de la cuenta
        $sql = "UPDATE bank_accounts
                SET current_balance = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$newBalance, $accountId]);

        // Registrar movimiento en bank_account_movements
        $movementType = $type === 'income' ? 'deposit' : 'withdrawal';
        $description = $type === 'income' ? 'Ingreso registrado' : 'Gasto registrado';

        $sql = "INSERT INTO bank_account_movements (
            bank_account_id, user_id, movement_type, amount, description, movement_date, balance_after
        ) VALUES (?, ?, ?, ?, ?, CURDATE(), ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $accountId,
            $this->session->get('user_id'),
            $movementType,
            $amount,
            $description,
            $newBalance
        ]);
    }

    /**
     * Actualizar saldo de tarjeta de crédito
     */
    private function updateCreditCardBalance(int $cardId, float $amount, string $type): void
    {
        // Para tarjetas: gastos aumentan el cupo consumido, ingresos (devoluciones/cashback) lo reducen
        if ($type === 'expense') {
            // Registrar transacción de tarjeta
            $sql = "INSERT INTO credit_card_transactions (
                credit_card_id, user_id, transaction_type, amount, description, transaction_date
            ) VALUES (?, ?, 'purchase', ?, 'Gasto registrado', CURDATE())";
        } else {
            // Registrar como devolución/cashback
            $sql = "INSERT INTO credit_card_transactions (
                credit_card_id, user_id, transaction_type, amount, description, transaction_date
            ) VALUES (?, ?, 'refund', ?, 'Ingreso/Devolución', CURDATE())";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$cardId, $this->session->get('user_id'), $amount]);
    }

    /**
     * Renderizar vista con layout
     */
    private function render(string $view, array $data = []): void
    {
        // Extraer variables para la vista
        extract($data);
        
        // Obtener información del usuario
        $userId = $this->session->get('user_id');
        $user = null;
        if ($userId) {
            $stmt = $this->db->prepare("SELECT first_name, last_name, email FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
        }

        // Renderizar con layout completo
        echo '<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . ($title ?? 'Control de Gastos') . '</title>
    <link href="/controlGastos/public/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="/controlGastos/public/assets/css/fontawesome.min.css" rel="stylesheet">
    <link href="/controlGastos/public/assets/css/custom.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="/controlGastos/public/">
                    <i class="fas fa-chart-line me-2"></i>Control de Gastos
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/controlGastos/public/">
                                <i class="fas fa-home me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/controlGastos/public/?route=accounts">
                                <i class="fas fa-exchange-alt me-1"></i>Control de Gastos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/controlGastos/public/?route=credit-cards">
                                <i class="fas fa-credit-card me-1"></i>Tarjetas de Crédito
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/controlGastos/public/?route=bank-accounts">
                                <i class="fas fa-university me-1"></i>Cuentas Bancarias
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>' . htmlspecialchars(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '') ?: 'Usuario') . '
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/controlGastos/public/?route=auth/logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Contenido principal -->
        <div class="container-fluid py-4">';
        
        // Mostrar mensajes flash
        if ($this->session->hasFlash('success')) {
            echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>' . $this->session->getFlash('success') . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
        }
        
        if ($this->session->hasFlash('error')) {
            echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>' . $this->session->getFlash('error') . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
        }

        // Incluir la vista específica
        $viewPath = __DIR__ . "/../../templates/pages/{$view}.php";
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            echo "<div class='container'><div class='alert alert-danger'>Vista no encontrada: {$view}</div></div>";
        }

        echo '
        </div>
    </div>

    <script src="/controlGastos/public/assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
    }
}
