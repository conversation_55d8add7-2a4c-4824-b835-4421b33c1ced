<?php
// La autenticación ya se verifica en el controlador
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0"><i class="fas fa-edit me-2"></i>Editar Tarjeta de Crédito</h1>
                    <p class="text-muted">Modifica la información de <?= htmlspecialchars($card['card_name']) ?></p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/controlGastos/public/?route=credit-cards/show&id=<?= $card['id'] ?>" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>Ver Detalles
                    </a>
                    <a href="/controlGastos/public/?route=credit-cards" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario -->
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i>Información de la Tarjeta
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/controlGastos/public/?route=credit-cards/update" id="editCreditCardForm">
                        <input type="hidden" name="id" value="<?= $card['id'] ?>">
                        
                        <!-- Información Básica -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="card_name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Nombre de la Tarjeta *
                                </label>
                                <input type="text" class="form-control" id="card_name" name="card_name" 
                                       value="<?= htmlspecialchars($card['card_name']) ?>" required maxlength="100">
                            </div>
                            <div class="col-md-6">
                                <label for="bank_name" class="form-label">
                                    <i class="fas fa-university me-1"></i>Banco Emisor *
                                </label>
                                <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                       value="<?= htmlspecialchars($card['bank_name']) ?>" required maxlength="100">
                            </div>
                        </div>

                        <!-- Información de la Tarjeta -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="card_number_last4" class="form-label">
                                    <i class="fas fa-credit-card me-1"></i>Últimos 4 Dígitos
                                </label>
                                <input type="text" class="form-control" id="card_number_last4" name="card_number_last4" 
                                       value="<?= htmlspecialchars($card['card_number_last4'] ?? '') ?>" maxlength="4" pattern="[0-9]{4}">
                            </div>
                            <div class="col-md-6">
                                <label for="credit_limit" class="form-label">
                                    <i class="fas fa-wallet me-1"></i>Cupo Total *
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit"
                                           value="<?= $card['credit_limit'] ?>" required min="1" step="0.01">
                                </div>
                            </div>
                        </div>

                        <!-- Fechas -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="expiry_date" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>Fecha de Expiración *
                                </label>
                                <input type="date" class="form-control" id="expiry_date" name="expiry_date" 
                                       value="<?= $card['expiry_date'] ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cut_off_day" class="form-label">
                                    <i class="fas fa-calendar-check me-1"></i>Día de Corte *
                                </label>
                                <select class="form-select" id="cut_off_day" name="cut_off_day" required>
                                    <?php for ($i = 1; $i <= 31; $i++): ?>
                                    <option value="<?= $i ?>" <?= $i == $card['cut_off_day'] ? 'selected' : '' ?>><?= $i ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>

                        <!-- Configuración de Pagos -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="payment_due_days" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Días para Pagar
                                </label>
                                <input type="number" class="form-control" id="payment_due_days" name="payment_due_days" 
                                       value="<?= $card['payment_due_days'] ?>" min="1" max="60" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cvv" class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>Código CCV
                                </label>
                                <input type="password" class="form-control" id="cvv" name="cvv" 
                                       value="<?= htmlspecialchars($card['cvv'] ?? '') ?>" maxlength="4" pattern="[0-9]{3,4}">
                                <div class="form-text">Dejar vacío para mantener el actual</div>
                            </div>
                        </div>

                        <!-- Descripción -->
                        <div class="mb-4">
                            <label for="description" class="form-label">
                                <i class="fas fa-comment me-1"></i>Descripción
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($card['description'] ?? '') ?></textarea>
                        </div>

                        <!-- Vista Previa -->
                        <div class="card bg-light mb-4" id="cardPreview">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Vista Previa</h6>
                            </div>
                            <div class="card-body">
                                <div class="card shadow-sm" style="max-width: 350px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                    <div class="card-body text-white">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div>
                                                <h6 class="mb-0" id="preview_card_name"><?= htmlspecialchars($card['card_name']) ?></h6>
                                                <small id="preview_bank_name"><?= htmlspecialchars($card['bank_name']) ?></small>
                                            </div>
                                            <i class="fas fa-credit-card fa-2x opacity-75"></i>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-end">
                                            <div>
                                                <small class="opacity-75">Cupo</small>
                                                <div id="preview_credit_limit">$<?= number_format($card['credit_limit'], 0) ?></div>
                                            </div>
                                            <div class="text-end">
                                                <small class="opacity-75">Vence</small>
                                                <div id="preview_expiry_date"><?= date('m/y', strtotime($card['expiry_date'])) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Botones -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-warning flex-fill">
                                <i class="fas fa-save me-2"></i>Actualizar Tarjeta
                            </button>
                            <a href="/controlGastos/public/?route=credit-cards/show&id=<?= $card['id'] ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Zona de Peligro -->
            <div class="card mt-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Zona de Peligro
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        <strong>Eliminar tarjeta:</strong> Esta acción no se puede deshacer. 
                        Se eliminarán todas las transacciones y pagos asociados.
                    </p>
                    <form method="POST" action="/controlGastos/public/?route=credit-cards/delete" 
                          onsubmit="return confirm('¿Estás seguro de que quieres eliminar esta tarjeta? Esta acción no se puede deshacer.')">
                        <input type="hidden" name="id" value="<?= $card['id'] ?>">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Eliminar Tarjeta
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Vista previa en tiempo real
    const form = document.getElementById('editCreditCardForm');
    const previewElements = {
        card_name: document.getElementById('preview_card_name'),
        bank_name: document.getElementById('preview_bank_name'),
        credit_limit: document.getElementById('preview_credit_limit'),
        expiry_date: document.getElementById('preview_expiry_date')
    };

    function updatePreview() {
        const cardName = form.card_name.value || 'Nombre de la Tarjeta';
        const bankName = form.bank_name.value || 'Banco Emisor';
        const creditLimit = form.credit_limit.value ? '$' + parseInt(form.credit_limit.value).toLocaleString() : '$0';
        const expiryDate = form.expiry_date.value ? new Date(form.expiry_date.value).toLocaleDateString('es-ES', {month: '2-digit', year: '2-digit'}) : '--/--';

        previewElements.card_name.textContent = cardName;
        previewElements.bank_name.textContent = bankName;
        previewElements.credit_limit.textContent = creditLimit;
        previewElements.expiry_date.textContent = expiryDate;
    }

    // Actualizar vista previa en tiempo real
    ['card_name', 'bank_name', 'credit_limit', 'expiry_date'].forEach(field => {
        form[field].addEventListener('input', updatePreview);
    });

    // Formatear número de tarjeta
    form.card_number_last4.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').slice(0, 4);
    });

    // Formatear CVV
    form.cvv.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').slice(0, 4);
    });
});
</script>
