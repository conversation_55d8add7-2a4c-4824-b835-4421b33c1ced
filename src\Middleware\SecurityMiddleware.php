<?php

declare(strict_types=1);

namespace ControlGastos\Middleware;

use ControlGastos\Core\Container;

/**
 * Middleware de seguridad global
 * Aplica headers de seguridad y validaciones básicas
 */
class SecurityMiddleware
{
    public function handle(Container $container): void
    {
        $this->setSecurityHeaders();
        $this->validateRequest();
        $this->preventDirectAccess();
    }

    /**
     * Establecer headers de seguridad
     */
    private function setSecurityHeaders(): void
    {
        // Prevenir clickjacking
        header('X-Frame-Options: SAMEORIGIN');
        
        // Prevenir MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Habilitar XSS protection
        header('X-XSS-Protection: 1; mode=block');
        
        // Configurar Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
               "font-src 'self' https://fonts.gstatic.com; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self';";
        header('Content-Security-Policy: ' . $csp);
        
        // Configurar Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Configurar Feature Policy
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
        
        // Configurar HSTS (solo en HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
    }

    /**
     * Validar request básica
     */
    private function validateRequest(): void
    {
        // Validar método HTTP
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        if (!in_array($method, $allowedMethods)) {
            http_response_code(405);
            exit('Método no permitido');
        }

        // Validar tamaño de request
        $maxSize = 10 * 1024 * 1024; // 10MB
        $contentLength = $_SERVER['CONTENT_LENGTH'] ?? 0;
        
        if ($contentLength > $maxSize) {
            http_response_code(413);
            exit('Request demasiado grande');
        }

        // Validar User Agent (básico)
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($userAgent) || strlen($userAgent) > 500) {
            http_response_code(400);
            exit('User Agent inválido');
        }
    }

    /**
     * Prevenir acceso directo a archivos sensibles
     */
    private function preventDirectAccess(): void
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        
        // Patrones de archivos/directorios bloqueados
        $blockedPatterns = [
            '/\.env',
            '/\.git',
            '/\.svn',
            '/vendor/',
            '/config/',
            '/src/',
            '/storage/',
            '/database/',
            '/tests/',
            '/composer\.',
            '/\.log$',
            '/\.sql$',
            '/\.md$',
            '/\.json$',
            '/\.lock$',
            '/\.yml$',
            '/\.yaml$',
            '/\.xml$'
        ];

        foreach ($blockedPatterns as $pattern) {
            if (preg_match($pattern, $uri)) {
                http_response_code(404);
                exit('Archivo no encontrado');
            }
        }
    }
}
