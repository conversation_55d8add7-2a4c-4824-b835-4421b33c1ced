<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reparar Base de Datos - Control de Gastos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding: 2rem 0;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header text-center">
                        <h1 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Reparar Base de Datos
                        </h1>
                        <p class="mb-0 mt-2">Solucionar problemas de estructura de tablas</p>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        $action = $_POST['action'] ?? '';
                        
                        try {
                            // Conectar a MySQL
                            $pdo = new PDO("mysql:host=localhost", "root", "");
                            echo "<div class='alert alert-success'>";
                            echo "<i class='fas fa-check me-2'></i>Conexión a MySQL: OK";
                            echo "</div>";
                            
                            // Crear base de datos si no existe
                            $pdo->exec("CREATE DATABASE IF NOT EXISTS control_gastos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                            echo "<div class='alert alert-success'>";
                            echo "<i class='fas fa-check me-2'></i>Base de datos 'control_gastos': OK";
                            echo "</div>";
                            
                            // Conectar a la base de datos específica
                            $pdo = new PDO("mysql:host=localhost;dbname=control_gastos", "root", "");
                            
                            if ($action === 'fix_tables') {
                                echo "<div class='alert alert-info'>";
                                echo "<h5><i class='fas fa-cogs me-2'></i>Creando/Reparando Tablas</h5>";
                                
                                // Crear tabla users con estructura correcta
                                $userTableSQL = "
                                CREATE TABLE IF NOT EXISTS users (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    first_name VARCHAR(100) NOT NULL,
                                    last_name VARCHAR(100) NOT NULL,
                                    email VARCHAR(255) NOT NULL UNIQUE,
                                    password VARCHAR(255) NOT NULL,
                                    role ENUM('admin', 'user') DEFAULT 'user',
                                    is_verified BOOLEAN DEFAULT FALSE,
                                    email_verified_at TIMESTAMP NULL,
                                    remember_token VARCHAR(100) NULL,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    
                                    INDEX idx_email (email),
                                    INDEX idx_role (role),
                                    INDEX idx_verified (is_verified)
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                                ";
                                
                                $pdo->exec($userTableSQL);
                                echo "<p>✅ Tabla 'users' creada/reparada</p>";
                                
                                // Crear tabla categories
                                $categoriesSQL = "
                                CREATE TABLE IF NOT EXISTS categories (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    user_id INT NOT NULL,
                                    name VARCHAR(100) NOT NULL,
                                    color VARCHAR(7) DEFAULT '#007bff',
                                    icon VARCHAR(50) DEFAULT 'fas fa-tag',
                                    type ENUM('income', 'expense') DEFAULT 'expense',
                                    is_active BOOLEAN DEFAULT TRUE,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    
                                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                                    INDEX idx_user_type (user_id, type),
                                    INDEX idx_active (is_active)
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                                ";
                                
                                $pdo->exec($categoriesSQL);
                                echo "<p>✅ Tabla 'categories' creada/reparada</p>";
                                
                                // Crear tabla accounts
                                $accountsSQL = "
                                CREATE TABLE IF NOT EXISTS accounts (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    user_id INT NOT NULL,
                                    name VARCHAR(100) NOT NULL,
                                    type ENUM('cash', 'bank', 'credit_card', 'savings', 'investment') DEFAULT 'cash',
                                    balance DECIMAL(15,2) DEFAULT 0.00,
                                    currency VARCHAR(3) DEFAULT 'MXN',
                                    is_active BOOLEAN DEFAULT TRUE,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    
                                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                                    INDEX idx_user_active (user_id, is_active),
                                    INDEX idx_type (type)
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                                ";
                                
                                $pdo->exec($accountsSQL);
                                echo "<p>✅ Tabla 'accounts' creada/reparada</p>";
                                
                                // Crear tabla transactions
                                $transactionsSQL = "
                                CREATE TABLE IF NOT EXISTS transactions (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    user_id INT NOT NULL,
                                    account_id INT NOT NULL,
                                    category_id INT NOT NULL,
                                    type ENUM('income', 'expense') NOT NULL,
                                    amount DECIMAL(15,2) NOT NULL,
                                    description TEXT,
                                    transaction_date DATE NOT NULL,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    
                                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                                    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                                    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
                                    INDEX idx_user_date (user_id, transaction_date),
                                    INDEX idx_type (type),
                                    INDEX idx_account (account_id),
                                    INDEX idx_category (category_id)
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                                ";
                                
                                $pdo->exec($transactionsSQL);
                                echo "<p>✅ Tabla 'transactions' creada/reparada</p>";
                                
                                // Crear tabla reminders
                                $remindersSQL = "
                                CREATE TABLE IF NOT EXISTS reminders (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    user_id INT NOT NULL,
                                    title VARCHAR(255) NOT NULL,
                                    description TEXT,
                                    amount DECIMAL(15,2),
                                    due_date DATE NOT NULL,
                                    is_completed BOOLEAN DEFAULT FALSE,
                                    completed_at TIMESTAMP NULL,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    
                                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                                    INDEX idx_user_due (user_id, due_date),
                                    INDEX idx_completed (is_completed)
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                                ";
                                
                                $pdo->exec($remindersSQL);
                                echo "<p>✅ Tabla 'reminders' creada/reparada</p>";
                                
                                echo "</div>";
                                
                                echo "<div class='alert alert-success'>";
                                echo "<h5><i class='fas fa-check-circle me-2'></i>¡Base de datos reparada exitosamente!</h5>";
                                echo "<p>Todas las tablas han sido creadas con la estructura correcta.</p>";
                                echo "<div class='d-grid gap-2'>";
                                echo "<a href='create-admin-local.php' class='btn btn-success'>";
                                echo "<i class='fas fa-user-plus me-2'></i>";
                                echo "Crear Usuario Administrador";
                                echo "</a>";
                                echo "</div>";
                                echo "</div>";
                                
                            } else {
                                // Mostrar estado actual de las tablas
                                echo "<div class='alert alert-info'>";
                                echo "<h5><i class='fas fa-info-circle me-2'></i>Estado Actual de la Base de Datos</h5>";
                                
                                $tables = ['users', 'categories', 'accounts', 'transactions', 'reminders'];
                                
                                foreach ($tables as $table) {
                                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                                    if ($stmt->rowCount() > 0) {
                                        echo "<p>✅ Tabla '$table': Existe</p>";
                                        
                                        // Mostrar estructura
                                        $stmt = $pdo->query("DESCRIBE $table");
                                        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                                        echo "<div class='code-block'>";
                                        echo "<small>Columnas: " . implode(', ', $columns) . "</small>";
                                        echo "</div>";
                                    } else {
                                        echo "<p>❌ Tabla '$table': No existe</p>";
                                    }
                                }
                                
                                echo "</div>";
                                
                                echo "<form method='POST'>";
                                echo "<input type='hidden' name='action' value='fix_tables'>";
                                echo "<div class='d-grid'>";
                                echo "<button type='submit' class='btn btn-primary btn-lg'>";
                                echo "<i class='fas fa-tools me-2'></i>";
                                echo "Crear/Reparar Todas las Tablas";
                                echo "</button>";
                                echo "</div>";
                                echo "</form>";
                            }
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>Error</h5>";
                            echo "<p><strong>Mensaje:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
                            echo "<hr>";
                            echo "<h6>Soluciones:</h6>";
                            echo "<ul>";
                            echo "<li>Verifica que XAMPP esté funcionando</li>";
                            echo "<li>Asegúrate de que MySQL esté iniciado</li>";
                            echo "<li>Ve a <a href='http://localhost/xampp' target='_blank'>Panel de XAMPP</a></li>";
                            echo "</ul>";
                            echo "</div>";
                        }
                        ?>
                        
                    </div>
                </div>
                
                <!-- Información adicional -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class='fas fa-info-circle me-2'></i>
                            Información
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><strong>¿Qué hace este script?</strong></p>
                        <ul>
                            <li>Crea la base de datos 'control_gastos' si no existe</li>
                            <li>Crea todas las tablas necesarias con la estructura correcta</li>
                            <li>Repara tablas existentes que tengan problemas de estructura</li>
                            <li>Configura índices y relaciones entre tablas</li>
                        </ul>
                        
                        <p><strong>Tablas que se crearán:</strong></p>
                        <ul>
                            <li><code>users</code> - Usuarios del sistema</li>
                            <li><code>categories</code> - Categorías de transacciones</li>
                            <li><code>accounts</code> - Cuentas financieras</li>
                            <li><code>transactions</code> - Transacciones</li>
                            <li><code>reminders</code> - Recordatorios</li>
                        </ul>
                        
                        <div class="mt-3">
                            <a href="setup-local.php" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-arrow-left me-1"></i>Volver a Setup
                            </a>
                            <a href="http://localhost/phpmyadmin" target="_blank" class="btn btn-outline-info">
                                <i class="fas fa-database me-1"></i>Abrir phpMyAdmin
                            </a>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</body>
</html>
