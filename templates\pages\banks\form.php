<?php
$content = ob_start();
$isEdit = isset($bank) && $bank;
$formTitle = $isEdit ? 'Editar Banco' : 'Nuevo Banco';
$submitText = $isEdit ? 'Actualizar Banco' : 'Crear Banco';
$formAction = $isEdit ? '?route=banks&action=update' : '?route=banks&action=store';
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="fas fa-<?= $isEdit ? 'edit' : 'plus' ?> text-primary me-2"></i>
            <?= $formTitle ?>
        </h1>
        <p class="text-muted mb-0">
            <?= $isEdit ? 'Modifica la información del banco' : 'Registra una nueva entidad financiera' ?>
        </p>
    </div>
    <div>
        <a href="?route=banks" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Volver
        </a>
    </div>
</div>

<!-- Form Card -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-university text-primary me-2"></i>
                    Información del Banco
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= $formAction ?>" id="bankForm">
                    <?php if ($isEdit): ?>
                        <input type="hidden" name="id" value="<?= $bank['id'] ?>">
                    <?php endif; ?>
                    
                    <div class="row g-3">
                        <!-- Bank Name -->
                        <div class="col-md-8">
                            <label for="bank_name" class="form-label">
                                <i class="fas fa-university text-primary me-1"></i>
                                Nombre del Banco <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                   value="<?= htmlspecialchars($bank['bank_name'] ?? '') ?>" 
                                   placeholder="Ej: Bancolombia, Banco de Bogotá" required>
                            <div class="form-text">Nombre oficial de la entidad financiera</div>
                        </div>
                        
                        <!-- Bank Code -->
                        <div class="col-md-4">
                            <label for="bank_code" class="form-label">
                                <i class="fas fa-hashtag text-primary me-1"></i>
                                Código del Banco
                            </label>
                            <input type="text" class="form-control" id="bank_code" name="bank_code" 
                                   value="<?= htmlspecialchars($bank['bank_code'] ?? '') ?>" 
                                   placeholder="Ej: 007, 001" maxlength="10">
                            <div class="form-text">Código oficial del banco</div>
                        </div>
                        
                        <!-- Bank Type -->
                        <div class="col-md-6">
                            <label for="bank_type" class="form-label">
                                <i class="fas fa-layer-group text-primary me-1"></i>
                                Tipo de Entidad
                            </label>
                            <select class="form-select" id="bank_type" name="bank_type">
                                <option value="commercial" <?= ($bank['bank_type'] ?? '') === 'commercial' ? 'selected' : '' ?>>
                                    Banco Comercial
                                </option>
                                <option value="cooperative" <?= ($bank['bank_type'] ?? '') === 'cooperative' ? 'selected' : '' ?>>
                                    Cooperativa
                                </option>
                                <option value="investment" <?= ($bank['bank_type'] ?? '') === 'investment' ? 'selected' : '' ?>>
                                    Banco de Inversión
                                </option>
                                <option value="digital" <?= ($bank['bank_type'] ?? '') === 'digital' ? 'selected' : '' ?>>
                                    Banco Digital
                                </option>
                                <option value="other" <?= ($bank['bank_type'] ?? '') === 'other' ? 'selected' : '' ?>>
                                    Otro
                                </option>
                            </select>
                        </div>
                        
                        <!-- Country -->
                        <div class="col-md-6">
                            <label for="country" class="form-label">
                                <i class="fas fa-flag text-primary me-1"></i>
                                País
                            </label>
                            <select class="form-select" id="country" name="country">
                                <option value="COL" <?= ($bank['country'] ?? 'COL') === 'COL' ? 'selected' : '' ?>>Colombia</option>
                                <option value="USA" <?= ($bank['country'] ?? '') === 'USA' ? 'selected' : '' ?>>Estados Unidos</option>
                                <option value="MEX" <?= ($bank['country'] ?? '') === 'MEX' ? 'selected' : '' ?>>México</option>
                                <option value="ESP" <?= ($bank['country'] ?? '') === 'ESP' ? 'selected' : '' ?>>España</option>
                                <option value="ARG" <?= ($bank['country'] ?? '') === 'ARG' ? 'selected' : '' ?>>Argentina</option>
                                <option value="CHL" <?= ($bank['country'] ?? '') === 'CHL' ? 'selected' : '' ?>>Chile</option>
                                <option value="PER" <?= ($bank['country'] ?? '') === 'PER' ? 'selected' : '' ?>>Perú</option>
                                <option value="BRA" <?= ($bank['country'] ?? '') === 'BRA' ? 'selected' : '' ?>>Brasil</option>
                            </select>
                        </div>
                        
                        <!-- Website -->
                        <div class="col-md-6">
                            <label for="website" class="form-label">
                                <i class="fas fa-globe text-primary me-1"></i>
                                Sitio Web
                            </label>
                            <input type="url" class="form-control" id="website" name="website" 
                                   value="<?= htmlspecialchars($bank['website'] ?? '') ?>" 
                                   placeholder="https://www.banco.com">
                        </div>
                        
                        <!-- Phone -->
                        <div class="col-md-6">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone text-primary me-1"></i>
                                Teléfono
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?= htmlspecialchars($bank['phone'] ?? '') ?>" 
                                   placeholder="Ej: +57 1 234 5678">
                        </div>
                        
                        <!-- Email -->
                        <div class="col-md-6">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope text-primary me-1"></i>
                                Email
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= htmlspecialchars($bank['email'] ?? '') ?>" 
                                   placeholder="<EMAIL>">
                        </div>
                        
                        <!-- Color -->
                        <div class="col-md-6">
                            <label for="color" class="form-label">
                                <i class="fas fa-palette text-primary me-1"></i>
                                Color Representativo
                            </label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color" id="color" name="color" 
                                       value="<?= htmlspecialchars($bank['color'] ?? '#007bff') ?>" 
                                       title="Seleccionar color">
                                <input type="text" class="form-control" id="colorText" 
                                       value="<?= htmlspecialchars($bank['color'] ?? '#007bff') ?>" 
                                       placeholder="#007bff" maxlength="7">
                            </div>
                            <div class="form-text">Color para identificar el banco en el sistema</div>
                        </div>
                        
                        <!-- Logo URL -->
                        <div class="col-12">
                            <label for="logo_url" class="form-label">
                                <i class="fas fa-image text-primary me-1"></i>
                                URL del Logo
                            </label>
                            <input type="url" class="form-control" id="logo_url" name="logo_url" 
                                   value="<?= htmlspecialchars($bank['logo_url'] ?? '') ?>" 
                                   placeholder="https://ejemplo.com/logo.png">
                            <div class="form-text">URL de la imagen del logo del banco (opcional)</div>
                        </div>
                        
                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left text-primary me-1"></i>
                                Descripción
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Descripción adicional del banco..."><?= htmlspecialchars($bank['description'] ?? '') ?></textarea>
                            <div class="form-text">Información adicional sobre el banco</div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                        <a href="?route=banks" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-<?= $isEdit ? 'save' : 'plus' ?> me-1"></i>
                            <?= $submitText ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sync color picker with text input
    const colorPicker = document.getElementById('color');
    const colorText = document.getElementById('colorText');
    
    colorPicker.addEventListener('change', function() {
        colorText.value = this.value;
    });
    
    colorText.addEventListener('input', function() {
        if (/^#[0-9A-Fa-f]{6}$/.test(this.value)) {
            colorPicker.value = this.value;
        }
    });
    
    // Form validation
    document.getElementById('bankForm').addEventListener('submit', function(e) {
        const bankName = document.getElementById('bank_name').value.trim();
        
        if (!bankName) {
            e.preventDefault();
            alert('El nombre del banco es requerido');
            document.getElementById('bank_name').focus();
            return;
        }
        
        // Validate color format
        const color = document.getElementById('colorText').value;
        if (color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
            e.preventDefault();
            alert('El color debe tener el formato #RRGGBB (ej: #007bff)');
            document.getElementById('colorText').focus();
            return;
        }
    });
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
