<?php
$content = ob_start();
?>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus"></i>
                    Nueva Cuenta
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" action="/accounts">
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Nombre de la Cuenta *</label>
                        <input type="text" 
                               class="form-control <?= isset($validation_errors['name']) ? 'is-invalid' : '' ?>" 
                               id="name" 
                               name="name" 
                               value="<?= htmlspecialchars($_POST['name'] ?? '') ?>"
                               required>
                        <?php if (isset($validation_errors['name'])): ?>
                            <div class="invalid-feedback">
                                <?= htmlspecialchars($validation_errors['name']) ?>
                            </div>
                        <?php endif; ?>
                        <div class="form-text">Ejemplo: Cuenta Corriente Bancolombia, Efectivo Casa, etc.</div>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Tipo de Cuenta *</label>
                        <select class="form-select <?= isset($validation_errors['type']) ? 'is-invalid' : '' ?>" 
                                id="type" 
                                name="type" 
                                required>
                            <option value="">Seleccione un tipo</option>
                            <?php foreach ($account_types as $value => $label): ?>
                                <option value="<?= htmlspecialchars($value) ?>" 
                                        <?= ($_POST['type'] ?? '') === $value ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($validation_errors['type'])): ?>
                            <div class="invalid-feedback">
                                <?= htmlspecialchars($validation_errors['type']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="currency" class="form-label">Moneda *</label>
                        <select class="form-select <?= isset($validation_errors['currency']) ? 'is-invalid' : '' ?>" 
                                id="currency" 
                                name="currency" 
                                required>
                            <?php foreach ($currencies as $value => $label): ?>
                                <option value="<?= htmlspecialchars($value) ?>" 
                                        <?= ($_POST['currency'] ?? 'COP') === $value ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($validation_errors['currency'])): ?>
                            <div class="invalid-feedback">
                                <?= htmlspecialchars($validation_errors['currency']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="initial_balance" class="form-label">Balance Inicial</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" 
                                   class="form-control <?= isset($validation_errors['initial_balance']) ? 'is-invalid' : '' ?>" 
                                   id="initial_balance" 
                                   name="initial_balance" 
                                   value="<?= htmlspecialchars($_POST['initial_balance'] ?? '0') ?>"
                                   step="0.01"
                                   min="0">
                            <?php if (isset($validation_errors['initial_balance'])): ?>
                                <div class="invalid-feedback">
                                    <?= htmlspecialchars($validation_errors['initial_balance']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="form-text">Ingrese el balance actual de la cuenta</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descripción</label>
                        <textarea class="form-control <?= isset($validation_errors['description']) ? 'is-invalid' : '' ?>" 
                                  id="description" 
                                  name="description" 
                                  rows="3"
                                  maxlength="500"><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                        <?php if (isset($validation_errors['description'])): ?>
                            <div class="invalid-feedback">
                                <?= htmlspecialchars($validation_errors['description']) ?>
                            </div>
                        <?php endif; ?>
                        <div class="form-text">Información adicional sobre la cuenta (opcional)</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="/accounts" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Crear Cuenta
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus en el primer campo
    document.getElementById('name').focus();
    
    // Validación en tiempo real
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    const typeSelect = document.getElementById('type');
    
    nameInput.addEventListener('input', function() {
        if (this.value.length < 2) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
    
    typeSelect.addEventListener('change', function() {
        if (this.value === '') {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
    
    // Prevenir envío si hay errores
    form.addEventListener('submit', function(e) {
        let hasErrors = false;
        
        if (nameInput.value.length < 2) {
            nameInput.classList.add('is-invalid');
            hasErrors = true;
        }
        
        if (typeSelect.value === '') {
            typeSelect.classList.add('is-invalid');
            hasErrors = true;
        }
        
        if (hasErrors) {
            e.preventDefault();
            alert('Por favor, corrija los errores antes de continuar');
        }
    });
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
