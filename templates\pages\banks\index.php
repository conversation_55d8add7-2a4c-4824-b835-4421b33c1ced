<?php
$content = ob_start();
?>

<!-- CSS específico para FontAwesome en esta página -->
<style>
/* Forzar carga de FontAwesome */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css');

/* Asegurar que FontAwesome se cargue correctamente */
.fas, .far, .fab, .fal, .fad {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome", sans-serif !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    line-height: 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    display: inline-block !important;
}

/* Iconos específicos como fallback con Unicode */
.fa-university:before { content: "\f19c" !important; }
.fa-plus:before { content: "\f067" !important; }
.fa-search:before { content: "\f002" !important; }
.fa-edit:before { content: "\f044" !important; }
.fa-trash:before { content: "\f1f8" !important; }
.fa-list:before { content: "\f03a" !important; }

/* Fallback para cuando FontAwesome no carga - mostrar emoji dentro del elemento i */
.fas {
    font-family: "Font Awesome 6 Free", sans-serif;
    font-weight: 900;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

/* Si FontAwesome no está disponible, el emoji será visible como texto */
/* Si FontAwesome está disponible, el :before ocultará el emoji */
</style>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <span class="icon-university text-primary me-2">🏛️</span>
            Gestión de Bancos
        </h1>
        <p class="text-muted mb-0">Administra las entidades financieras de tu sistema</p>
    </div>
    <div class="d-flex gap-2">
        <a href="?route=banks&action=create" class="btn btn-primary">
            <span class="icon-plus me-1">➕</span>
            Nuevo Banco
        </a>
    </div>
</div>

<!-- Alerts -->
<?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card card-gradient-primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Total Bancos</h6>
                        <h3 class="text-white mb-0"><?= $stats['total_banks'] ?? 0 ?></h3>
                        <small class="text-white-50">
                            <i class="fas fa-university"></i>
                            Entidades registradas
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-university fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-gradient-success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Bancos Activos</h6>
                        <h3 class="text-white mb-0"><?= $stats['active_banks'] ?? 0 ?></h3>
                        <small class="text-white-50">
                            <i class="fas fa-check-circle"></i>
                            En uso actualmente
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-check-circle fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-gradient-info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title text-white-50 mb-2">Tipos de Banco</h6>
                        <h3 class="text-white mb-0"><?= $stats['bank_types'] ?? 0 ?></h3>
                        <small class="text-white-50">
                            <i class="fas fa-layer-group"></i>
                            Categorías diferentes
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="fas fa-layer-group fa-lg text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Banks Table -->
<div class="card">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <span class="icon-list text-primary me-2">📋</span>
                Lista de Bancos
            </h5>
            <div class="d-flex gap-2">
                <div class="input-group input-group-sm" style="width: 250px;">
                    <span class="input-group-text">
                        <span class="icon-search">🔍</span>
                    </span>
                    <input type="text" class="form-control" id="searchBanks" placeholder="Buscar bancos...">
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($banks)): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="banksTable">
                    <thead class="table-light">
                        <tr>
                            <th>Banco</th>
                            <th>Código</th>
                            <th>Tipo</th>
                            <th>País</th>
                            <th>Cuentas</th>
                            <th>Tarjetas</th>
                            <th>Estado</th>
                            <th class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($banks as $bank): ?>
                            <tr data-bank-id="<?= $bank['id'] ?>">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bank-color me-3" style="width: 20px; height: 20px; background-color: <?= htmlspecialchars($bank['color']) ?>; border-radius: 50%;"></div>
                                        <div>
                                            <div class="fw-bold"><?= htmlspecialchars($bank['bank_name']) ?></div>
                                            <?php if (!empty($bank['description'])): ?>
                                                <small class="text-muted"><?= htmlspecialchars(substr($bank['description'], 0, 50)) ?><?= strlen($bank['description']) > 50 ? '...' : '' ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= htmlspecialchars($bank['bank_code'] ?: 'N/A') ?></span>
                                </td>
                                <td>
                                    <?php
                                    $typeLabels = [
                                        'commercial' => 'Comercial',
                                        'cooperative' => 'Cooperativa',
                                        'investment' => 'Inversión',
                                        'digital' => 'Digital',
                                        'other' => 'Otro'
                                    ];
                                    $typeColors = [
                                        'commercial' => 'primary',
                                        'cooperative' => 'success',
                                        'investment' => 'warning',
                                        'digital' => 'info',
                                        'other' => 'secondary'
                                    ];
                                    ?>
                                    <span class="badge bg-<?= $typeColors[$bank['bank_type']] ?? 'secondary' ?>">
                                        <?= $typeLabels[$bank['bank_type']] ?? 'Desconocido' ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark"><?= htmlspecialchars($bank['country']) ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= $bank['accounts_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-warning"><?= $bank['cards_count'] ?></span>
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input status-toggle" type="checkbox" 
                                               data-bank-id="<?= $bank['id'] ?>"
                                               <?= $bank['is_active'] ? 'checked' : '' ?>>
                                        <label class="form-check-label">
                                            <span class="badge bg-<?= $bank['is_active'] ? 'success' : 'secondary' ?>">
                                                <?= $bank['is_active'] ? 'Activo' : 'Inactivo' ?>
                                            </span>
                                        </label>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="?route=banks&action=edit&id=<?= $bank['id'] ?>"
                                           class="btn btn-outline-primary" title="Editar">
                                            <span class="icon-emoji">✏️</span>
                                        </a>
                                        <?php if ($bank['accounts_count'] == 0 && $bank['cards_count'] == 0): ?>
                                            <button type="button" class="btn btn-outline-danger delete-bank"
                                                    data-bank-id="<?= $bank['id'] ?>"
                                                    data-bank-name="<?= htmlspecialchars($bank['bank_name']) ?>"
                                                    title="Eliminar">
                                                <span class="icon-emoji">🗑️</span>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-outline-secondary" disabled title="No se puede eliminar (tiene cuentas/tarjetas)">
                                                <span class="icon-emoji">🔒</span>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <span class="icon-emoji" style="font-size: 3rem; color: #6c757d; margin-bottom: 1rem; display: block;">🏛️</span>
                <h5 class="text-muted">No hay bancos registrados</h5>
                <p class="text-muted mb-4">Comienza agregando tu primer banco o entidad financiera</p>
                <a href="?route=banks&action=create" class="btn btn-primary">
                    <span class="icon-emoji me-1">➕</span>
                    Crear Primer Banco
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirmar Eliminación
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>¿Estás seguro de que deseas eliminar el banco <strong id="bankNameToDelete"></strong>?</p>
                <p class="text-muted small">Esta acción no se puede deshacer.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form method="POST" action="?route=banks&action=delete" style="display: inline;">
                    <input type="hidden" name="id" id="bankIdToDelete">
                    <button type="submit" class="btn btn-danger">
                        <span class="icon-trash me-1">🗑️</span>
                        Eliminar
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Verificar si FontAwesome está cargado
    function checkFontAwesome() {
        const testIcon = document.createElement('i');
        testIcon.className = 'fas fa-edit';
        testIcon.style.position = 'absolute';
        testIcon.style.left = '-9999px';
        document.body.appendChild(testIcon);

        const computedStyle = window.getComputedStyle(testIcon, ':before');
        const content = computedStyle.content;

        document.body.removeChild(testIcon);

        // Si FontAwesome no está cargado, el content será "none" o vacío
        if (!content || content === 'none' || content === '""') {
            console.log('FontAwesome no detectado, manteniendo emojis...');
            // Los emojis ya están en el HTML como fallback
        } else {
            console.log('FontAwesome detectado correctamente');
        }
    }

    // Verificar después de que la página se haya cargado completamente
    setTimeout(checkFontAwesome, 500);

    // Search functionality
    const searchInput = document.getElementById('searchBanks');
    const table = document.getElementById('banksTable');

    if (searchInput && table) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }

    // Status toggle
    document.querySelectorAll('.status-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const bankId = this.dataset.bankId;
            const isActive = this.checked;

            fetch('?route=banks&action=toggle-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `id=${bankId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update badge
                    const badge = this.parentElement.querySelector('.badge');
                    if (data.new_status) {
                        badge.className = 'badge bg-success';
                        badge.textContent = 'Activo';
                    } else {
                        badge.className = 'badge bg-secondary';
                        badge.textContent = 'Inactivo';
                    }

                    // Show success message
                    showAlert('success', data.message);
                } else {
                    // Revert toggle
                    this.checked = !isActive;
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                // Revert toggle
                this.checked = !isActive;
                showAlert('danger', 'Error de conexión');
            });
        });
    });

    // Delete bank
    document.querySelectorAll('.delete-bank').forEach(button => {
        button.addEventListener('click', function() {
            const bankId = this.dataset.bankId;
            const bankName = this.dataset.bankName;

            document.getElementById('bankIdToDelete').value = bankId;
            document.getElementById('bankNameToDelete').textContent = bankName;

            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
