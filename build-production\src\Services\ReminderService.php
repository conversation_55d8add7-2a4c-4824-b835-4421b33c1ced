<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Repositories\ReminderRepository;
use ControlGastos\Repositories\CategoryRepository;
use ControlGastos\Repositories\AccountRepository;
use ControlGastos\Services\TransactionService;
use ControlGastos\Models\Reminder;
use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de gestión de recordatorios
 * Maneja la lógica de negocio para recordatorios y compromisos de pago
 */
class ReminderService
{
    private ReminderRepository $reminderRepository;
    private CategoryRepository $categoryRepository;
    private AccountRepository $accountRepository;
    private TransactionService $transactionService;
    private Logger $logger;

    public function __construct(
        ReminderRepository $reminderRepository,
        CategoryRepository $categoryRepository,
        AccountRepository $accountRepository,
        TransactionService $transactionService,
        Logger $logger
    ) {
        $this->reminderRepository = $reminderRepository;
        $this->categoryRepository = $categoryRepository;
        $this->accountRepository = $accountRepository;
        $this->transactionService = $transactionService;
        $this->logger = $logger;
    }

    /**
     * Crear nuevo recordatorio
     */
    public function createReminder(array $data, int $userId): array
    {
        try {
            // Verificar que la categoría pertenezca al usuario
            $category = $this->categoryRepository->findByIdAndUser($data['category_id'], $userId);
            if (!$category) {
                return [
                    'success' => false,
                    'message' => 'Categoría no encontrada'
                ];
            }

            // Verificar cuenta si se especifica
            if (!empty($data['account_id'])) {
                $account = $this->accountRepository->findByIdAndUser($data['account_id'], $userId);
                if (!$account) {
                    return [
                        'success' => false,
                        'message' => 'Cuenta no encontrada'
                    ];
                }
            }

            // Crear instancia de recordatorio
            $reminder = new Reminder();
            $reminder->setUserId($userId);
            $reminder->setTitle($data['title']);
            $reminder->setDescription($data['description']);
            $reminder->setType($data['type']);
            $reminder->setAmount((float) $data['amount']);
            $reminder->setCategoryId($data['category_id']);
            $reminder->setSubcategoryId($data['subcategory_id'] ?? null);
            $reminder->setAccountId($data['account_id'] ?? null);
            $reminder->setDueDate(new \DateTime($data['due_date']));
            $reminder->setPriority($data['priority'] ?? 'medium');
            $reminder->setStatus($data['status'] ?? 'pending');
            $reminder->setNotes($data['notes'] ?? null);

            // Configurar notificaciones
            $reminder->setNotificationEnabled($data['notification_enabled'] ?? true);
            $reminder->setNotificationDaysBefore($data['notification_days_before'] ?? 1);

            // Configurar auto-creación de transacciones
            $reminder->setAutoCreateTransaction($data['auto_create_transaction'] ?? false);

            // Configurar recurrencia si aplica
            if (!empty($data['is_recurring'])) {
                $reminder->setIsRecurring(true);
                $reminder->setRecurringType($data['recurring_type'] ?? null);
                $reminder->setRecurringInterval($data['recurring_interval'] ?? 1);
                
                if (!empty($data['recurring_end_date'])) {
                    $reminder->setRecurringEndDate(new \DateTime($data['recurring_end_date']));
                }
            }

            // Validar datos
            $errors = $reminder->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Crear recordatorio
            $reminder = $this->reminderRepository->create($reminder);

            $this->logger->info('Recordatorio creado', [
                'reminder_id' => $reminder->getId(),
                'user_id' => $userId,
                'title' => $reminder->getTitle(),
                'due_date' => $reminder->getDueDate()->format('Y-m-d H:i:s')
            ]);

            return [
                'success' => true,
                'message' => 'Recordatorio creado exitosamente',
                'reminder' => $reminder->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al crear recordatorio: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Actualizar recordatorio existente
     */
    public function updateReminder(int $reminderId, array $data, int $userId): array
    {
        try {
            // Buscar recordatorio
            $reminder = $this->reminderRepository->findByIdAndUser($reminderId, $userId);
            if (!$reminder) {
                return [
                    'success' => false,
                    'message' => 'Recordatorio no encontrado'
                ];
            }

            // Verificar nueva categoría si cambió
            if (isset($data['category_id']) && $data['category_id'] != $reminder->getCategoryId()) {
                $category = $this->categoryRepository->findByIdAndUser($data['category_id'], $userId);
                if (!$category) {
                    return [
                        'success' => false,
                        'message' => 'Nueva categoría no encontrada'
                    ];
                }
            }

            // Verificar nueva cuenta si cambió
            if (isset($data['account_id']) && !empty($data['account_id']) && $data['account_id'] != $reminder->getAccountId()) {
                $account = $this->accountRepository->findByIdAndUser($data['account_id'], $userId);
                if (!$account) {
                    return [
                        'success' => false,
                        'message' => 'Nueva cuenta no encontrada'
                    ];
                }
            }

            // Actualizar datos
            if (isset($data['title'])) {
                $reminder->setTitle($data['title']);
            }
            
            if (isset($data['description'])) {
                $reminder->setDescription($data['description']);
            }
            
            if (isset($data['type'])) {
                $reminder->setType($data['type']);
            }
            
            if (isset($data['amount'])) {
                $reminder->setAmount((float) $data['amount']);
            }
            
            if (isset($data['category_id'])) {
                $reminder->setCategoryId($data['category_id']);
            }
            
            if (isset($data['subcategory_id'])) {
                $reminder->setSubcategoryId($data['subcategory_id']);
            }
            
            if (isset($data['account_id'])) {
                $reminder->setAccountId($data['account_id']);
            }
            
            if (isset($data['due_date'])) {
                $reminder->setDueDate(new \DateTime($data['due_date']));
            }
            
            if (isset($data['priority'])) {
                $reminder->setPriority($data['priority']);
            }
            
            if (isset($data['status'])) {
                $reminder->setStatus($data['status']);
                
                // Si se marca como completado, establecer fecha
                if ($data['status'] === 'completed' && !$reminder->getCompletedAt()) {
                    $reminder->setCompletedAt(new \DateTime());
                }
            }
            
            if (isset($data['notes'])) {
                $reminder->setNotes($data['notes']);
            }

            // Actualizar configuración de notificaciones
            if (isset($data['notification_enabled'])) {
                $reminder->setNotificationEnabled((bool) $data['notification_enabled']);
            }
            
            if (isset($data['notification_days_before'])) {
                $reminder->setNotificationDaysBefore($data['notification_days_before']);
            }

            // Actualizar auto-creación de transacciones
            if (isset($data['auto_create_transaction'])) {
                $reminder->setAutoCreateTransaction((bool) $data['auto_create_transaction']);
            }

            // Actualizar recurrencia
            if (isset($data['is_recurring'])) {
                $reminder->setIsRecurring((bool) $data['is_recurring']);
                
                if ($reminder->isRecurring()) {
                    $reminder->setRecurringType($data['recurring_type'] ?? null);
                    $reminder->setRecurringInterval($data['recurring_interval'] ?? 1);
                    
                    if (!empty($data['recurring_end_date'])) {
                        $reminder->setRecurringEndDate(new \DateTime($data['recurring_end_date']));
                    } else {
                        $reminder->setRecurringEndDate(null);
                    }
                } else {
                    $reminder->setRecurringType(null);
                    $reminder->setRecurringInterval(null);
                    $reminder->setRecurringEndDate(null);
                }
            }

            // Validar datos
            $errors = $reminder->validate();
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos',
                    'errors' => $errors
                ];
            }

            // Actualizar recordatorio
            $this->reminderRepository->update($reminder);

            $this->logger->info('Recordatorio actualizado', [
                'reminder_id' => $reminder->getId(),
                'user_id' => $userId,
                'title' => $reminder->getTitle()
            ]);

            return [
                'success' => true,
                'message' => 'Recordatorio actualizado exitosamente',
                'reminder' => $reminder->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al actualizar recordatorio: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Eliminar recordatorio
     */
    public function deleteReminder(int $reminderId, int $userId): array
    {
        try {
            // Buscar recordatorio
            $reminder = $this->reminderRepository->findByIdAndUser($reminderId, $userId);
            if (!$reminder) {
                return [
                    'success' => false,
                    'message' => 'Recordatorio no encontrado'
                ];
            }

            // Eliminar recordatorio
            $this->reminderRepository->delete($reminderId);

            $this->logger->info('Recordatorio eliminado', [
                'reminder_id' => $reminderId,
                'user_id' => $userId,
                'title' => $reminder->getTitle()
            ]);

            return [
                'success' => true,
                'message' => 'Recordatorio eliminado exitosamente'
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al eliminar recordatorio: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener recordatorio por ID
     */
    public function getReminder(int $reminderId, int $userId): array
    {
        try {
            $reminder = $this->reminderRepository->findByIdAndUser($reminderId, $userId);
            
            if (!$reminder) {
                return [
                    'success' => false,
                    'message' => 'Recordatorio no encontrado'
                ];
            }

            return [
                'success' => true,
                'reminder' => $reminder->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener recordatorio: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener recordatorios del usuario
     */
    public function getUserReminders(int $userId, array $filters = []): array
    {
        try {
            $page = $filters['page'] ?? 1;
            $perPage = $filters['per_page'] ?? 20;
            
            $result = $this->reminderRepository->getPaginated($userId, $page, $perPage, $filters);
            
            return [
                'success' => true,
                'reminders' => $result['data'],
                'pagination' => [
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'per_page' => $result['per_page'],
                    'total_pages' => $result['total_pages']
                ]
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener recordatorios: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Marcar recordatorio como completado
     */
    public function markAsCompleted(int $reminderId, int $userId, bool $createTransaction = false): array
    {
        try {
            // Buscar recordatorio
            $reminder = $this->reminderRepository->findByIdAndUser($reminderId, $userId);
            if (!$reminder) {
                return [
                    'success' => false,
                    'message' => 'Recordatorio no encontrado'
                ];
            }

            // Marcar como completado
            $reminder->markAsCompleted();

            // Crear transacción automáticamente si está configurado
            if (($createTransaction || $reminder->getAutoCreateTransaction()) && $reminder->getAccountId()) {
                $transactionData = [
                    'account_id' => $reminder->getAccountId(),
                    'category_id' => $reminder->getCategoryId(),
                    'subcategory_id' => $reminder->getSubcategoryId(),
                    'type' => $reminder->getType() === 'income' ? 'income' : 'expense',
                    'amount' => $reminder->getAmount(),
                    'description' => $reminder->getTitle(),
                    'transaction_date' => $reminder->getDueDate()->format('Y-m-d H:i:s'),
                    'reference' => 'Recordatorio #' . $reminder->getId(),
                    'notes' => $reminder->getNotes()
                ];

                $transactionResult = $this->transactionService->createTransaction($transactionData, $userId);
                
                if (!$transactionResult['success']) {
                    $this->logger->warning('Error al crear transacción automática', [
                        'reminder_id' => $reminderId,
                        'error' => $transactionResult['message']
                    ]);
                }
            }

            // Crear próximo recordatorio si es recurrente
            if ($reminder->isRecurring()) {
                $nextDate = $reminder->getNextRecurringDate();
                if ($nextDate) {
                    $nextReminderData = $reminder->toArray();
                    unset($nextReminderData['id'], $nextReminderData['completed_at'], $nextReminderData['last_notification_sent']);
                    $nextReminderData['due_date'] = $nextDate->format('Y-m-d H:i:s');
                    $nextReminderData['status'] = 'pending';

                    $nextReminder = new Reminder($nextReminderData);
                    $this->reminderRepository->create($nextReminder);
                }
            }

            // Actualizar recordatorio actual
            $this->reminderRepository->update($reminder);

            $this->logger->info('Recordatorio marcado como completado', [
                'reminder_id' => $reminderId,
                'user_id' => $userId,
                'created_transaction' => $createTransaction || $reminder->getAutoCreateTransaction()
            ]);

            return [
                'success' => true,
                'message' => 'Recordatorio marcado como completado',
                'reminder' => $reminder->toDetailedArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al marcar recordatorio como completado: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener recordatorios vencidos
     */
    public function getOverdueReminders(int $userId): array
    {
        try {
            $reminders = $this->reminderRepository->findOverdue($userId);
            
            return [
                'success' => true,
                'reminders' => $reminders,
                'count' => count($reminders)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener recordatorios vencidos: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener recordatorios que vencen pronto
     */
    public function getDueSoonReminders(int $userId, int $days = 7): array
    {
        try {
            $reminders = $this->reminderRepository->findDueSoon($userId, $days);
            
            return [
                'success' => true,
                'reminders' => $reminders,
                'count' => count($reminders)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener recordatorios próximos: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener estadísticas de recordatorios
     */
    public function getReminderStats(int $userId): array
    {
        try {
            $stats = $this->reminderRepository->getStats($userId);
            $typeSummary = $this->reminderRepository->getTypeSummary($userId);
            
            return [
                'success' => true,
                'stats' => [
                    'general' => $stats,
                    'by_type' => $typeSummary
                ]
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener estadísticas: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Buscar recordatorios
     */
    public function searchReminders(int $userId, string $search): array
    {
        try {
            $reminders = $this->reminderRepository->search($userId, $search);
            
            return [
                'success' => true,
                'reminders' => $reminders,
                'count' => count($reminders)
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en búsqueda de recordatorios: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Procesar notificaciones de recordatorios
     */
    public function processNotifications(): array
    {
        try {
            $reminders = $this->reminderRepository->findNeedingNotification();
            $sent = 0;
            $errors = [];

            foreach ($reminders as $reminderData) {
                try {
                    $reminder = new Reminder($reminderData);
                    
                    // Aquí se enviaría la notificación (email, SMS, etc.)
                    // Por ahora solo simulamos el envío
                    $this->sendNotification($reminder, $reminderData);
                    
                    // Marcar notificación como enviada
                    $reminder->markNotificationSent();
                    $this->reminderRepository->update($reminder);
                    
                    $sent++;

                } catch (Exception $e) {
                    $errors[] = "Error enviando notificación para recordatorio {$reminderData['id']}: " . $e->getMessage();
                }
            }

            $this->logger->info('Notificaciones de recordatorios procesadas', [
                'sent' => $sent,
                'errors' => count($errors)
            ]);

            return [
                'success' => true,
                'sent' => $sent,
                'errors' => $errors
            ];

        } catch (Exception $e) {
            $this->logger->error('Error procesando notificaciones: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Actualizar estados automáticamente
     */
    public function updateStatuses(): array
    {
        try {
            $updated = $this->reminderRepository->updateOverdueStatuses();
            
            $this->logger->info('Estados de recordatorios actualizados', [
                'updated' => $updated
            ]);

            return [
                'success' => true,
                'updated' => $updated
            ];

        } catch (Exception $e) {
            $this->logger->error('Error actualizando estados: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener recordatorios para calendario
     */
    public function getCalendarReminders(int $userId, \DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $reminders = $this->reminderRepository->getCalendarReminders($userId, $startDate, $endDate);
            
            return [
                'success' => true,
                'reminders' => $reminders
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al obtener recordatorios del calendario: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Enviar notificación (método placeholder)
     */
    private function sendNotification(Reminder $reminder, array $userData): void
    {
        // Aquí se implementaría el envío real de notificaciones
        // Por ejemplo: email, SMS, push notifications, etc.
        
        $this->logger->info('Notificación enviada', [
            'reminder_id' => $reminder->getId(),
            'user_email' => $userData['user_email'] ?? null,
            'title' => $reminder->getTitle(),
            'due_date' => $reminder->getDueDate()->format('Y-m-d H:i:s')
        ]);
    }

    /**
     * Obtener tipos de recordatorio disponibles
     */
    public function getReminderTypes(): array
    {
        return Reminder::TYPES;
    }

    /**
     * Obtener prioridades disponibles
     */
    public function getPriorities(): array
    {
        return Reminder::PRIORITIES;
    }

    /**
     * Obtener estados disponibles
     */
    public function getStatuses(): array
    {
        return Reminder::STATUSES;
    }

    /**
     * Obtener tipos de recurrencia disponibles
     */
    public function getRecurringTypes(): array
    {
        return Reminder::RECURRING_TYPES;
    }
}
