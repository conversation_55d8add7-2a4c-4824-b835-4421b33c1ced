<?php

declare(strict_types=1);

namespace ControlGastos\Repositories;

use ControlGastos\Core\Database;
use ControlGastos\Models\Transaction;
use Exception;

/**
 * Repositorio de transacciones
 * Maneja todas las operaciones de base de datos relacionadas con transacciones
 */
class TransactionRepository
{
    private Database $database;

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Crear nueva transacción
     */
    public function create(Transaction $transaction): Transaction
    {
        $query = "
            INSERT INTO transactions (
                user_id, account_id, category_id, subcategory_id, type, amount,
                description, transaction_date, reference, notes, tags,
                is_recurring, recurring_type, recurring_interval, recurring_end_date,
                created_at, updated_at
            ) VALUES (
                :user_id, :account_id, :category_id, :subcategory_id, :type, :amount,
                :description, :transaction_date, :reference, :notes, :tags,
                :is_recurring, :recurring_type, :recurring_interval, :recurring_end_date,
                :created_at, :updated_at
            )
        ";

        $params = [
            'user_id' => $transaction->getUserId(),
            'account_id' => $transaction->getAccountId(),
            'category_id' => $transaction->getCategoryId(),
            'subcategory_id' => $transaction->getSubcategoryId(),
            'type' => $transaction->getType(),
            'amount' => $transaction->getAmount(),
            'description' => $transaction->getDescription(),
            'transaction_date' => $transaction->getTransactionDate()?->format('Y-m-d H:i:s'),
            'reference' => $transaction->getReference(),
            'notes' => $transaction->getNotes(),
            'tags' => implode(',', $transaction->getTags()),
            'is_recurring' => $transaction->isRecurring() ? 1 : 0,
            'recurring_type' => $transaction->getRecurringType(),
            'recurring_interval' => $transaction->getRecurringInterval(),
            'recurring_end_date' => $transaction->getRecurringEndDate()?->format('Y-m-d H:i:s'),
            'created_at' => $transaction->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $transaction->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        $id = $this->database->insert($query, $params);
        $transaction->setId($id);

        return $transaction;
    }

    /**
     * Buscar transacción por ID
     */
    public function findById(int $id): ?Transaction
    {
        $query = "SELECT * FROM transactions WHERE id = :id";
        $result = $this->database->selectOne($query, ['id' => $id]);

        return $result ? new Transaction($result) : null;
    }

    /**
     * Buscar transacción por ID y usuario
     */
    public function findByIdAndUser(int $id, int $userId): ?Transaction
    {
        $query = "SELECT * FROM transactions WHERE id = :id AND user_id = :user_id";
        $result = $this->database->selectOne($query, ['id' => $id, 'user_id' => $userId]);

        return $result ? new Transaction($result) : null;
    }

    /**
     * Obtener transacciones por usuario
     */
    public function findByUser(int $userId, int $limit = 50, int $offset = 0): array
    {
        $query = "
            SELECT t.*, 
                   a.name as account_name,
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name
            FROM transactions t
            INNER JOIN accounts a ON t.account_id = a.id
            INNER JOIN categories c ON t.category_id = c.id
            LEFT JOIN subcategories s ON t.subcategory_id = s.id
            WHERE t.user_id = :user_id
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT :limit OFFSET :offset
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'limit' => $limit,
            'offset' => $offset
        ]);

        return array_map(fn($row) => $this->mapTransactionWithRelations($row), $results);
    }

    /**
     * Obtener transacciones por cuenta
     */
    public function findByAccount(int $accountId, int $userId): array
    {
        $query = "
            SELECT t.*, 
                   a.name as account_name,
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name
            FROM transactions t
            INNER JOIN accounts a ON t.account_id = a.id
            INNER JOIN categories c ON t.category_id = c.id
            LEFT JOIN subcategories s ON t.subcategory_id = s.id
            WHERE t.account_id = :account_id AND t.user_id = :user_id
            ORDER BY t.transaction_date DESC
        ";

        $results = $this->database->select($query, [
            'account_id' => $accountId,
            'user_id' => $userId
        ]);

        return array_map(fn($row) => $this->mapTransactionWithRelations($row), $results);
    }

    /**
     * Obtener transacciones por categoría
     */
    public function findByCategory(int $categoryId, int $userId): array
    {
        $query = "
            SELECT t.*, 
                   a.name as account_name,
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name
            FROM transactions t
            INNER JOIN accounts a ON t.account_id = a.id
            INNER JOIN categories c ON t.category_id = c.id
            LEFT JOIN subcategories s ON t.subcategory_id = s.id
            WHERE t.category_id = :category_id AND t.user_id = :user_id
            ORDER BY t.transaction_date DESC
        ";

        $results = $this->database->select($query, [
            'category_id' => $categoryId,
            'user_id' => $userId
        ]);

        return array_map(fn($row) => $this->mapTransactionWithRelations($row), $results);
    }

    /**
     * Obtener transacciones por rango de fechas
     */
    public function findByDateRange(int $userId, \DateTime $startDate, \DateTime $endDate): array
    {
        $query = "
            SELECT t.*, 
                   a.name as account_name,
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name
            FROM transactions t
            INNER JOIN accounts a ON t.account_id = a.id
            INNER JOIN categories c ON t.category_id = c.id
            LEFT JOIN subcategories s ON t.subcategory_id = s.id
            WHERE t.user_id = :user_id 
            AND t.transaction_date BETWEEN :start_date AND :end_date
            ORDER BY t.transaction_date DESC
        ";

        $results = $this->database->select($query, [
            'user_id' => $userId,
            'start_date' => $startDate->format('Y-m-d H:i:s'),
            'end_date' => $endDate->format('Y-m-d H:i:s')
        ]);

        return array_map(fn($row) => $this->mapTransactionWithRelations($row), $results);
    }

    /**
     * Actualizar transacción
     */
    public function update(Transaction $transaction): bool
    {
        $query = "
            UPDATE transactions SET
                account_id = :account_id,
                category_id = :category_id,
                subcategory_id = :subcategory_id,
                type = :type,
                amount = :amount,
                description = :description,
                transaction_date = :transaction_date,
                reference = :reference,
                notes = :notes,
                tags = :tags,
                is_recurring = :is_recurring,
                recurring_type = :recurring_type,
                recurring_interval = :recurring_interval,
                recurring_end_date = :recurring_end_date,
                updated_at = :updated_at
            WHERE id = :id
        ";

        $params = [
            'id' => $transaction->getId(),
            'account_id' => $transaction->getAccountId(),
            'category_id' => $transaction->getCategoryId(),
            'subcategory_id' => $transaction->getSubcategoryId(),
            'type' => $transaction->getType(),
            'amount' => $transaction->getAmount(),
            'description' => $transaction->getDescription(),
            'transaction_date' => $transaction->getTransactionDate()?->format('Y-m-d H:i:s'),
            'reference' => $transaction->getReference(),
            'notes' => $transaction->getNotes(),
            'tags' => implode(',', $transaction->getTags()),
            'is_recurring' => $transaction->isRecurring() ? 1 : 0,
            'recurring_type' => $transaction->getRecurringType(),
            'recurring_interval' => $transaction->getRecurringInterval(),
            'recurring_end_date' => $transaction->getRecurringEndDate()?->format('Y-m-d H:i:s'),
            'updated_at' => $transaction->getUpdatedAt()->format('Y-m-d H:i:s')
        ];

        return $this->database->update($query, $params) > 0;
    }

    /**
     * Eliminar transacción
     */
    public function delete(int $id): bool
    {
        $query = "DELETE FROM transactions WHERE id = :id";
        return $this->database->delete($query, ['id' => $id]) > 0;
    }

    /**
     * Obtener balance por cuenta
     */
    public function getAccountBalance(int $accountId): float
    {
        $query = "
            SELECT 
                COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END), 0) as balance
            FROM transactions 
            WHERE account_id = :account_id
        ";

        $result = $this->database->selectOne($query, ['account_id' => $accountId]);
        return (float) $result['balance'];
    }

    /**
     * Obtener estadísticas por usuario
     */
    public function getStats(int $userId, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        $whereClause = "WHERE user_id = :user_id";
        $params = ['user_id' => $userId];

        if ($startDate && $endDate) {
            $whereClause .= " AND transaction_date BETWEEN :start_date AND :end_date";
            $params['start_date'] = $startDate->format('Y-m-d H:i:s');
            $params['end_date'] = $endDate->format('Y-m-d H:i:s');
        }

        $query = "
            SELECT 
                COUNT(*) as total_transactions,
                SUM(CASE WHEN type = 'income' THEN 1 ELSE 0 END) as total_income_transactions,
                SUM(CASE WHEN type = 'expense' THEN 1 ELSE 0 END) as total_expense_transactions,
                COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as total_income,
                COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as total_expenses,
                COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END), 0) as net_balance,
                COALESCE(AVG(CASE WHEN type = 'income' THEN amount END), 0) as avg_income,
                COALESCE(AVG(CASE WHEN type = 'expense' THEN amount END), 0) as avg_expense
            FROM transactions 
            {$whereClause}
        ";

        return $this->database->selectOne($query, $params) ?: [];
    }

    /**
     * Obtener transacciones recurrentes pendientes
     */
    public function getRecurringTransactionsDue(): array
    {
        $query = "
            SELECT * FROM transactions 
            WHERE is_recurring = 1 
            AND (recurring_end_date IS NULL OR recurring_end_date > NOW())
            ORDER BY transaction_date ASC
        ";

        $results = $this->database->select($query);
        return array_map(fn($row) => new Transaction($row), $results);
    }

    /**
     * Buscar transacciones
     */
    public function search(int $userId, string $search, array $filters = []): array
    {
        $whereConditions = ['t.user_id = :user_id'];
        $params = ['user_id' => $userId];

        // Búsqueda en descripción, referencia y notas
        if (!empty($search)) {
            $whereConditions[] = "(t.description LIKE :search OR t.reference LIKE :search OR t.notes LIKE :search)";
            $params['search'] = '%' . $search . '%';
        }

        // Filtros adicionales
        if (!empty($filters['type'])) {
            $whereConditions[] = "t.type = :type";
            $params['type'] = $filters['type'];
        }

        if (!empty($filters['account_id'])) {
            $whereConditions[] = "t.account_id = :account_id";
            $params['account_id'] = $filters['account_id'];
        }

        if (!empty($filters['category_id'])) {
            $whereConditions[] = "t.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        if (!empty($filters['start_date'])) {
            $whereConditions[] = "t.transaction_date >= :start_date";
            $params['start_date'] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $whereConditions[] = "t.transaction_date <= :end_date";
            $params['end_date'] = $filters['end_date'];
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        $query = "
            SELECT t.*, 
                   a.name as account_name,
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name
            FROM transactions t
            INNER JOIN accounts a ON t.account_id = a.id
            INNER JOIN categories c ON t.category_id = c.id
            LEFT JOIN subcategories s ON t.subcategory_id = s.id
            {$whereClause}
            ORDER BY t.transaction_date DESC
            LIMIT 100
        ";

        $results = $this->database->select($query, $params);
        return array_map(fn($row) => $this->mapTransactionWithRelations($row), $results);
    }

    /**
     * Obtener transacciones con paginación
     */
    public function getPaginated(int $userId, int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ['t.user_id = :user_id'];
        $params = ['user_id' => $userId];

        // Aplicar filtros
        if (!empty($filters['type'])) {
            $whereConditions[] = "t.type = :type";
            $params['type'] = $filters['type'];
        }

        if (!empty($filters['account_id'])) {
            $whereConditions[] = "t.account_id = :account_id";
            $params['account_id'] = $filters['account_id'];
        }

        if (!empty($filters['category_id'])) {
            $whereConditions[] = "t.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = "(t.description LIKE :search OR t.reference LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($filters['start_date'])) {
            $whereConditions[] = "t.transaction_date >= :start_date";
            $params['start_date'] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $whereConditions[] = "t.transaction_date <= :end_date";
            $params['end_date'] = $filters['end_date'];
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Consulta principal
        $query = "
            SELECT t.*, 
                   a.name as account_name,
                   c.name as category_name, c.color as category_color, c.icon as category_icon,
                   s.name as subcategory_name
            FROM transactions t
            INNER JOIN accounts a ON t.account_id = a.id
            INNER JOIN categories c ON t.category_id = c.id
            LEFT JOIN subcategories s ON t.subcategory_id = s.id
            {$whereClause}
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT :limit OFFSET :offset
        ";

        $params['limit'] = $perPage;
        $params['offset'] = $offset;

        $results = $this->database->select($query, $params);
        $transactions = array_map(fn($row) => $this->mapTransactionWithRelations($row), $results);

        // Contar total
        $countQuery = "
            SELECT COUNT(*) as total 
            FROM transactions t
            INNER JOIN accounts a ON t.account_id = a.id
            INNER JOIN categories c ON t.category_id = c.id
            LEFT JOIN subcategories s ON t.subcategory_id = s.id
            {$whereClause}
        ";
        unset($params['limit'], $params['offset']);
        $totalResult = $this->database->selectOne($countQuery, $params);
        $total = $totalResult['total'];

        return [
            'data' => $transactions,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }

    /**
     * Mapear transacción con relaciones
     */
    private function mapTransactionWithRelations(array $row): array
    {
        $transaction = new Transaction($row);
        $transactionArray = $transaction->toDetailedArray();
        
        // Agregar información de relaciones
        $transactionArray['account_name'] = $row['account_name'] ?? null;
        $transactionArray['category_name'] = $row['category_name'] ?? null;
        $transactionArray['category_color'] = $row['category_color'] ?? null;
        $transactionArray['category_icon'] = $row['category_icon'] ?? null;
        $transactionArray['subcategory_name'] = $row['subcategory_name'] ?? null;
        
        return $transactionArray;
    }

    /**
     * Obtener resumen por categorías
     */
    public function getCategorySummary(int $userId, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        $whereClause = "WHERE t.user_id = :user_id";
        $params = ['user_id' => $userId];

        if ($startDate && $endDate) {
            $whereClause .= " AND t.transaction_date BETWEEN :start_date AND :end_date";
            $params['start_date'] = $startDate->format('Y-m-d H:i:s');
            $params['end_date'] = $endDate->format('Y-m-d H:i:s');
        }

        $query = "
            SELECT 
                c.id as category_id,
                c.name as category_name,
                c.color as category_color,
                c.icon as category_icon,
                t.type,
                COUNT(*) as transaction_count,
                SUM(t.amount) as total_amount
            FROM transactions t
            INNER JOIN categories c ON t.category_id = c.id
            {$whereClause}
            GROUP BY c.id, c.name, c.color, c.icon, t.type
            ORDER BY total_amount DESC
        ";

        return $this->database->select($query, $params);
    }
}
