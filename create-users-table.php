<?php
/**
 * Script para crear la tabla de usuarios
 */

try {
    // Configuración de la base de datos
    $host = 'localhost';
    $dbname = 'controlGastos';
    $username = 'root';
    $password = '';

    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "🔧 CREANDO TABLA DE USUARIOS\n";
    echo "=" . str_repeat("=", 50) . "\n\n";

    // Verificar si la tabla existe
    $sql = "SHOW TABLES LIKE 'users'";
    $stmt = $pdo->query($sql);
    $tableExists = $stmt->rowCount() > 0;

    if (!$tableExists) {
        echo "⚠️ La tabla 'users' no existe. Creándola...\n";
        
        $sql = "
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(150) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            is_admin TINYINT(1) DEFAULT 0,
            email_verified_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "✅ Tabla 'users' creada exitosamente\n";
    } else {
        echo "✅ La tabla 'users' ya existe\n";
    }

    // Verificar si existe un usuario de prueba
    $sql = "SELECT COUNT(*) FROM users";
    $stmt = $pdo->query($sql);
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        echo "\n📝 CREANDO USUARIO DE PRUEBA...\n";
        
        $sql = "INSERT INTO users (name, email, password, is_active, is_admin) VALUES (?, ?, ?, 1, 1)";
        $stmt = $pdo->prepare($sql);
        $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
        $stmt->execute(['Admin', '<EMAIL>', $hashedPassword]);
        
        echo "✅ Usuario de prueba creado:\n";
        echo "   Email: <EMAIL>\n";
        echo "   Password: 123456\n";
    } else {
        echo "\n✅ Ya existen usuarios en la base de datos ($count usuarios)\n";
    }

    echo "\n🎊 ¡PROCESO COMPLETADO EXITOSAMENTE!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
