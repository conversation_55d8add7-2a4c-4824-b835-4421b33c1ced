/* Control de Gastos - Custom CSS */

/* Variables CSS */
:root {
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    --sidebar-width: 280px;
    --header-height: 70px;
    
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.12);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.15);
    --shadow-xl: 0 12px 24px rgba(0,0,0,0.18);
    
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Styles */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #1e293b;
}

.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Layout */
.main-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
    transition: var(--transition);
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.card-header {
    background-color: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Gradient Cards */
.card-gradient-primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
}

.card-gradient-success {
    background: var(--success-gradient);
    color: white;
    border: none;
}

.card-gradient-danger {
    background: var(--danger-gradient);
    color: white;
    border: none;
}

.card-gradient-warning {
    background: var(--warning-gradient);
    color: white;
    border: none;
}

.card-gradient-info {
    background: var(--info-gradient);
    color: white;
    border: none;
}

.card-gradient-dark {
    background: var(--dark-gradient);
    color: white;
    border: none;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
    border: none;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
}

.btn-gradient-primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
}

.btn-gradient-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-gradient-success {
    background: var(--success-gradient);
    color: white;
    border: none;
}

.btn-gradient-success:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

/* Forms */
.form-control, .form-select {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 0.625rem 0.875rem;
    transition: var(--transition);
    background-color: #ffffff;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background-color: #f8fafc;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    color: #6b7280;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    border-color: #f1f5f9;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: #f8fafc;
}

/* Financial Amounts */
.amount-positive {
    color: #059669;
    font-weight: 600;
}

.amount-negative {
    color: #dc2626;
    font-weight: 600;
}

.amount-neutral {
    color: #6b7280;
    font-weight: 600;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: #ecfdf5;
    color: #065f46;
    border-left: 4px solid #10b981;
}

.alert-danger {
    background-color: #fef2f2;
    color: #991b1b;
    border-left: 4px solid #ef4444;
}

.alert-warning {
    background-color: #fffbeb;
    color: #92400e;
    border-left: 4px solid #f59e0b;
}

.alert-info {
    background-color: #eff6ff;
    color: #1e40af;
    border-left: 4px solid #3b82f6;
}

/* Modals */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid #e5e7eb;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e5e7eb;
    padding: 1.5rem;
}

/* Dropdowns */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: #f8fafc;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e5e7eb;
}

.progress-bar {
    border-radius: 4px;
}

/* Breadcrumbs */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #9ca3af;
}

.breadcrumb-item a {
    color: #6b7280;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: #374151;
    font-weight: 500;
}

/* Pagination */
.pagination {
    gap: 0.25rem;
}

.page-link {
    border: none;
    border-radius: 8px;
    color: #6b7280;
    padding: 0.5rem 0.75rem;
    transition: var(--transition);
}

.page-link:hover {
    background-color: #f3f4f6;
    color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
}

/* Toasts */
.toast {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

/* Loading Spinner */
.loading-overlay {
    backdrop-filter: blur(4px);
    background-color: rgba(255, 255, 255, 0.8);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Utility Classes */
.shadow-soft {
    box-shadow: var(--shadow-sm);
}

.shadow-medium {
    box-shadow: var(--shadow-md);
}

.shadow-strong {
    box-shadow: var(--shadow-lg);
}

.rounded-soft {
    border-radius: var(--border-radius);
}

.rounded-medium {
    border-radius: var(--border-radius-lg);
}

.rounded-strong {
    border-radius: var(--border-radius-xl);
}

/* Responsive Utilities */
@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 0.875rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.75rem 0.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    [data-bs-theme="auto"] {
        --bs-body-bg: #1a202c;
        --bs-body-color: #e2e8f0;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .main-header,
    .btn,
    .pagination,
    .modal,
    .toast-container {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}
