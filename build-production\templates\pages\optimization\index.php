<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Optimización y Rendimiento</h2>
    <div>
        <button class="btn btn-outline-primary" onclick="refreshMetrics()">
            <i class="fas fa-sync"></i> Actualizar Métricas
        </button>
        <a href="/optimization/report" class="btn btn-outline-secondary">
            <i class="fas fa-download"></i> Generar Reporte
        </a>
    </div>
</div>

<!-- Navegación -->
<div class="row mb-4">
    <div class="col-12">
        <div class="nav nav-pills justify-content-center" role="tablist">
            <a class="nav-link active" href="/optimization">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a class="nav-link" href="/optimization/performance">
                <i class="fas fa-chart-line"></i> Performance
            </a>
            <a class="nav-link" href="/optimization/cache">
                <i class="fas fa-memory"></i> Cache
            </a>
            <a class="nav-link" href="/optimization/database">
                <i class="fas fa-database"></i> Base de Datos
            </a>
        </div>
    </div>
</div>

<!-- Alertas de Performance -->
<?php if (!empty($performance_report['alerts'])): ?>
<div class="row mb-4">
    <?php foreach ($performance_report['alerts'] as $alert): ?>
        <div class="col-12 mb-2">
            <div class="alert alert-<?= $alert['level'] === 'critical' ? 'danger' : 'warning' ?> alert-dismissible fade show">
                <i class="fas fa-<?= $alert['level'] === 'critical' ? 'exclamation-triangle' : 'exclamation-circle' ?>"></i>
                <strong><?= ucfirst($alert['level']) ?>:</strong> <?= htmlspecialchars($alert['message']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<!-- Métricas Principales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">Tiempo de Respuesta</h5>
                <h3><?= number_format($performance_report['summary']['request_duration'], 3) ?>s</h3>
                <small class="text-muted">Último request</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Uso de Memoria</h5>
                <h3><?= $performance_report['summary']['memory_usage_mb'] ?> MB</h3>
                <small class="text-muted"><?= $performance_report['summary']['memory_usage_percentage'] ?>% del límite</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">Cache</h5>
                <h3><?= $cache_stats['memory_cache_size'] ?? 0 ?></h3>
                <small class="text-muted">Entradas en memoria</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">Consultas BD</h5>
                <h3><?= $query_analysis['total_queries'] ?? 0 ?></h3>
                <small class="text-muted">En esta sesión</small>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos de Performance -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-area"></i> Uso de Memoria</h5>
            </div>
            <div class="card-body">
                <canvas id="memoryChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Tiempo de Respuesta</h5>
            </div>
            <div class="card-body">
                <canvas id="responseTimeChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Estadísticas de Cache -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-memory"></i> Estadísticas de Cache</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary"><?= $cache_stats['memory_cache_size'] ?? 0 ?></h4>
                        <p class="mb-0 text-muted">Entradas en Memoria</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info"><?= $cache_stats['disk_cache_files'] ?? 0 ?></h4>
                        <p class="mb-0 text-muted">Archivos en Disco</p>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-success"><?= isset($cache_stats['total_size']) ? number_format($cache_stats['total_size'] / 1024 / 1024, 2) : 0 ?> MB</h6>
                        <p class="mb-0 text-muted">Tamaño Total</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-warning"><?= $cache_stats['expired_files'] ?? 0 ?></h6>
                        <p class="mb-0 text-muted">Archivos Expirados</p>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-warning btn-sm" onclick="clearCache('expired')">
                        <i class="fas fa-trash"></i> Limpiar Expirados
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="clearCache('all')">
                        <i class="fas fa-trash-alt"></i> Limpiar Todo
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-database"></i> Estadísticas de Base de Datos</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($db_stats['table_sizes'])): ?>
                    <h6>Top Tablas por Tamaño:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Tabla</th>
                                    <th>Tamaño</th>
                                    <th>Filas</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($db_stats['table_sizes'], 0, 5) as $table): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($table['TABLE_NAME']) ?></td>
                                        <td><?= $table['size_mb'] ?> MB</td>
                                        <td><?= number_format($table['TABLE_ROWS']) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="optimizeDatabase('optimize_indexes')">
                        <i class="fas fa-tools"></i> Optimizar Índices
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Análisis de Consultas -->
<?php if (!empty($query_analysis['slow_queries'])): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-exclamation-triangle text-warning"></i> Consultas Lentas Detectadas</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Consulta</th>
                        <th>Tiempo (s)</th>
                        <th>Timestamp</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (array_slice($query_analysis['slow_queries'], 0, 5) as $query): ?>
                        <tr>
                            <td>
                                <code class="small"><?= htmlspecialchars(substr($query['query'], 0, 100)) ?>...</code>
                            </td>
                            <td>
                                <span class="badge bg-warning"><?= number_format($query['execution_time'], 3) ?>s</span>
                            </td>
                            <td>
                                <small><?= date('H:i:s', (int) $query['timestamp']) ?></small>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Recomendaciones -->
<?php if (!empty($performance_report['recommendations'])): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Recomendaciones de Optimización</h5>
    </div>
    <div class="card-body">
        <?php foreach ($performance_report['recommendations'] as $recommendation): ?>
            <div class="alert alert-<?= $recommendation['priority'] === 'high' ? 'warning' : 'info' ?> mb-2">
                <strong><?= ucfirst($recommendation['type']) ?>:</strong>
                <?= htmlspecialchars($recommendation['message']) ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<!-- Acciones Rápidas -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-tools"></i> Acciones Rápidas</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <button class="btn btn-outline-primary w-100 mb-2" onclick="clearCache('all')">
                    <i class="fas fa-trash"></i> Limpiar Cache
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-success w-100 mb-2" onclick="optimizeDatabase('optimize_indexes')">
                    <i class="fas fa-tools"></i> Optimizar BD
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-info w-100 mb-2" onclick="refreshMetrics()">
                    <i class="fas fa-sync"></i> Actualizar Métricas
                </button>
            </div>
            <div class="col-md-3">
                <a href="/optimization/report" class="btn btn-outline-secondary w-100 mb-2">
                    <i class="fas fa-download"></i> Generar Reporte
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Variables globales para los gráficos
let memoryChart, responseTimeChart;

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    startRealTimeUpdates();
});

function initializeCharts() {
    // Gráfico de memoria
    const memoryCtx = document.getElementById('memoryChart').getContext('2d');
    memoryChart = new Chart(memoryCtx, {
        type: 'doughnut',
        data: {
            labels: ['Usado', 'Disponible'],
            datasets: [{
                data: [
                    <?= $performance_report['summary']['memory_usage_percentage'] ?>,
                    <?= 100 - $performance_report['summary']['memory_usage_percentage'] ?>
                ],
                backgroundColor: ['#dc3545', '#28a745'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Gráfico de tiempo de respuesta (placeholder con datos simulados)
    const responseCtx = document.getElementById('responseTimeChart').getContext('2d');
    responseTimeChart = new Chart(responseCtx, {
        type: 'line',
        data: {
            labels: ['5m', '4m', '3m', '2m', '1m', 'Ahora'],
            datasets: [{
                label: 'Tiempo de Respuesta (s)',
                data: [0.8, 1.2, 0.9, 1.1, 0.7, <?= $performance_report['summary']['request_duration'] ?>],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + 's';
                        }
                    }
                }
            }
        }
    });
}

function refreshMetrics() {
    fetch('/optimization/metrics', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDashboard(data);
            showAlert('success', 'Métricas actualizadas');
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Error actualizando métricas');
    });
}

function clearCache(type) {
    const confirmMessage = type === 'all' ? 
        '¿Limpiar todo el cache?' : 
        '¿Limpiar cache expirado?';
    
    if (confirm(confirmMessage)) {
        const formData = new FormData();
        formData.append('type', type);
        
        fetch('/optimization/clear-cache', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            showAlert(data.success ? 'success' : 'error', data.message);
            if (data.success) {
                setTimeout(() => location.reload(), 1000);
            }
        })
        .catch(error => {
            showAlert('error', 'Error limpiando cache');
        });
    }
}

function optimizeDatabase(action) {
    if (confirm('¿Ejecutar optimización de base de datos?')) {
        const formData = new FormData();
        formData.append('action', action);
        
        fetch('/optimization/optimize-database', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            showAlert(data.success ? 'success' : 'error', data.message);
        })
        .catch(error => {
            showAlert('error', 'Error optimizando base de datos');
        });
    }
}

function updateDashboard(data) {
    // Actualizar métricas en tiempo real
    const metrics = data.performance_metrics.request_metrics;
    
    // Actualizar gráfico de memoria
    if (memoryChart) {
        const memoryUsage = metrics.memory_usage_percentage;
        memoryChart.data.datasets[0].data = [memoryUsage, 100 - memoryUsage];
        memoryChart.update();
    }
    
    // Actualizar gráfico de tiempo de respuesta
    if (responseTimeChart) {
        responseTimeChart.data.datasets[0].data.shift();
        responseTimeChart.data.datasets[0].data.push(metrics.request_duration);
        responseTimeChart.update();
    }
}

function startRealTimeUpdates() {
    // Actualizar métricas cada 30 segundos
    setInterval(refreshMetrics, 30000);
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            bootstrap.Alert.getOrCreateInstance(alert).close();
        }
    }, 5000);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
