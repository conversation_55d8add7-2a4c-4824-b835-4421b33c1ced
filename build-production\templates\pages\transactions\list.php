<?php
$content = ob_start();
?>

<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Gestión de Transacciones</h1>
        <p class="text-muted mb-0">Administra todos tus ingresos y gastos</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
            <i class="fas fa-filter"></i>
            <span class="d-none d-md-inline ms-1">Filtros</span>
        </button>
        <button class="btn btn-outline-info" onclick="exportTransactions()">
            <i class="fas fa-download"></i>
            <span class="d-none d-md-inline ms-1">Exportar</span>
        </button>
        <a href="/transactions/create" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            <span class="d-none d-md-inline ms-1">Nueva Transacción</span>
        </a>
    </div>
</div>

<!-- Summary Cards -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card card-gradient-success">
            <div class="card-body text-center">
                <i class="fas fa-arrow-up fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-1">Ingresos</h5>
                <h3 class="text-white mb-0">$<?= number_format($summary['total_income'] ?? 0, 2) ?></h3>
                <small class="text-white-50"><?= $summary['income_count'] ?? 0 ?> transacciones</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card card-gradient-danger">
            <div class="card-body text-center">
                <i class="fas fa-arrow-down fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-1">Gastos</h5>
                <h3 class="text-white mb-0">$<?= number_format($summary['total_expenses'] ?? 0, 2) ?></h3>
                <small class="text-white-50"><?= $summary['expenses_count'] ?? 0 ?> transacciones</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card card-gradient-info">
            <div class="card-body text-center">
                <i class="fas fa-balance-scale fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-1">Balance</h5>
                <h3 class="text-white mb-0">$<?= number_format(($summary['total_income'] ?? 0) - ($summary['total_expenses'] ?? 0), 2) ?></h3>
                <small class="text-white-50">Período actual</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card card-gradient-warning">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-1">Promedio</h5>
                <h3 class="text-white mb-0">$<?= number_format($summary['average_transaction'] ?? 0, 2) ?></h3>
                <small class="text-white-50">Por transacción</small>
            </div>
        </div>
    </div>
</div>

<!-- Filters Collapse -->
<div class="collapse mb-4" id="filtersCollapse">
    <div class="card">
        <div class="card-body">
            <form id="filtersForm" class="row g-3" method="GET">
                <div class="col-md-3">
                    <label for="filterType" class="form-label">Tipo</label>
                    <select class="form-select" id="filterType" name="type">
                        <option value="">Todos</option>
                        <option value="income" <?= ($filters['type'] ?? '') === 'income' ? 'selected' : '' ?>>Ingresos</option>
                        <option value="expense" <?= ($filters['type'] ?? '') === 'expense' ? 'selected' : '' ?>>Gastos</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filterAccount" class="form-label">Cuenta</label>
                    <select class="form-select" id="filterAccount" name="account_id">
                        <option value="">Todas las cuentas</option>
                        <?php if (!empty($accounts)): ?>
                            <?php foreach ($accounts as $account): ?>
                                <option value="<?= $account['id'] ?>" <?= ($filters['account_id'] ?? '') == $account['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($account['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filterCategory" class="form-label">Categoría</label>
                    <select class="form-select" id="filterCategory" name="category_id">
                        <option value="">Todas las categorías</option>
                        <?php if (!empty($categories)): ?>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>" <?= ($filters['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filterDateRange" class="form-label">Período</label>
                    <select class="form-select" id="filterDateRange" name="date_range">
                        <option value="">Seleccionar período</option>
                        <option value="today" <?= ($filters['date_range'] ?? '') === 'today' ? 'selected' : '' ?>>Hoy</option>
                        <option value="week" <?= ($filters['date_range'] ?? '') === 'week' ? 'selected' : '' ?>>Esta semana</option>
                        <option value="month" <?= ($filters['date_range'] ?? '') === 'month' ? 'selected' : '' ?>>Este mes</option>
                        <option value="quarter" <?= ($filters['date_range'] ?? '') === 'quarter' ? 'selected' : '' ?>>Este trimestre</option>
                        <option value="year" <?= ($filters['date_range'] ?? '') === 'year' ? 'selected' : '' ?>>Este año</option>
                        <option value="custom" <?= ($filters['date_range'] ?? '') === 'custom' ? 'selected' : '' ?>>Personalizado</option>
                    </select>
                </div>
                <div class="col-md-4" id="customDateRange" style="display: none;">
                    <label for="filterStartDate" class="form-label">Fecha inicio</label>
                    <input type="date" class="form-control" id="filterStartDate" name="start_date" value="<?= $filters['start_date'] ?? '' ?>">
                </div>
                <div class="col-md-4" id="customDateRangeEnd" style="display: none;">
                    <label for="filterEndDate" class="form-label">Fecha fin</label>
                    <input type="date" class="form-control" id="filterEndDate" name="end_date" value="<?= $filters['end_date'] ?? '' ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <div class="btn-group w-100">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Filtrar
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Limpiar
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list text-primary me-2"></i>
                Lista de Transacciones
            </h5>
            <div class="d-flex align-items-center gap-2">
                <span class="text-muted">
                    Mostrando <?= count($transactions ?? []) ?> de <?= $total_transactions ?? 0 ?> transacciones
                </span>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary active" onclick="changeView('table')" id="tableViewBtn">
                        <i class="fas fa-table"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="changeView('cards')" id="cardsViewBtn">
                        <i class="fas fa-th-large"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Table View -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>Fecha</th>
                            <th>Descripción</th>
                            <th>Categoría</th>
                            <th>Cuenta</th>
                            <th>Tipo</th>
                            <th class="text-end">Monto</th>
                            <th class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($transactions)): ?>
                            <?php foreach ($transactions as $transaction): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input transaction-checkbox" value="<?= $transaction['id'] ?>">
                                    </td>
                                    <td>
                                        <span class="fw-semibold"><?= date('d/m/Y', strtotime($transaction['transaction_date'])) ?></span>
                                        <br>
                                        <small class="text-muted"><?= date('H:i', strtotime($transaction['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="rounded-circle p-2 me-2" style="background-color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?>20;">
                                                <i class="<?= htmlspecialchars($transaction['category_icon'] ?? 'fas fa-circle') ?> fa-sm" 
                                                   style="color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?>;"></i>
                                            </div>
                                            <div>
                                                <span class="fw-semibold"><?= htmlspecialchars($transaction['description']) ?></span>
                                                <?php if (!empty($transaction['subcategory_name'])): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($transaction['subcategory_name']) ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge" style="background-color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?>;">
                                            <?= htmlspecialchars($transaction['category_name']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted"><?= htmlspecialchars($transaction['account_name']) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge <?= $transaction['type'] === 'income' ? 'bg-success' : 'bg-danger' ?>">
                                            <i class="fas fa-arrow-<?= $transaction['type'] === 'income' ? 'up' : 'down' ?> me-1"></i>
                                            <?= $transaction['type'] === 'income' ? 'Ingreso' : 'Gasto' ?>
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold <?= $transaction['type'] === 'income' ? 'amount-positive' : 'amount-negative' ?>">
                                            <?= $transaction['type'] === 'income' ? '+' : '-' ?>$<?= number_format($transaction['amount'], 2) ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm">
                                            <a href="/transactions/<?= $transaction['id'] ?>/edit" class="btn btn-outline-primary" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-danger" onclick="deleteTransaction(<?= $transaction['id'] ?>)" title="Eliminar">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">No se encontraron transacciones</h6>
                                    <p class="text-muted mb-3">No hay transacciones que coincidan con los filtros aplicados.</p>
                                    <a href="/transactions/create" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>Crear Primera Transacción
                                    </a>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Cards View -->
        <div id="cardsView" style="display: none;">
            <div class="p-3">
                <div class="row g-3">
                    <?php if (!empty($transactions)): ?>
                        <?php foreach ($transactions as $transaction): ?>
                            <div class="col-lg-4 col-md-6">
                                <div class="card h-100 border-start border-4" style="border-color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?> !important;">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle p-2 me-2" style="background-color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?>20;">
                                                    <i class="<?= htmlspecialchars($transaction['category_icon'] ?? 'fas fa-circle') ?>" 
                                                       style="color: <?= htmlspecialchars($transaction['category_color'] ?? '#007bff') ?>;"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0"><?= htmlspecialchars($transaction['description']) ?></h6>
                                                    <small class="text-muted"><?= htmlspecialchars($transaction['category_name']) ?></small>
                                                </div>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="/transactions/<?= $transaction['id'] ?>/edit">
                                                        <i class="fas fa-edit me-2"></i>Editar
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteTransaction(<?= $transaction['id'] ?>)">
                                                        <i class="fas fa-trash me-2"></i>Eliminar
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="badge <?= $transaction['type'] === 'income' ? 'bg-success' : 'bg-danger' ?>">
                                                <i class="fas fa-arrow-<?= $transaction['type'] === 'income' ? 'up' : 'down' ?> me-1"></i>
                                                <?= $transaction['type'] === 'income' ? 'Ingreso' : 'Gasto' ?>
                                            </span>
                                            <span class="fw-bold <?= $transaction['type'] === 'income' ? 'amount-positive' : 'amount-negative' ?>">
                                                <?= $transaction['type'] === 'income' ? '+' : '-' ?>$<?= number_format($transaction['amount'], 2) ?>
                                            </span>
                                        </div>
                                        <div class="d-flex justify-content-between text-muted small">
                                            <span><?= htmlspecialchars($transaction['account_name']) ?></span>
                                            <span><?= date('d/m/Y', strtotime($transaction['transaction_date'])) ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No se encontraron transacciones</h6>
                                <p class="text-muted mb-3">No hay transacciones que coincidan con los filtros aplicados.</p>
                                <a href="/transactions/create" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Crear Primera Transacción
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
<?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
<nav aria-label="Paginación de transacciones" class="mt-4">
    <ul class="pagination justify-content-center">
        <li class="page-item <?= $pagination['current_page'] <= 1 ? 'disabled' : '' ?>">
            <a class="page-link" href="?page=<?= $pagination['current_page'] - 1 ?>&<?= http_build_query($filters ?? []) ?>">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        
        <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
            <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                <a class="page-link" href="?page=<?= $i ?>&<?= http_build_query($filters ?? []) ?>"><?= $i ?></a>
            </li>
        <?php endfor; ?>
        
        <li class="page-item <?= $pagination['current_page'] >= $pagination['total_pages'] ? 'disabled' : '' ?>">
            <a class="page-link" href="?page=<?= $pagination['current_page'] + 1 ?>&<?= http_build_query($filters ?? []) ?>">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    </ul>
</nav>
<?php endif; ?>

<!-- Bulk Actions (when transactions are selected) -->
<div id="bulkActions" class="position-fixed bottom-0 start-50 translate-middle-x bg-primary text-white p-3 rounded-top shadow" style="display: none; z-index: 1000;">
    <div class="d-flex align-items-center gap-3">
        <span id="selectedCount">0 seleccionadas</span>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-light btn-sm" onclick="bulkDelete()">
                <i class="fas fa-trash me-1"></i>Eliminar
            </button>
            <button class="btn btn-light btn-sm" onclick="bulkExport()">
                <i class="fas fa-download me-1"></i>Exportar
            </button>
            <button class="btn btn-outline-light btn-sm" onclick="clearSelection()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeTransactionsPage();
});

function initializeTransactionsPage() {
    // Initialize filters
    initializeFilters();
    
    // Initialize selection
    initializeSelection();
    
    // Set initial view
    changeView('table');
}

function initializeFilters() {
    const dateRangeSelect = document.getElementById('filterDateRange');
    const customDateRange = document.getElementById('customDateRange');
    const customDateRangeEnd = document.getElementById('customDateRangeEnd');
    
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateRange.style.display = 'block';
                customDateRangeEnd.style.display = 'block';
            } else {
                customDateRange.style.display = 'none';
                customDateRangeEnd.style.display = 'none';
            }
        });
        
        // Trigger change event if custom is already selected
        if (dateRangeSelect.value === 'custom') {
            dateRangeSelect.dispatchEvent(new Event('change'));
        }
    }
    
    // Form submission
    const filtersForm = document.getElementById('filtersForm');
    if (filtersForm) {
        filtersForm.addEventListener('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });
    }
}

function initializeSelection() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.transaction-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function updateBulkActions() {
        const selected = document.querySelectorAll('.transaction-checkbox:checked');
        const count = selected.length;
        
        if (count > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = `${count} seleccionada${count > 1 ? 's' : ''}`;
        } else {
            bulkActions.style.display = 'none';
        }
        
        if (selectAll) {
            selectAll.indeterminate = count > 0 && count < checkboxes.length;
            selectAll.checked = count === checkboxes.length;
        }
    }
}

function changeView(view) {
    const tableView = document.getElementById('tableView');
    const cardsView = document.getElementById('cardsView');
    const tableBtn = document.getElementById('tableViewBtn');
    const cardsBtn = document.getElementById('cardsViewBtn');
    
    if (view === 'table') {
        tableView.style.display = 'block';
        cardsView.style.display = 'none';
        tableBtn.classList.add('active');
        cardsBtn.classList.remove('active');
    } else {
        tableView.style.display = 'none';
        cardsView.style.display = 'block';
        tableBtn.classList.remove('active');
        cardsBtn.classList.add('active');
    }
    
    localStorage.setItem('transactionsView', view);
}

function applyFilters() {
    const form = document.getElementById('filtersForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    window.location.href = '?' + params.toString();
}

function clearFilters() {
    window.location.href = '/transactions';
}

function deleteTransaction(id) {
    showConfirmation(
        '¿Está seguro de que desea eliminar esta transacción?',
        async function() {
            try {
                const response = await fetch(`/api/transactions/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showToast('Transacción eliminada exitosamente', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast(data.message || 'Error al eliminar la transacción', 'error');
                }
            } catch (error) {
                showToast('Error al eliminar la transacción', 'error');
            }
        },
        'Eliminar Transacción'
    );
}

function bulkDelete() {
    const selected = document.querySelectorAll('.transaction-checkbox:checked');
    const ids = Array.from(selected).map(cb => cb.value);
    
    showConfirmation(
        `¿Está seguro de que desea eliminar ${ids.length} transacciones?`,
        async function() {
            try {
                const response = await fetch('/api/transactions/bulk-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ ids })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showToast(`${ids.length} transacciones eliminadas exitosamente`, 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast(data.message || 'Error al eliminar las transacciones', 'error');
                }
            } catch (error) {
                showToast('Error al eliminar las transacciones', 'error');
            }
        },
        'Eliminar Transacciones'
    );
}

function bulkExport() {
    const selected = document.querySelectorAll('.transaction-checkbox:checked');
    const ids = Array.from(selected).map(cb => cb.value);
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/transactions/export';
    
    const idsInput = document.createElement('input');
    idsInput.type = 'hidden';
    idsInput.name = 'ids';
    idsInput.value = JSON.stringify(ids);
    
    form.appendChild(idsInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

function clearSelection() {
    document.querySelectorAll('.transaction-checkbox').forEach(cb => cb.checked = false);
    const selectAll = document.getElementById('selectAll');
    if (selectAll) selectAll.checked = false;
    document.getElementById('bulkActions').style.display = 'none';
}

function exportTransactions() {
    const form = document.getElementById('filtersForm');
    const formData = new FormData(form);
    
    const exportForm = document.createElement('form');
    exportForm.method = 'POST';
    exportForm.action = '/transactions/export';
    
    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        exportForm.appendChild(input);
    }
    
    document.body.appendChild(exportForm);
    exportForm.submit();
    document.body.removeChild(exportForm);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
