<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Repositories\UserRepository;
use ControlGastos\Models\User;
use ControlGastos\Core\Session;
use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de autenticación
 * Maneja login, registro, verificación y seguridad
 */
class AuthService
{
    private UserRepository $userRepository;
    private Session $session;
    private Logger $logger;
    private array $loginAttempts = [];

    public function __construct(
        UserRepository $userRepository,
        Session $session,
        Logger $logger
    ) {
        $this->userRepository = $userRepository;
        $this->session = $session;
        $this->logger = $logger;
    }

    /**
     * Registrar nuevo usuario
     */
    public function register(array $data): array
    {
        try {
            // Validar datos
            $this->validateRegistrationData($data);

            // Verificar si el email ya existe
            if ($this->userRepository->emailExists($data['email'])) {
                return [
                    'success' => false,
                    'message' => 'El email ya está registrado'
                ];
            }

            // Crear usuario
            $user = new User();
            $user->setEmail($data['email']);
            $user->setPassword($data['password']);
            $user->setFirstName($data['first_name']);
            $user->setLastName($data['last_name']);
            $user->setPhone($data['phone'] ?? null);
            
            // Generar token de verificación
            $user->generateEmailVerificationToken();

            // Guardar usuario
            $user = $this->userRepository->create($user);

            // Log del registro
            $this->logger->info('Usuario registrado', [
                'user_id' => $user->getId(),
                'email' => $user->getEmail()
            ]);

            return [
                'success' => true,
                'message' => 'Usuario registrado exitosamente',
                'user' => $user->toPublicArray(),
                'verification_token' => $user->getEmailVerificationToken()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en registro: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Iniciar sesión
     */
    public function login(string $email, string $password, bool $rememberMe = false): array
    {
        try {
            // Verificar límite de intentos
            if ($this->isLoginBlocked($email)) {
                return [
                    'success' => false,
                    'message' => 'Demasiados intentos fallidos. Intente más tarde.'
                ];
            }

            // Buscar usuario
            $user = $this->userRepository->findByEmail($email);
            
            if (!$user) {
                $this->recordFailedLogin($email);
                return [
                    'success' => false,
                    'message' => 'Credenciales inválidas'
                ];
            }

            // Verificar contraseña
            if (!$user->verifyPassword($password)) {
                $this->recordFailedLogin($email);
                return [
                    'success' => false,
                    'message' => 'Credenciales inválidas'
                ];
            }

            // Verificar estado del usuario
            if (!$user->isActive()) {
                return [
                    'success' => false,
                    'message' => 'Cuenta desactivada'
                ];
            }

            // Verificar 2FA si está habilitado
            if ($user->isTwoFactorEnabled()) {
                // Guardar datos temporales para 2FA
                $this->session->set('2fa_user_id', $user->getId());
                $this->session->set('2fa_remember_me', $rememberMe);
                
                return [
                    'success' => true,
                    'requires_2fa' => true,
                    'message' => 'Ingrese el código de autenticación'
                ];
            }

            // Login exitoso
            $this->createUserSession($user, $rememberMe);
            $this->clearFailedLogins($email);

            return [
                'success' => true,
                'message' => 'Login exitoso',
                'user' => $user->toPublicArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en login: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Verificar código 2FA
     */
    public function verify2FA(string $code): array
    {
        try {
            $userId = $this->session->get('2fa_user_id');
            $rememberMe = $this->session->get('2fa_remember_me', false);

            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'Sesión 2FA expirada'
                ];
            }

            $user = $this->userRepository->findById($userId);
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'Usuario no encontrado'
                ];
            }

            // Verificar código 2FA
            if (!$this->verify2FACode($user, $code)) {
                return [
                    'success' => false,
                    'message' => 'Código inválido'
                ];
            }

            // Login exitoso
            $this->createUserSession($user, $rememberMe);
            
            // Limpiar datos temporales de 2FA
            $this->session->remove('2fa_user_id');
            $this->session->remove('2fa_remember_me');

            return [
                'success' => true,
                'message' => 'Autenticación exitosa',
                'user' => $user->toPublicArray()
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en 2FA: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Cerrar sesión
     */
    public function logout(): void
    {
        $userId = $this->session->get('user_id');
        
        if ($userId) {
            $this->logger->info('Usuario cerró sesión', ['user_id' => $userId]);
        }

        $this->session->destroy();
    }

    /**
     * Verificar email
     */
    public function verifyEmail(string $token): array
    {
        try {
            $user = $this->userRepository->findByEmailVerificationToken($token);
            
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'Token de verificación inválido'
                ];
            }

            $user->setEmailVerified(true);
            $user->setEmailVerificationToken(null);
            $this->userRepository->update($user);

            $this->logger->info('Email verificado', [
                'user_id' => $user->getId(),
                'email' => $user->getEmail()
            ]);

            return [
                'success' => true,
                'message' => 'Email verificado exitosamente'
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en verificación de email: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Solicitar reset de contraseña
     */
    public function requestPasswordReset(string $email): array
    {
        try {
            $user = $this->userRepository->findByEmail($email);
            
            if (!$user) {
                // Por seguridad, no revelar si el email existe
                return [
                    'success' => true,
                    'message' => 'Si el email existe, recibirá instrucciones para restablecer su contraseña'
                ];
            }

            $token = $user->generatePasswordResetToken();
            $this->userRepository->update($user);

            $this->logger->info('Reset de contraseña solicitado', [
                'user_id' => $user->getId(),
                'email' => $user->getEmail()
            ]);

            return [
                'success' => true,
                'message' => 'Si el email existe, recibirá instrucciones para restablecer su contraseña',
                'reset_token' => $token
            ];

        } catch (Exception $e) {
            $this->logger->error('Error en reset de contraseña: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Restablecer contraseña
     */
    public function resetPassword(string $token, string $newPassword): array
    {
        try {
            $user = $this->userRepository->findByPasswordResetToken($token);
            
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'Token de reset inválido o expirado'
                ];
            }

            $user->setPassword($newPassword);
            $user->clearPasswordResetToken();
            $this->userRepository->update($user);

            $this->logger->info('Contraseña restablecida', [
                'user_id' => $user->getId(),
                'email' => $user->getEmail()
            ]);

            return [
                'success' => true,
                'message' => 'Contraseña restablecida exitosamente'
            ];

        } catch (Exception $e) {
            $this->logger->error('Error al restablecer contraseña: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Validar datos de registro
     */
    private function validateRegistrationData(array $data): void
    {
        $required = ['email', 'password', 'first_name', 'last_name'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception("El campo {$field} es requerido");
            }
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Email inválido');
        }

        if (strlen($data['password']) < 8) {
            throw new Exception('La contraseña debe tener al menos 8 caracteres');
        }
    }

    /**
     * Crear sesión de usuario
     */
    private function createUserSession(User $user, bool $rememberMe): void
    {
        $this->session->regenerateId();
        
        $this->session->set('user_id', $user->getId());
        $this->session->set('user_authenticated', true);
        $this->session->set('user_email', $user->getEmail());
        $this->session->set('user_name', $user->getFullName());
        $this->session->set('user_ip', $_SERVER['REMOTE_ADDR'] ?? '');
        $this->session->set('last_activity', time());

        if ($rememberMe) {
            // Extender tiempo de sesión para "recordarme"
            ini_set('session.gc_maxlifetime', '2592000'); // 30 días
        }

        $this->logger->info('Sesión creada', [
            'user_id' => $user->getId(),
            'email' => $user->getEmail(),
            'remember_me' => $rememberMe
        ]);
    }

    /**
     * Verificar si el login está bloqueado
     */
    private function isLoginBlocked(string $email): bool
    {
        $key = 'login_attempts_' . $email;
        $attempts = $this->session->get($key, []);
        
        // Limpiar intentos antiguos (más de 15 minutos)
        $attempts = array_filter($attempts, fn($time) => time() - $time < 900);
        
        return count($attempts) >= 5;
    }

    /**
     * Registrar intento fallido de login
     */
    private function recordFailedLogin(string $email): void
    {
        $key = 'login_attempts_' . $email;
        $attempts = $this->session->get($key, []);
        $attempts[] = time();
        $this->session->set($key, $attempts);

        $this->logger->warning('Intento de login fallido', [
            'email' => $email,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
    }

    /**
     * Limpiar intentos fallidos
     */
    private function clearFailedLogins(string $email): void
    {
        $key = 'login_attempts_' . $email;
        $this->session->remove($key);
    }

    /**
     * Verificar código 2FA
     */
    private function verify2FACode(User $user, string $code): bool
    {
        // Implementar verificación TOTP
        // Requiere librería como RobThree/TwoFactorAuth
        if (!$user->getTwoFactorSecret()) {
            return false;
        }

        // Por ahora, aceptar códigos de 6 dígitos
        return preg_match('/^\d{6}$/', $code);
    }
}
