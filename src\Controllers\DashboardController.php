<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Core\Database;
use ControlGastos\Core\Logger;
use ControlGastos\Services\TransactionService;
use ControlGastos\Services\AccountService;
use ControlGastos\Services\ReminderService;
use ControlGastos\Services\ReportService;
use ControlGastos\Services\AdvancedReportService;
use ControlGastos\Services\EmailService;
use ControlGastos\Core\Session;

/**
 * Controlador del Dashboard
 * Maneja la página principal con resumen financiero
 */
class DashboardController
{
    private Database $database;
    private Logger $logger;
    private TransactionService $transactionService;
    private AccountService $accountService;
    private ReminderService $reminderService;
    private ReportService $reportService;
    private AdvancedReportService $advancedReportService;
    private EmailService $emailService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->database = $container->get('database');
        $this->logger = $container->get('logger');
        $this->transactionService = $container->get('transactionService');
        $this->accountService = $container->get('accountService');
        $this->reminderService = $container->get('reminderService');
        $this->reportService = $container->get('reportService');
        $this->advancedReportService = $container->get('advancedReportService');
        $this->emailService = $container->get('emailService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Mostrar dashboard principal
     */
    public function index(): string
    {
        try {
            // Usar el servicio de reportes avanzados para obtener todos los datos
            $dashboardData = $this->advancedReportService->getDashboardData($this->userId);

            // Obtener cuentas del usuario
            $accounts = $this->accountService->getUserAccounts($this->userId);

            // Obtener transacciones recientes
            $recent_transactions = $this->getRecentTransactions($this->userId, 10);

            // Obtener recordatorios próximos
            $upcoming_reminders = $this->getUpcomingReminders($this->userId, 5);

            $data = [
                'title' => 'Dashboard Financiero',
                'breadcrumb' => [
                    ['title' => 'Dashboard']
                ],
                'financial_summary' => $dashboardData['financial_summary'] ?? [],
                'comparison' => $dashboardData['comparison'] ?? [],
                'trends' => $dashboardData['trends'] ?? [],
                'category_analysis' => $dashboardData['category_analysis'] ?? [],
                'spending_patterns' => $dashboardData['spending_patterns'] ?? [],
                'financial_health' => $dashboardData['financial_health'] ?? [],
                'predictions' => $dashboardData['predictions'] ?? [],
                'goals_progress' => $dashboardData['goals_progress'] ?? [],
                'alerts' => $dashboardData['alerts'] ?? [],
                'accounts' => $accounts,
                'recent_transactions' => $recent_transactions,
                'upcoming_reminders' => $upcoming_reminders,
                'success' => $this->session->getFlash('success'),
                'error' => $this->session->getFlash('error')
            ];

            return $this->render('dashboard/index', $data);

        } catch (\Exception $e) {
            $this->session->flash('error', 'Error cargando el dashboard: ' . $e->getMessage());

            $data = [
                'title' => 'Dashboard Financiero',
                'breadcrumb' => [
                    ['title' => 'Dashboard']
                ],
                'financial_summary' => [
                    'total_balance' => 0,
                    'monthly_income' => 0,
                    'monthly_expenses' => 0,
                    'savings_rate' => 0
                ],
                'comparison' => [],
                'trends' => [],
                'category_analysis' => [],
                'spending_patterns' => [],
                'financial_health' => [],
                'predictions' => [],
                'goals_progress' => [],
                'alerts' => [],
                'accounts' => [],
                'recent_transactions' => [],
                'upcoming_reminders' => [],
                'error' => $this->session->getFlash('error')
            ];

            return $this->render('dashboard/index', $data);
        }
    }

    /**
     * Obtener resumen financiero integrado global
     */
    private function getFinancialSummary(): array
    {
        try {
            // 1. BALANCE TOTAL CONSOLIDADO
            $total_balance = $this->getConsolidatedBalance();

            // 2. ESTADÍSTICAS MENSUALES INTEGRADAS
            $monthlyStats = $this->getIntegratedMonthlyStats();

            // 3. UTILIZACIÓN DE TARJETAS DE CRÉDITO
            $creditUtilization = $this->getCreditCardUtilization();

            // 4. DISTRIBUCIÓN DE PATRIMONIO
            $assetDistribution = $this->getAssetDistribution();

            // Calcular tasa de ahorro
            $savings_rate = $monthlyStats['monthly_income'] > 0 ?
                (($monthlyStats['monthly_income'] - $monthlyStats['monthly_expenses']) / $monthlyStats['monthly_income']) * 100 : 0;

            return [
                'total_balance' => $total_balance,
                'monthly_income' => $monthlyStats['monthly_income'],
                'monthly_expenses' => $monthlyStats['monthly_expenses'],
                'savings_rate' => max(0, $savings_rate),
                'net_income' => $monthlyStats['monthly_income'] - $monthlyStats['monthly_expenses'],
                'credit_utilization' => $creditUtilization,
                'asset_distribution' => $assetDistribution,
                'transaction_count' => $monthlyStats['transaction_count'],
                'avg_transaction' => $monthlyStats['transaction_count'] > 0 ?
                    $monthlyStats['monthly_expenses'] / $monthlyStats['transaction_count'] : 0
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo resumen financiero: ' . $e->getMessage());
            return [
                'total_balance' => 0,
                'monthly_income' => 0,
                'monthly_expenses' => 0,
                'savings_rate' => 0,
                'net_income' => 0,
                'credit_utilization' => 0,
                'asset_distribution' => [],
                'transaction_count' => 0,
                'avg_transaction' => 0
            ];
        }
    }

    /**
     * Obtener balance consolidado de todas las fuentes
     */
    private function getConsolidatedBalance(): float
    {
        $total = 0;

        // Saldos de cuentas bancarias
        $query = "SELECT SUM(current_balance) as total FROM bank_accounts WHERE user_id = ? AND is_active = 1";
        $result = $this->database->select($query, [$this->userId]);
        $total += (float)($result[0]['total'] ?? 0);

        // Cupos disponibles de tarjetas de crédito (como activo disponible)
        $query = "SELECT SUM(credit_limit - used_credit) as available FROM credit_cards WHERE user_id = ? AND is_active = 1";
        $result = $this->database->select($query, [$this->userId]);
        $total += (float)($result[0]['available'] ?? 0);

        return $total;
    }

    /**
     * Obtener estadísticas mensuales integradas
     */
    private function getIntegratedMonthlyStats(): array
    {
        $monthly_income = 0;
        $monthly_expenses = 0;
        $transaction_count = 0;

        // 1. Transacciones directas
        $query = "SELECT
                    SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as income,
                    SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expenses,
                    COUNT(*) as count
                  FROM transactions
                  WHERE user_id = ?
                  AND MONTH(transaction_date) = MONTH(CURDATE())
                  AND YEAR(transaction_date) = YEAR(CURDATE())";

        $result = $this->database->select($query, [$this->userId]);
        if (!empty($result)) {
            $monthly_income += (float)($result[0]['income'] ?? 0);
            $monthly_expenses += (float)($result[0]['expenses'] ?? 0);
            $transaction_count += (int)($result[0]['count'] ?? 0);
        }

        // 2. Transacciones de tarjetas de crédito
        $query = "SELECT
                    SUM(CASE WHEN transaction_type IN ('refund', 'cashback', 'payment_reversal') THEN amount ELSE 0 END) as income,
                    SUM(CASE WHEN transaction_type IN ('purchase', 'fee') THEN amount ELSE 0 END) as expenses,
                    COUNT(*) as count
                  FROM credit_card_transactions
                  WHERE user_id = ?
                  AND MONTH(transaction_date) = MONTH(CURDATE())
                  AND YEAR(transaction_date) = YEAR(CURDATE())";

        $result = $this->database->select($query, [$this->userId]);
        if (!empty($result)) {
            $monthly_income += (float)($result[0]['income'] ?? 0);
            $monthly_expenses += (float)($result[0]['expenses'] ?? 0);
            $transaction_count += (int)($result[0]['count'] ?? 0);
        }

        // 3. Movimientos bancarios (excluyendo automáticos)
        $query = "SELECT
                    SUM(CASE WHEN movement_type IN ('deposit', 'transfer_in', 'interest') THEN amount ELSE 0 END) as income,
                    SUM(CASE WHEN movement_type IN ('withdrawal', 'transfer_out', 'fee') THEN amount ELSE 0 END) as expenses,
                    COUNT(*) as count
                  FROM bank_account_movements
                  WHERE user_id = ?
                  AND MONTH(movement_date) = MONTH(CURDATE())
                  AND YEAR(movement_date) = YEAR(CURDATE())
                  AND description NOT LIKE '%registrado%'";

        $result = $this->database->select($query, [$this->userId]);
        if (!empty($result)) {
            $monthly_income += (float)($result[0]['income'] ?? 0);
            $monthly_expenses += (float)($result[0]['expenses'] ?? 0);
            $transaction_count += (int)($result[0]['count'] ?? 0);
        }

        return [
            'monthly_income' => $monthly_income,
            'monthly_expenses' => $monthly_expenses,
            'transaction_count' => $transaction_count
        ];
    }

    /**
     * Obtener utilización de tarjetas de crédito
     */
    private function getCreditCardUtilization(): float
    {
        $query = "SELECT
                    SUM(credit_limit) as total_limit,
                    SUM(used_credit) as total_used
                  FROM credit_cards
                  WHERE user_id = ? AND is_active = 1";

        $result = $this->database->select($query, [$this->userId]);

        if (!empty($result) && $result[0]['total_limit'] > 0) {
            return ((float)$result[0]['total_used'] / (float)$result[0]['total_limit']) * 100;
        }

        return 0;
    }

    /**
     * Obtener distribución de activos
     */
    private function getAssetDistribution(): array
    {
        $distribution = [];

        // Cuentas bancarias
        $query = "SELECT SUM(current_balance) as total FROM bank_accounts WHERE user_id = ? AND is_active = 1";
        $result = $this->database->select($query, [$this->userId]);
        $bankBalance = (float)($result[0]['total'] ?? 0);

        // Cupos disponibles de tarjetas
        $query = "SELECT SUM(credit_limit - used_credit) as available FROM credit_cards WHERE user_id = ? AND is_active = 1";
        $result = $this->database->select($query, [$this->userId]);
        $creditAvailable = (float)($result[0]['available'] ?? 0);

        $total = $bankBalance + $creditAvailable;

        if ($total > 0) {
            $distribution = [
                'bank_accounts' => [
                    'amount' => $bankBalance,
                    'percentage' => ($bankBalance / $total) * 100,
                    'label' => 'Cuentas Bancarias'
                ],
                'credit_available' => [
                    'amount' => $creditAvailable,
                    'percentage' => ($creditAvailable / $total) * 100,
                    'label' => 'Crédito Disponible'
                ]
            ];
        }

        return $distribution;
    }

    /**
     * Obtener gastos por categoría
     */
    private function getCategoryExpenses(int $userId, string $startDate, string $endDate): array
    {
        $query = "SELECT
                    c.name as category_name,
                    c.color as category_color,
                    SUM(t.amount) as total_amount
                  FROM transactions t
                  LEFT JOIN categories c ON t.category_id = c.id
                  WHERE t.user_id = ?
                  AND t.type = 'expense'
                  AND t.transaction_date BETWEEN ? AND ?
                  GROUP BY c.id, c.name, c.color
                  ORDER BY total_amount DESC";

        return $this->database->select($query, [$userId, $startDate, $endDate]);
    }

    /**
     * Obtener datos para gráficos (AJAX)
     */
    public function getChartData(): void
    {
        try {
            $type = $_GET['type'] ?? 'income_expenses';
            $period = $_GET['period'] ?? '6months';
            
            switch ($type) {
                case 'income_expenses':
                    $data = $this->getIncomeExpensesChartData($period);
                    break;
                case 'category_distribution':
                    $data = $this->getCategoryDistributionData($period);
                    break;
                default:
                    throw new \Exception('Tipo de gráfico no válido');
            }

            $this->jsonResponse([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Obtener datos para gráfico de ingresos vs gastos
     */
    private function getIncomeExpensesChartData(string $period): array
    {
        $months = $period === '1year' ? 12 : 6;
        $data = [
            'labels' => [],
            'income' => [],
            'expenses' => []
        ];

        for ($i = $months - 1; $i >= 0; $i--) {
            $date = date('Y-m-01', strtotime("-{$i} months"));
            $startDate = $date;
            $endDate = date('Y-m-t', strtotime($date));
            
            $data['labels'][] = date('M Y', strtotime($date));
            
            $transactions = $this->getTransactionsByDateRange(
                $this->userId,
                $startDate,
                $endDate
            );
            
            $income = 0;
            $expenses = 0;
            
            foreach ($transactions as $transaction) {
                if ($transaction['type'] === 'income') {
                    $income += $transaction['amount'];
                } else {
                    $expenses += $transaction['amount'];
                }
            }
            
            $data['income'][] = $income;
            $data['expenses'][] = $expenses;
        }

        return $data;
    }

    /**
     * Obtener datos para gráfico de distribución por categorías
     */
    private function getCategoryDistributionData(string $period): array
    {
        $months = $period === '1year' ? 12 : 6;
        $startDate = date('Y-m-01', strtotime("-{$months} months"));
        $endDate = date('Y-m-t');
        
        $categoryExpenses = $this->getCategoryExpenses(
            $this->userId,
            $startDate,
            $endDate
        );
        
        $data = [
            'labels' => [],
            'values' => [],
            'colors' => []
        ];
        
        foreach ($categoryExpenses as $category) {
            $data['labels'][] = $category['category_name'];
            $data['values'][] = $category['total_amount'];
            $data['colors'][] = $category['category_color'] ?? '#007bff';
        }
        
        return $data;
    }

    /**
     * Actualizar dashboard (AJAX)
     */
    public function refresh(): void
    {
        try {
            $financial_summary = $this->getFinancialSummary();
            $accounts = $this->accountService->getUserAccounts($this->userId);
            $recent_transactions = $this->getRecentTransactions($this->userId, 5);
            
            $this->jsonResponse([
                'success' => true,
                'data' => [
                    'financial_summary' => $financial_summary,
                    'accounts' => $accounts,
                    'recent_transactions' => $recent_transactions,
                    'timestamp' => time()
                ]
            ]);

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error actualizando dashboard: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Obtener transacciones recientes
     */
    private function getRecentTransactions(int $userId, int $limit = 10): array
    {
        try {
            $query = "
                SELECT
                    t.*,
                    c.name as category_name,
                    c.color as category_color,
                    c.icon as category_icon,
                    a.name as account_name
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                LEFT JOIN accounts a ON t.account_id = a.id
                WHERE t.user_id = ?
                ORDER BY t.created_at DESC
                LIMIT ?
            ";

            return $this->database->select($query, [$userId, $limit]);

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo transacciones recientes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener recordatorios próximos
     */
    private function getUpcomingReminders(int $userId, int $limit = 5): array
    {
        try {
            $query = "
                SELECT *
                FROM reminders
                WHERE user_id = ?
                AND due_date >= CURDATE()
                AND is_completed = 0
                ORDER BY due_date ASC
                LIMIT ?
            ";

            return $this->database->select($query, [$userId, $limit]);

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo recordatorios próximos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener transacciones por rango de fechas
     */
    private function getTransactionsByDateRange(int $userId, string $startDate, string $endDate): array
    {
        try {
            $query = "
                SELECT *
                FROM transactions
                WHERE user_id = ?
                AND transaction_date BETWEEN ? AND ?
                ORDER BY transaction_date DESC
            ";

            return $this->database->select($query, [$userId, $startDate, $endDate]);

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo transacciones por rango: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Enviar respuesta JSON
     */
    private function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
