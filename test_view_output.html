
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0"><i class="fas fa-credit-card me-2"></i>Mis Tarjetas de Crédito</h1>
                    <p class="text-muted">Gestiona tus tarjetas de crédito y controla tus gastos</p>
                </div>
                <a href="/controlGastos/public/?route=credit-cards/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nueva Tarjeta
                </a>
            </div>
        </div>
    </div>

    <!-- Resumen General -->
        <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Tarjetas</h6>
                            <h3 class="mb-0">1</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-credit-card fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Cupo Total</h6>
                            <h3 class="mb-0">$0</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Saldo Total</h6>
                            <h3 class="mb-0">$930</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Disponible</h6>
                            <h3 class="mb-0">$49,070</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tarjetas de Crédito -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-credit-card me-2"></i>Mis Tarjetas
                        </h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                                <option value="">Todas las tarjetas</option>
                                <option value="active">Solo activas</option>
                                <option value="blocked">Bloqueadas</option>
                                <option value="cancelled">Canceladas</option>
                                <option value="expired">Vencidas</option>
                            </select>
                            <button class="btn btn-sm btn-outline-primary" onclick="checkExpiredCards()">
                                <i class="fas fa-sync-alt me-1"></i>Verificar Vencidas
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                                        <div class="row">
                                                                        <div class="col-md-6 col-lg-4 mb-4" data-card-status="active">
                            <div class="card h-100 shadow-sm ">
                                <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                    <div class="d-flex justify-content-between align-items-center text-white">
                                        <div>
                                            <h6 class="mb-0">
                                                Visa Principal                                                                                            </h6>
                                            <small>Banco Nacional</small>
                                        </div>
                                        <div>
                                                                                        <small>**** 1234</small>
                                                                                    </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Información financiera -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">Saldo Actual:</span>
                                            <strong class="text-danger">
                                                $930                                            </strong>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">Cupo Total:</span>
                                            <span>$50,000</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">Disponible:</span>
                                            <strong class="text-success">$49,070</strong>
                                        </div>
                                    </div>

                                    <!-- Barra de utilización -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <small class="text-muted">Utilización</small>
                                            <small class="text-muted">1.9%</small>
                                        </div>
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar bg-success" 
                                                 style="width: 1.9%"></div>
                                        </div>
                                    </div>

                                    <!-- Fechas importantes -->
                                    <div class="mb-3">
                                        <small class="text-muted d-block">Próximo corte: 
Warning: Undefined array key "next_cut_off_date" in C:\xampp\htdocs\controlGastos\templates\pages\credit_cards\index.php on line 187
01/01/1970</small>
                                        <small class="text-muted d-block">Fecha límite pago: 05/07/2025</small>
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex gap-2">
                                        <a href="/controlGastos/public/?route=credit-cards/show&id=2" class="btn btn-primary btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>Ver
                                        </a>
                                        <a href="/controlGastos/public/?route=credit-cards/edit&id=2" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="/controlGastos/public/?route=credit-cards/reports&id=2" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-chart-bar"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            </div>
                                    </div>
            </div>
        </div>
    </div>

    <!-- Actividad Reciente -->
    </div>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}
.card-filtered {
    display: none !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filtro por estado
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            const selectedStatus = this.value;
            const cards = document.querySelectorAll('[data-card-status]');

            cards.forEach(card => {
                if (selectedStatus === '' || card.dataset.cardStatus === selectedStatus) {
                    card.classList.remove('card-filtered');
                } else {
                    card.classList.add('card-filtered');
                }
            });
        });
    }
});

function checkExpiredCards() {
    if (confirm('¿Deseas verificar y actualizar automáticamente las tarjetas vencidas?')) {
        // Crear formulario para verificar tarjetas vencidas
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/controlGastos/public/?route=credit-cards/check-expired';

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
