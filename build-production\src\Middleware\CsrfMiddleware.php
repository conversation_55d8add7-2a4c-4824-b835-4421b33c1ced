<?php

declare(strict_types=1);

namespace ControlGastos\Middleware;

use ControlGastos\Core\Container;
use ControlGastos\Core\Session;

/**
 * Middleware de protección CSRF
 * Valida tokens CSRF en requests POST, PUT, DELETE
 */
class CsrfMiddleware
{
    private Session $session;

    public function __construct(Session $session)
    {
        $this->session = $session;
    }

    public function handle(Container $container): void
    {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        // Solo validar en métodos que modifican datos
        if (in_array($method, ['POST', 'PUT', 'DELETE', 'PATCH'])) {
            $this->validateCsrfToken();
        }
    }

    /**
     * Validar token CSRF
     */
    private function validateCsrfToken(): void
    {
        // Obtener token del request
        $token = $this->getTokenFromRequest();
        
        if (!$token) {
            $this->handleCsrfError('Token CSRF no encontrado');
        }

        // Validar token
        if (!$this->session->validateCsrfToken($token)) {
            $this->handleCsrfError('Token CSRF inválido');
        }
    }

    /**
     * Obtener token del request
     */
    private function getTokenFromRequest(): ?string
    {
        // Buscar en POST data
        if (isset($_POST['csrf_token'])) {
            return $_POST['csrf_token'];
        }

        // Buscar en headers
        if (isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
            return $_SERVER['HTTP_X_CSRF_TOKEN'];
        }

        // Buscar en JSON body
        $input = file_get_contents('php://input');
        if ($input) {
            $data = json_decode($input, true);
            if (isset($data['csrf_token'])) {
                return $data['csrf_token'];
            }
        }

        return null;
    }

    /**
     * Manejar error de CSRF
     */
    private function handleCsrfError(string $message): void
    {
        // Log del intento de CSRF
        error_log("CSRF Error: {$message} | IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));

        // Si es AJAX, devolver JSON
        if ($this->isAjaxRequest()) {
            http_response_code(419);
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => $message,
                'csrf_token' => $this->session->getCsrfToken()
            ]);
            exit;
        }

        // Redirigir con error
        $this->session->flash('error', $message);
        header('Location: ' . ($_SERVER['HTTP_REFERER'] ?? '/'));
        exit;
    }

    /**
     * Verificar si es una request AJAX
     */
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
