-- Migración 008: Crear tablas de backup y seguridad
-- Fecha: 2024-01-01
-- Descripción: Tablas para gestión de backups, auditoría y seguridad

-- Tabla de backups
CREATE TABLE IF NOT EXISTS backups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    backup_name VARCHAR(255) NOT NULL,
    backup_type <PERSON>NU<PERSON>('user', 'full') NOT NULL DEFAULT 'user',
    user_id INT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL DEFAULT 0,
    status ENUM('pending', 'completed', 'failed', 'deleted') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_backups_user_id (user_id),
    INDEX idx_backups_status (status),
    INDEX idx_backups_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de backups programados
CREATE TABLE IF NOT EXISTS scheduled_backups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    frequency ENUM('hourly', 'daily', 'weekly', 'monthly') NOT NULL DEFAULT 'daily',
    next_run TIMESTAMP NOT NULL,
    last_run TIMESTAMP NULL,
    last_backup_id INT NULL,
    status ENUM('active', 'paused', 'cancelled') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_scheduled_backups_user_id (user_id),
    INDEX idx_scheduled_backups_next_run (next_run),
    INDEX idx_scheduled_backups_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (last_backup_id) REFERENCES backups(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de intentos de login
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL DEFAULT FALSE,
    user_id INT NULL,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_login_attempts_email (email),
    INDEX idx_login_attempts_ip (ip_address),
    INDEX idx_login_attempts_attempted_at (attempted_at),
    INDEX idx_login_attempts_success (success),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de log de auditoría
CREATE TABLE IF NOT EXISTS audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id INT NULL,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_audit_log_user_id (user_id),
    INDEX idx_audit_log_action (action),
    INDEX idx_audit_log_resource (resource),
    INDEX idx_audit_log_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de configuración de seguridad
CREATE TABLE IF NOT EXISTS security_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_setting (user_id, setting_key),
    INDEX idx_security_settings_user_id (user_id),
    INDEX idx_security_settings_key (setting_key),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agregar campos de seguridad a la tabla users si no existen
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS last_ip VARCHAR(45) NULL,
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS two_factor_secret VARCHAR(32) NULL;

-- Crear índices adicionales en users para seguridad
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login);
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON users(is_admin);

-- Insertar configuraciones de seguridad por defecto
INSERT IGNORE INTO security_settings (user_id, setting_key, setting_value) VALUES
(NULL, 'max_login_attempts', '5'),
(NULL, 'lockout_duration', '900'),
(NULL, 'session_timeout', '3600'),
(NULL, 'password_min_length', '8'),
(NULL, 'require_2fa', 'false'),
(NULL, 'backup_retention_days', '30'),
(NULL, 'auto_backup_enabled', 'true'),
(NULL, 'auto_backup_frequency', 'daily');

-- Crear vista para estadísticas de seguridad
CREATE OR REPLACE VIEW security_stats AS
SELECT 
    (SELECT COUNT(*) FROM login_attempts WHERE success = 0 AND attempted_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)) as failed_logins_24h,
    (SELECT COUNT(*) FROM login_attempts WHERE success = 1 AND attempted_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)) as successful_logins_24h,
    (SELECT COUNT(DISTINCT ip_address) FROM login_attempts WHERE attempted_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)) as unique_ips_24h,
    (SELECT COUNT(*) FROM backups WHERE status = 'completed') as total_backups,
    (SELECT COUNT(*) FROM backups WHERE status = 'completed' AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)) as backups_last_week,
    (SELECT COUNT(*) FROM audit_log WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)) as audit_entries_24h;

-- Crear procedimiento para limpiar datos antiguos
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS CleanOldSecurityData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Limpiar intentos de login antiguos (más de 30 días)
    DELETE FROM login_attempts 
    WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

    -- Limpiar log de auditoría antiguo (más de 90 días)
    DELETE FROM audit_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

    -- Limpiar backups marcados como eliminados (más de 7 días)
    DELETE FROM backups 
    WHERE status = 'deleted' 
    AND deleted_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

    COMMIT;
END //

DELIMITER ;

-- Crear evento para ejecutar limpieza automática (si los eventos están habilitados)
-- SET GLOBAL event_scheduler = ON;

-- CREATE EVENT IF NOT EXISTS cleanup_security_data
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL CleanOldSecurityData();

-- Crear función para verificar fortaleza de contraseña
DELIMITER //

CREATE FUNCTION IF NOT EXISTS CheckPasswordStrength(password VARCHAR(255))
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE strength INT DEFAULT 0;
    
    -- Verificar longitud mínima
    IF LENGTH(password) >= 8 THEN
        SET strength = strength + 1;
    END IF;
    
    -- Verificar mayúsculas
    IF password REGEXP '[A-Z]' THEN
        SET strength = strength + 1;
    END IF;
    
    -- Verificar minúsculas
    IF password REGEXP '[a-z]' THEN
        SET strength = strength + 1;
    END IF;
    
    -- Verificar números
    IF password REGEXP '[0-9]' THEN
        SET strength = strength + 1;
    END IF;
    
    -- Verificar caracteres especiales
    IF password REGEXP '[^A-Za-z0-9]' THEN
        SET strength = strength + 1;
    END IF;
    
    RETURN strength;
END //

DELIMITER ;

-- Crear trigger para auditar cambios importantes
DELIMITER //

CREATE TRIGGER IF NOT EXISTS audit_user_changes
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    IF OLD.email != NEW.email OR OLD.name != NEW.name OR OLD.is_admin != NEW.is_admin THEN
        INSERT INTO audit_log (user_id, action, resource, resource_id, details, ip_address)
        VALUES (
            NEW.id,
            'update_user',
            'user',
            NEW.id,
            JSON_OBJECT(
                'old_email', OLD.email,
                'new_email', NEW.email,
                'old_name', OLD.name,
                'new_name', NEW.name,
                'old_is_admin', OLD.is_admin,
                'new_is_admin', NEW.is_admin
            ),
            @current_ip
        );
    END IF;
END //

DELIMITER ;

-- Insertar datos de prueba para desarrollo (solo si no existen)
INSERT IGNORE INTO backups (backup_name, backup_type, user_id, file_path, file_size, status) VALUES
('demo_backup_2024-01-01', 'user', 1, '/storage/backups/demo_backup_2024-01-01.backup', 1024000, 'completed'),
('system_backup_2024-01-01', 'full', NULL, '/storage/backups/system_backup_2024-01-01.backup', 5120000, 'completed');

-- Comentarios de documentación
/*
Tablas creadas:
1. backups - Almacena información de backups creados
2. scheduled_backups - Gestiona backups automáticos programados
3. login_attempts - Registra intentos de login para seguridad
4. audit_log - Log de auditoría de acciones importantes
5. security_settings - Configuraciones de seguridad del sistema

Características de seguridad implementadas:
- Tracking de intentos de login fallidos
- Auditoría completa de acciones del usuario
- Gestión de backups con cifrado
- Configuraciones de seguridad flexibles
- Limpieza automática de datos antiguos
- Verificación de fortaleza de contraseñas

Índices optimizados para:
- Consultas por usuario
- Búsquedas por fecha
- Filtros por estado
- Análisis de seguridad
*/
