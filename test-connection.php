<?php
/**
 * Script de prueba rápida para verificar la conexión a la base de datos
 */

echo "<h1>🧪 Prueba de Conexión - Control de Gastos</h1>";

try {
    // Cargar bootstrap
    require_once 'src/bootstrap.php';
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 1rem; margin: 1rem 0; border-radius: 5px;'>";
    echo "✅ Bootstrap cargado correctamente<br>";
    
    // Probar container
    echo "✅ Container inicializado<br>";
    
    // Probar configuración
    $config = $container->get('config');
    echo "✅ Configuración cargada<br>";
    echo "📋 Base de datos configurada: " . $config['database']['connections']['mysql']['database'] . "<br>";
    echo "🖥️ Host: " . $config['database']['connections']['mysql']['host'] . "<br>";
    echo "👤 Usuario: " . $config['database']['connections']['mysql']['username'] . "<br>";
    
    // Probar conexión a base de datos
    $database = $container->get('database');
    echo "✅ Clase SimpleDatabase instanciada<br>";

    $connection = $database->getConnection();
    echo "✅ Conexión PDO establecida<br>";
    
    // Probar consulta simple
    $result = $database->selectOne("SELECT 1 as test");
    echo "✅ Consulta de prueba ejecutada: " . $result['test'] . "<br>";
    
    // Verificar tablas
    $tables = $database->select("SHOW TABLES");
    echo "📊 Tablas encontradas: " . count($tables) . "<br>";
    
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "  - " . $tableName . "<br>";
    }
    
    // Probar sesión
    $session = $container->get('session');
    echo "✅ Sesión inicializada<br>";
    
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 1rem; margin: 1rem 0; border-radius: 5px;'>";
    echo "<h3>🎉 ¡Todo funciona correctamente!</h3>";
    echo "<p>Puedes proceder a probar la aplicación:</p>";
    echo "<ul>";
    echo "<li><a href='public/'>🏠 Ir a la aplicación principal</a></li>";
    echo "<li><a href='public/?route=auth/login'>🔐 Página de login</a></li>";
    echo "<li><a href='create-admin-local.php'>👤 Crear usuario admin</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;'>";
    echo "<h3>❌ Error encontrado:</h3>";
    echo "<p><strong>Mensaje:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Archivo:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    
    echo "<h4>🔧 Posibles soluciones:</h4>";
    echo "<ul>";
    echo "<li>Verifica que XAMPP esté funcionando (Apache y MySQL)</li>";
    echo "<li>Asegúrate de que la base de datos 'control_gastos' exista</li>";
    echo "<li>Verifica las credenciales en el archivo .env</li>";
    echo "<li>Ejecuta <a href='fix-database.php'>fix-database.php</a> para reparar la BD</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Archivo de prueba: " . __FILE__ . "</small></p>";
?>
