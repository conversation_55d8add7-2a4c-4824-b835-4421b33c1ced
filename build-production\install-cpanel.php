<?php
/**
 * Script de instalación para cPanel
 * Ejecutar una sola vez después de subir archivos
 */

echo "<h1>🚀 Instalación Control de Gastos</h1>";

// Verificar PHP
if (version_compare(PHP_VERSION, '8.0.0') < 0) {
    die("❌ Error: Se requiere PHP 8.0 o superior. Versión actual: " . PHP_VERSION);
}

echo "✅ PHP Version: " . PHP_VERSION . "<br>";

// Verificar extensiones
$required_extensions = ['pdo', 'pdo_mysql', 'openssl', 'mbstring', 'json'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        die("❌ Error: Extensión PHP requerida no encontrada: $ext");
    }
    echo "✅ Extensión $ext: OK<br>";
}

// Crear directorios con permisos
$directories = [
    'logs',
    'storage/cache',
    'storage/sessions',
    'storage/uploads',
    'storage/backups'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Directorio creado: $dir<br>";
    }
    
    if (!is_writable($dir)) {
        chmod($dir, 0755);
        echo "⚠️ Permisos ajustados: $dir<br>";
    }
}

// Verificar archivo .env
if (!file_exists('.env')) {
    if (file_exists('.env.production')) {
        echo "⚠️ Renombra .env.production a .env y configura los valores<br>";
    } else {
        echo "❌ Error: Archivo .env no encontrado<br>";
    }
} else {
    echo "✅ Archivo .env encontrado<br>";
}

// Verificar conexión a base de datos
try {
    if (file_exists('.env')) {
        $env = parse_ini_file('.env');
        $pdo = new PDO(
            "mysql:host={$env['DB_HOST']};dbname={$env['DB_NAME']}",
            $env['DB_USER'],
            $env['DB_PASS']
        );
        echo "✅ Conexión a base de datos: OK<br>";
    }
} catch (Exception $e) {
    echo "❌ Error de base de datos: " . $e->getMessage() . "<br>";
}

echo "<h2>📋 Próximos pasos:</h2>";
echo "<ol>";
echo "<li>Renombra .env.production a .env</li>";
echo "<li>Configura los valores en .env</li>";
echo "<li>Ejecuta las migraciones de base de datos</li>";
echo "<li>Elimina este archivo (install-cpanel.php)</li>";
echo "</ol>";

echo "<h2>🔧 Comandos útiles:</h2>";
echo "<p><strong>Ejecutar migraciones:</strong><br>";
echo "<code>php database/run-migrations.php</code></p>";

echo "<p><strong>Crear usuario admin:</strong><br>";
echo "<code>php scripts/create-admin.php</code></p>";
?>
