<?php
$content = ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Recordatorios y Compromisos</h2>
    <div>
        <a href="/reminders/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nuevo Recordatorio
        </a>
        <a href="/reminders/calendar" class="btn btn-outline-info">
            <i class="fas fa-calendar"></i> Calendario
        </a>
        <a href="/reminders/dashboard" class="btn btn-outline-secondary">
            <i class="fas fa-chart-pie"></i> Dashboard
        </a>
        <a href="/reminders/export<?= !empty($filters) ? '?' . http_build_query(array_filter($filters)) : '' ?>" class="btn btn-outline-secondary">
            <i class="fas fa-download"></i> Exportar
        </a>
    </div>
</div>

<!-- Alertas de recordatorios importantes -->
<?php if (!empty($overdue_reminders) || !empty($due_soon_reminders)): ?>
<div class="row mb-4">
    <?php if (!empty($overdue_reminders)): ?>
    <div class="col-md-6">
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Recordatorios Vencidos (<?= count($overdue_reminders) ?>)</h5>
            <ul class="mb-0">
                <?php foreach (array_slice($overdue_reminders, 0, 3) as $reminder): ?>
                    <li>
                        <strong><?= htmlspecialchars($reminder['title']) ?></strong> - 
                        <?= $reminder['amount_formatted'] ?> 
                        (<?= abs($reminder['days_until_due']) ?> días vencido)
                    </li>
                <?php endforeach; ?>
                <?php if (count($overdue_reminders) > 3): ?>
                    <li><em>... y <?= count($overdue_reminders) - 3 ?> más</em></li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($due_soon_reminders)): ?>
    <div class="col-md-6">
        <div class="alert alert-warning">
            <h5><i class="fas fa-clock"></i> Próximos a Vencer (<?= count($due_soon_reminders) ?>)</h5>
            <ul class="mb-0">
                <?php foreach (array_slice($due_soon_reminders, 0, 3) as $reminder): ?>
                    <li>
                        <strong><?= htmlspecialchars($reminder['title']) ?></strong> - 
                        <?= $reminder['amount_formatted'] ?> 
                        (<?= $reminder['days_until_due'] ?> días)
                    </li>
                <?php endforeach; ?>
                <?php if (count($due_soon_reminders) > 3): ?>
                    <li><em>... y <?= count($due_soon_reminders) - 3 ?> más</em></li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<!-- Estadísticas -->
<?php if (!empty($stats['general'])): ?>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">Total Recordatorios</h5>
                <h3><?= number_format($stats['general']['total_reminders']) ?></h3>
                <small class="text-muted"><?= $stats['general']['pending_reminders'] ?> pendientes</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">Vencidos</h5>
                <h3 class="text-danger"><?= $stats['general']['overdue_reminders'] ?></h3>
                <small class="text-muted">Requieren atención</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">Esta Semana</h5>
                <h3 class="text-warning"><?= $stats['general']['due_this_week'] ?></h3>
                <small class="text-muted">Próximos 7 días</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Monto Pendiente</h5>
                <h3 class="text-info">$ <?= number_format($stats['general']['pending_amount'], 2) ?></h3>
                <small class="text-muted">Total por pagar</small>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filtros -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter"></i> Filtros
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="/reminders" id="filtersForm">
            <div class="row">
                <div class="col-md-2">
                    <label for="status" class="form-label">Estado</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Todos</option>
                        <?php foreach ($statuses as $value => $label): ?>
                            <option value="<?= htmlspecialchars($value) ?>" 
                                    <?= ($filters['status'] ?? '') === $value ? 'selected' : '' ?>>
                                <?= htmlspecialchars($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="type" class="form-label">Tipo</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">Todos</option>
                        <?php foreach ($reminder_types as $value => $label): ?>
                            <option value="<?= htmlspecialchars($value) ?>" 
                                    <?= ($filters['type'] ?? '') === $value ? 'selected' : '' ?>>
                                <?= htmlspecialchars($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="priority" class="form-label">Prioridad</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="">Todas</option>
                        <?php foreach ($priorities as $value => $label): ?>
                            <option value="<?= htmlspecialchars($value) ?>" 
                                    <?= ($filters['priority'] ?? '') === $value ? 'selected' : '' ?>>
                                <?= htmlspecialchars($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="search" class="form-label">Buscar</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" 
                               placeholder="Título, descripción..." value="<?= htmlspecialchars($filters['search'] ?? '') ?>">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter"></i> Filtrar
                        </button>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-12">
                    <a href="/reminders" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times"></i> Limpiar Filtros
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Recordatorios -->
<?php if (empty($reminders)): ?>
    <div class="text-center py-5">
        <i class="fas fa-bell fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No hay recordatorios</h4>
        <p class="text-muted">
            <?php if (!empty(array_filter($filters))): ?>
                No se encontraron recordatorios con los filtros aplicados
            <?php else: ?>
                Crea tu primer recordatorio para no olvidar compromisos importantes
            <?php endif; ?>
        </p>
        <a href="/reminders/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nuevo Recordatorio
        </a>
    </div>
<?php else: ?>
    <div class="row">
        <?php foreach ($reminders as $reminder): ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 <?= $reminder['is_overdue'] ? 'border-danger' : ($reminder['is_due_soon'] ? 'border-warning' : '') ?>">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <span class="badge me-2" style="background-color: <?= $reminder['priority_color'] ?>;">
                                <?= $reminder['priority_label'] ?>
                            </span>
                            <span class="badge" style="background-color: <?= $reminder['status_color'] ?>;">
                                <?= $reminder['status_label'] ?>
                            </span>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/reminders/<?= $reminder['id'] ?>">
                                    <i class="fas fa-eye"></i> Ver Detalles
                                </a></li>
                                <li><a class="dropdown-item" href="/reminders/<?= $reminder['id'] ?>/edit">
                                    <i class="fas fa-edit"></i> Editar
                                </a></li>
                                <?php if ($reminder['status'] === 'pending'): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-success" href="#" onclick="markCompleted(<?= $reminder['id'] ?>)">
                                        <i class="fas fa-check"></i> Marcar Completado
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteReminder(<?= $reminder['id'] ?>)">
                                    <i class="fas fa-trash"></i> Eliminar
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($reminder['title']) ?></h5>
                        <p class="card-text text-muted small">
                            <?= htmlspecialchars($reminder['description']) ?>
                        </p>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Monto:</span>
                                <strong class="text-primary"><?= $reminder['amount_formatted'] ?></strong>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Vencimiento:</span>
                                <span class="<?= $reminder['is_overdue'] ? 'text-danger' : ($reminder['is_due_soon'] ? 'text-warning' : '') ?>">
                                    <?= $reminder['due_date_formatted'] ?>
                                </span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">Categoría:</span>
                                <div class="d-flex align-items-center">
                                    <i class="<?= htmlspecialchars($reminder['category_icon'] ?? 'fas fa-folder') ?> me-1" 
                                       style="color: <?= htmlspecialchars($reminder['category_color'] ?? '#007bff') ?>;"></i>
                                    <small><?= htmlspecialchars($reminder['category_name']) ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($reminder['is_overdue']): ?>
                            <div class="alert alert-danger py-2 mb-2">
                                <small><i class="fas fa-exclamation-triangle"></i> 
                                Vencido hace <?= abs($reminder['days_until_due']) ?> días</small>
                            </div>
                        <?php elseif ($reminder['is_due_soon']): ?>
                            <div class="alert alert-warning py-2 mb-2">
                                <small><i class="fas fa-clock"></i> 
                                Vence en <?= $reminder['days_until_due'] ?> días</small>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($reminder['is_recurring']): ?>
                            <div class="mb-2">
                                <span class="badge bg-info">
                                    <i class="fas fa-repeat"></i> <?= $reminder['recurring_type_label'] ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($reminder['notification_enabled']): ?>
                            <div class="mb-2">
                                <span class="badge bg-secondary">
                                    <i class="fas fa-bell"></i> Notificaciones activas
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                Tipo: <?= $reminder['type_label'] ?>
                            </small>
                            <?php if ($reminder['status'] === 'pending'): ?>
                                <button class="btn btn-sm btn-success" onclick="markCompleted(<?= $reminder['id'] ?>)">
                                    <i class="fas fa-check"></i> Completar
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Paginación -->
    <?php if (($pagination['total_pages'] ?? 0) > 1): ?>
        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Paginación de recordatorios">
                <ul class="pagination">
                    <?php if ($pagination['page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $pagination['page'] - 1])) ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $pagination['page'] - 2); $i <= min($pagination['total_pages'], $pagination['page'] + 2); $i++): ?>
                        <li class="page-item <?= $i === $pagination['page'] ? 'active' : '' ?>">
                            <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $i])) ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($pagination['page'] < $pagination['total_pages']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $pagination['page'] + 1])) ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
        <div class="text-center">
            <small class="text-muted">
                Página <?= $pagination['page'] ?> de <?= $pagination['total_pages'] ?> 
                (<?= number_format($pagination['total']) ?> recordatorios)
            </small>
        </div>
    <?php endif; ?>
<?php endif; ?>

<script>
function markCompleted(reminderId) {
    if (confirm('¿Marcar este recordatorio como completado?')) {
        fetch(`/reminders/${reminderId}/complete`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': '<?= $csrf_token ?? '' ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Error al marcar como completado');
            }
        })
        .catch(error => {
            alert('Error al marcar como completado');
        });
    }
}

function deleteReminder(reminderId) {
    if (confirm('¿Está seguro de que desea eliminar este recordatorio?')) {
        fetch(`/reminders/${reminderId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': '<?= $csrf_token ?? '' ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Error al eliminar el recordatorio');
            }
        })
        .catch(error => {
            alert('Error al eliminar el recordatorio');
        });
    }
}

// Auto-submit del formulario de filtros cuando cambian los selects
document.addEventListener('DOMContentLoaded', function() {
    const selects = document.querySelectorAll('#filtersForm select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            setTimeout(() => {
                document.getElementById('filtersForm').submit();
            }, 100);
        });
    });
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/main.php';
?>
