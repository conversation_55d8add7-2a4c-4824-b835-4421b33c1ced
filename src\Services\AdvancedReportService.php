<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Core\Database;
use ControlGastos\Core\Logger;

/**
 * Servicio de Reportes Avanzados
 * Proporciona análisis financieros detallados, predicciones y métricas avanzadas
 */
class AdvancedReportService
{
    private Database $database;
    private Logger $logger;

    public function __construct(Database $database, Logger $logger)
    {
        $this->database = $database;
        $this->logger = $logger;
    }

    /**
     * Obtener dashboard completo con métricas avanzadas
     */
    public function getDashboardData(int $userId): array
    {
        try {
            $currentMonth = date('Y-m-01');
            $currentMonthEnd = date('Y-m-t');
            $previousMonth = date('Y-m-01', strtotime('-1 month'));
            $previousMonthEnd = date('Y-m-t', strtotime('-1 month'));

            return [
                'financial_summary' => $this->getFinancialSummary($userId, $currentMonth, $currentMonthEnd),
                'comparison' => $this->getPeriodComparison($userId, $currentMonth, $currentMonthEnd, $previousMonth, $previousMonthEnd),
                'trends' => $this->getTrendAnalysis($userId, 6),
                'category_analysis' => $this->getCategoryAnalysis($userId, $currentMonth, $currentMonthEnd),
                'spending_patterns' => $this->getSpendingPatterns($userId, 3),
                'financial_health' => $this->getFinancialHealthScore($userId),
                'predictions' => $this->getFinancialPredictions($userId, 3),
                'goals_progress' => $this->getGoalsProgress($userId),
                'alerts' => $this->getFinancialAlerts($userId)
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo datos del dashboard: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener resumen financiero detallado
     */
    public function getFinancialSummary(int $userId, string $startDate, string $endDate): array
    {
        try {
            $query = "
                SELECT 
                    type,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count,
                    AVG(amount) as avg_amount,
                    MIN(amount) as min_amount,
                    MAX(amount) as max_amount,
                    STDDEV(amount) as std_deviation
                FROM transactions 
                WHERE user_id = ? 
                AND transaction_date BETWEEN ? AND ?
                GROUP BY type
            ";

            $data = $this->database->select($query, [$userId, $startDate, $endDate]);
            
            $summary = [
                'total_income' => 0,
                'total_expenses' => 0,
                'net_income' => 0,
                'transaction_count' => 0,
                'avg_transaction' => 0,
                'savings_rate' => 0,
                'expense_ratio' => 0,
                'income_volatility' => 0,
                'expense_volatility' => 0
            ];

            foreach ($data as $row) {
                if ($row['type'] === 'income') {
                    $summary['total_income'] = $row['total_amount'];
                    $summary['income_volatility'] = $row['std_deviation'] ?? 0;
                } else {
                    $summary['total_expenses'] = $row['total_amount'];
                    $summary['expense_volatility'] = $row['std_deviation'] ?? 0;
                }
                $summary['transaction_count'] += $row['transaction_count'];
            }

            $summary['net_income'] = $summary['total_income'] - $summary['total_expenses'];
            $summary['savings_rate'] = $summary['total_income'] > 0 
                ? ($summary['net_income'] / $summary['total_income']) * 100 
                : 0;
            $summary['expense_ratio'] = $summary['total_income'] > 0 
                ? ($summary['total_expenses'] / $summary['total_income']) * 100 
                : 0;
            $summary['avg_transaction'] = $summary['transaction_count'] > 0 
                ? ($summary['total_income'] + $summary['total_expenses']) / $summary['transaction_count'] 
                : 0;

            return $summary;

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo resumen financiero: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener análisis de tendencias
     */
    public function getTrendAnalysis(int $userId, int $months = 12): array
    {
        try {
            $query = "
                SELECT 
                    DATE_FORMAT(transaction_date, '%Y-%m') as month,
                    type,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count,
                    AVG(amount) as avg_amount
                FROM transactions 
                WHERE user_id = ? 
                AND transaction_date >= DATE_SUB(NOW(), INTERVAL ? MONTH)
                GROUP BY DATE_FORMAT(transaction_date, '%Y-%m'), type
                ORDER BY month DESC, type
            ";

            $data = $this->database->select($query, [$userId, $months]);
            
            return $this->processTrendData($data);

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo análisis de tendencias: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener comparación de períodos
     */
    public function getPeriodComparison(int $userId, string $currentStart, string $currentEnd, string $previousStart, string $previousEnd): array
    {
        try {
            $current = $this->getFinancialSummary($userId, $currentStart, $currentEnd);
            $previous = $this->getFinancialSummary($userId, $previousStart, $previousEnd);
            
            return [
                'current' => $current,
                'previous' => $previous,
                'changes' => [
                    'income_change' => $this->calculatePercentageChange($previous['total_income'], $current['total_income']),
                    'expense_change' => $this->calculatePercentageChange($previous['total_expenses'], $current['total_expenses']),
                    'net_change' => $this->calculatePercentageChange($previous['net_income'], $current['net_income']),
                    'savings_rate_change' => $current['savings_rate'] - $previous['savings_rate'],
                    'transaction_count_change' => $this->calculatePercentageChange($previous['transaction_count'], $current['transaction_count'])
                ]
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo comparación de períodos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener análisis de categorías
     */
    public function getCategoryAnalysis(int $userId, string $startDate, string $endDate): array
    {
        try {
            $query = "
                SELECT 
                    c.id as category_id,
                    c.name as category_name,
                    c.color as category_color,
                    c.icon as category_icon,
                    SUM(t.amount) as total_amount,
                    COUNT(t.id) as transaction_count,
                    AVG(t.amount) as avg_amount,
                    MIN(t.amount) as min_amount,
                    MAX(t.amount) as max_amount,
                    STDDEV(t.amount) as std_deviation
                FROM transactions t
                INNER JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ? 
                AND t.type = 'expense'
                AND t.transaction_date BETWEEN ? AND ?
                GROUP BY c.id, c.name, c.color, c.icon
                ORDER BY total_amount DESC
            ";

            $expenses = $this->database->select($query, [$userId, $startDate, $endDate]);
            $totalExpenses = array_sum(array_column($expenses, 'total_amount'));

            $analysis = [];
            foreach ($expenses as $expense) {
                $percentage = $totalExpenses > 0 ? ($expense['total_amount'] / $totalExpenses) * 100 : 0;
                
                $analysis[] = [
                    'category_id' => $expense['category_id'],
                    'category_name' => $expense['category_name'],
                    'category_color' => $expense['category_color'],
                    'category_icon' => $expense['category_icon'],
                    'total_amount' => $expense['total_amount'],
                    'percentage' => $percentage,
                    'transaction_count' => $expense['transaction_count'],
                    'avg_amount' => $expense['avg_amount'],
                    'min_amount' => $expense['min_amount'],
                    'max_amount' => $expense['max_amount'],
                    'volatility' => $expense['std_deviation'] ?? 0,
                    'frequency_score' => $this->calculateFrequencyScore($expense['transaction_count'], $startDate, $endDate)
                ];
            }

            return [
                'categories' => $analysis,
                'total_expenses' => $totalExpenses,
                'concentration_index' => $this->calculateConcentrationIndex($analysis),
                'top_categories' => array_slice($analysis, 0, 5)
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo análisis de categorías: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener patrones de gasto
     */
    public function getSpendingPatterns(int $userId, int $months = 6): array
    {
        try {
            // Patrones por día de la semana
            $weekdayQuery = "
                SELECT 
                    DAYNAME(transaction_date) as day_name,
                    DAYOFWEEK(transaction_date) as day_number,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count,
                    AVG(amount) as avg_amount
                FROM transactions 
                WHERE user_id = ? 
                AND type = 'expense'
                AND transaction_date >= DATE_SUB(NOW(), INTERVAL ? MONTH)
                GROUP BY DAYOFWEEK(transaction_date), DAYNAME(transaction_date)
                ORDER BY day_number
            ";

            $weekdayPatterns = $this->database->select($weekdayQuery, [$userId, $months]);

            // Patrones por hora
            $hourlyQuery = "
                SELECT 
                    HOUR(created_at) as hour,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count
                FROM transactions 
                WHERE user_id = ? 
                AND type = 'expense'
                AND transaction_date >= DATE_SUB(NOW(), INTERVAL ? MONTH)
                GROUP BY HOUR(created_at)
                ORDER BY hour
            ";

            $hourlyPatterns = $this->database->select($hourlyQuery, [$userId, $months]);

            return [
                'weekday_patterns' => $weekdayPatterns,
                'hourly_patterns' => $hourlyPatterns,
                'insights' => $this->generatePatternInsights($weekdayPatterns, $hourlyPatterns),
                'peak_spending_day' => $this->findPeakSpendingDay($weekdayPatterns),
                'peak_spending_hour' => $this->findPeakSpendingHour($hourlyPatterns)
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo patrones de gasto: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener score de salud financiera
     */
    public function getFinancialHealthScore(int $userId): array
    {
        try {
            $currentMonth = date('Y-m-01');
            $currentMonthEnd = date('Y-m-t');
            $last3Months = date('Y-m-01', strtotime('-3 months'));

            $summary = $this->getFinancialSummary($userId, $currentMonth, $currentMonthEnd);
            $trends = $this->getTrendAnalysis($userId, 3);
            $patterns = $this->getSpendingPatterns($userId, 3);

            $scores = [
                'savings_rate_score' => $this->calculateSavingsRateScore($summary['savings_rate']),
                'expense_volatility_score' => $this->calculateVolatilityScore($summary['expense_volatility']),
                'trend_score' => $this->calculateTrendScore($trends),
                'consistency_score' => $this->calculateConsistencyScore($patterns),
                'diversification_score' => $this->calculateDiversificationScore($userId, $currentMonth, $currentMonthEnd)
            ];

            $overallScore = array_sum($scores) / count($scores);

            return [
                'overall_score' => round($overallScore, 1),
                'scores' => $scores,
                'rating' => $this->getHealthRating($overallScore),
                'recommendations' => $this->generateHealthRecommendations($scores)
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error calculando score de salud financiera: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener predicciones financieras
     */
    public function getFinancialPredictions(int $userId, int $forecastMonths = 3): array
    {
        try {
            $historicalData = $this->getHistoricalData($userId, 12);
            
            if (count($historicalData) < 3) {
                return ['error' => 'Datos insuficientes para predicciones'];
            }

            $incomeTrend = $this->calculateTrend($historicalData, 'income');
            $expenseTrend = $this->calculateTrend($historicalData, 'expense');
            
            $predictions = [];
            for ($i = 1; $i <= $forecastMonths; $i++) {
                $futureMonth = date('Y-m', strtotime("+{$i} months"));
                
                $predictedIncome = $this->predictValue($incomeTrend, $i);
                $predictedExpenses = $this->predictValue($expenseTrend, $i);
                
                $predictions[] = [
                    'month' => $futureMonth,
                    'predicted_income' => $predictedIncome,
                    'predicted_expenses' => $predictedExpenses,
                    'predicted_savings' => $predictedIncome - $predictedExpenses,
                    'confidence_level' => $this->calculateConfidence($historicalData, $i)
                ];
            }

            return [
                'predictions' => $predictions,
                'trends' => [
                    'income_trend' => $incomeTrend,
                    'expense_trend' => $expenseTrend
                ],
                'recommendations' => $this->generatePredictionRecommendations($predictions)
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo predicciones: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener progreso de metas
     */
    public function getGoalsProgress(int $userId): array
    {
        try {
            $currentMonth = date('Y-m-01');
            $currentMonthEnd = date('Y-m-t');
            
            $summary = $this->getFinancialSummary($userId, $currentMonth, $currentMonthEnd);
            
            // Metas predefinidas (en el futuro se podrán personalizar)
            $goals = [
                'savings_rate' => [
                    'target' => 20,
                    'current' => $summary['savings_rate'],
                    'unit' => '%'
                ],
                'expense_limit' => [
                    'target' => $summary['total_income'] * 0.8,
                    'current' => $summary['total_expenses'],
                    'unit' => '$'
                ],
                'emergency_fund' => [
                    'target' => $summary['total_expenses'] * 6, // 6 meses de gastos
                    'current' => $this->getEmergencyFundBalance($userId),
                    'unit' => '$'
                ]
            ];

            $progress = [];
            foreach ($goals as $key => $goal) {
                $percentage = $goal['target'] > 0 ? ($goal['current'] / $goal['target']) * 100 : 0;
                $progress[$key] = [
                    'target' => $goal['target'],
                    'current' => $goal['current'],
                    'percentage' => min(100, $percentage),
                    'status' => $this->getGoalStatus($percentage),
                    'unit' => $goal['unit']
                ];
            }

            return $progress;

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo progreso de metas: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener alertas financieras
     */
    public function getFinancialAlerts(int $userId): array
    {
        try {
            $alerts = [];
            $currentMonth = date('Y-m-01');
            $currentMonthEnd = date('Y-m-t');
            
            $summary = $this->getFinancialSummary($userId, $currentMonth, $currentMonthEnd);
            $comparison = $this->getPeriodComparison(
                $userId, 
                $currentMonth, 
                $currentMonthEnd,
                date('Y-m-01', strtotime('-1 month')),
                date('Y-m-t', strtotime('-1 month'))
            );

            // Alerta de gastos altos
            if ($comparison['changes']['expense_change'] > 20) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Aumento significativo en gastos',
                    'message' => 'Tus gastos han aumentado un ' . round($comparison['changes']['expense_change'], 1) . '% este mes',
                    'action' => 'Revisa tus categorías de gasto'
                ];
            }

            // Alerta de baja tasa de ahorro
            if ($summary['savings_rate'] < 10) {
                $alerts[] = [
                    'type' => 'danger',
                    'title' => 'Tasa de ahorro baja',
                    'message' => 'Tu tasa de ahorro es del ' . round($summary['savings_rate'], 1) . '%, recomendamos al menos 20%',
                    'action' => 'Considera reducir gastos no esenciales'
                ];
            }

            // Alerta de gastos concentrados
            $categoryAnalysis = $this->getCategoryAnalysis($userId, $currentMonth, $currentMonthEnd);
            if ($categoryAnalysis['concentration_index'] > 0.5) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'Gastos muy concentrados',
                    'message' => 'La mayoría de tus gastos se concentran en pocas categorías',
                    'action' => 'Considera diversificar tus gastos'
                ];
            }

            return $alerts;

        } catch (\Exception $e) {
            $this->logger->error('Error obteniendo alertas financieras: ' . $e->getMessage());
            return [];
        }
    }

    // ==================== MÉTODOS AUXILIARES ====================

    /**
     * Procesar datos de tendencias
     */
    private function processTrendData(array $data): array
    {
        $processed = [
            'months' => [],
            'income_data' => [],
            'expense_data' => [],
            'net_data' => []
        ];

        $monthlyData = [];
        foreach ($data as $row) {
            $month = $row['month'];
            if (!isset($monthlyData[$month])) {
                $monthlyData[$month] = ['income' => 0, 'expense' => 0];
            }
            $monthlyData[$month][$row['type']] = $row['total_amount'];
        }

        foreach ($monthlyData as $month => $amounts) {
            $processed['months'][] = $month;
            $processed['income_data'][] = $amounts['income'];
            $processed['expense_data'][] = $amounts['expense'];
            $processed['net_data'][] = $amounts['income'] - $amounts['expense'];
        }

        return $processed;
    }

    /**
     * Calcular cambio porcentual
     */
    private function calculatePercentageChange(float $previous, float $current): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        return (($current - $previous) / $previous) * 100;
    }

    /**
     * Calcular score de frecuencia
     */
    private function calculateFrequencyScore(int $transactionCount, string $startDate, string $endDate): float
    {
        $days = (strtotime($endDate) - strtotime($startDate)) / 86400;
        return $days > 0 ? $transactionCount / $days : 0;
    }

    /**
     * Calcular índice de concentración (Herfindahl)
     */
    private function calculateConcentrationIndex(array $categories): float
    {
        $totalAmount = array_sum(array_column($categories, 'total_amount'));
        if ($totalAmount == 0) return 0;

        $herfindahl = 0;
        foreach ($categories as $category) {
            $share = $category['total_amount'] / $totalAmount;
            $herfindahl += $share * $share;
        }

        return $herfindahl;
    }

    /**
     * Generar insights de patrones
     */
    private function generatePatternInsights(array $weekdayPatterns, array $hourlyPatterns): array
    {
        $insights = [];

        // Día con más gastos
        $maxWeekday = array_reduce($weekdayPatterns, function($carry, $item) {
            return (!$carry || $item['total_amount'] > $carry['total_amount']) ? $item : $carry;
        });

        if ($maxWeekday) {
            $insights[] = "Gastas más los " . $maxWeekday['day_name'] . "s";
        }

        // Hora pico
        $maxHour = array_reduce($hourlyPatterns, function($carry, $item) {
            return (!$carry || $item['total_amount'] > $carry['total_amount']) ? $item : $carry;
        });

        if ($maxHour) {
            $insights[] = "Tu hora pico de gastos es a las " . $maxHour['hour'] . ":00";
        }

        return $insights;
    }

    /**
     * Encontrar día pico de gastos
     */
    private function findPeakSpendingDay(array $weekdayPatterns): ?array
    {
        return array_reduce($weekdayPatterns, function($carry, $item) {
            return (!$carry || $item['total_amount'] > $carry['total_amount']) ? $item : $carry;
        });
    }

    /**
     * Encontrar hora pico de gastos
     */
    private function findPeakSpendingHour(array $hourlyPatterns): ?array
    {
        return array_reduce($hourlyPatterns, function($carry, $item) {
            return (!$carry || $item['total_amount'] > $carry['total_amount']) ? $item : $carry;
        });
    }

    /**
     * Calcular score de tasa de ahorro
     */
    private function calculateSavingsRateScore(float $savingsRate): float
    {
        if ($savingsRate >= 20) return 100;
        if ($savingsRate >= 15) return 80;
        if ($savingsRate >= 10) return 60;
        if ($savingsRate >= 5) return 40;
        if ($savingsRate >= 0) return 20;
        return 0;
    }

    /**
     * Calcular score de volatilidad
     */
    private function calculateVolatilityScore(float $volatility): float
    {
        // Menor volatilidad = mejor score
        if ($volatility <= 100) return 100;
        if ($volatility <= 500) return 80;
        if ($volatility <= 1000) return 60;
        if ($volatility <= 2000) return 40;
        return 20;
    }

    /**
     * Calcular score de tendencia
     */
    private function calculateTrendScore(array $trends): float
    {
        // Evalúa si las tendencias son positivas
        $incomeGrowth = $this->calculateGrowthRate($trends['income_data'] ?? []);
        $expenseGrowth = $this->calculateGrowthRate($trends['expense_data'] ?? []);

        $score = 50; // Base score

        if ($incomeGrowth > 0) $score += 25;
        if ($expenseGrowth < 0) $score += 25;
        if ($expenseGrowth > 10) $score -= 25; // Penalizar crecimiento alto de gastos

        return max(0, min(100, $score));
    }

    /**
     * Calcular score de consistencia
     */
    private function calculateConsistencyScore(array $patterns): float
    {
        // Evalúa la consistencia en los patrones de gasto
        $weekdayAmounts = array_column($patterns['weekday_patterns'] ?? [], 'total_amount');
        if (empty($weekdayAmounts)) return 50;

        $mean = array_sum($weekdayAmounts) / count($weekdayAmounts);
        $variance = 0;

        foreach ($weekdayAmounts as $amount) {
            $variance += pow($amount - $mean, 2);
        }

        $variance /= count($weekdayAmounts);
        $coefficient = $mean > 0 ? sqrt($variance) / $mean : 0;

        // Menor coeficiente de variación = mayor consistencia
        if ($coefficient <= 0.2) return 100;
        if ($coefficient <= 0.4) return 80;
        if ($coefficient <= 0.6) return 60;
        if ($coefficient <= 0.8) return 40;
        return 20;
    }

    /**
     * Calcular score de diversificación
     */
    private function calculateDiversificationScore(int $userId, string $startDate, string $endDate): float
    {
        $analysis = $this->getCategoryAnalysis($userId, $startDate, $endDate);
        $concentrationIndex = $analysis['concentration_index'] ?? 1;

        // Menor concentración = mayor diversificación
        return max(0, (1 - $concentrationIndex) * 100);
    }

    /**
     * Obtener rating de salud financiera
     */
    private function getHealthRating(float $score): string
    {
        if ($score >= 90) return 'Excelente';
        if ($score >= 80) return 'Muy Bueno';
        if ($score >= 70) return 'Bueno';
        if ($score >= 60) return 'Regular';
        if ($score >= 50) return 'Necesita Mejoras';
        return 'Crítico';
    }

    /**
     * Generar recomendaciones de salud financiera
     */
    private function generateHealthRecommendations(array $scores): array
    {
        $recommendations = [];

        if ($scores['savings_rate_score'] < 60) {
            $recommendations[] = 'Aumenta tu tasa de ahorro reduciendo gastos no esenciales';
        }

        if ($scores['expense_volatility_score'] < 60) {
            $recommendations[] = 'Trata de mantener gastos más consistentes mes a mes';
        }

        if ($scores['diversification_score'] < 60) {
            $recommendations[] = 'Diversifica tus gastos entre más categorías';
        }

        if ($scores['trend_score'] < 60) {
            $recommendations[] = 'Controla el crecimiento de tus gastos';
        }

        if (empty($recommendations)) {
            $recommendations[] = '¡Excelente manejo financiero! Mantén estos hábitos';
        }

        return $recommendations;
    }

    /**
     * Obtener datos históricos
     */
    private function getHistoricalData(int $userId, int $months): array
    {
        $query = "
            SELECT
                DATE_FORMAT(transaction_date, '%Y-%m') as month,
                type,
                SUM(amount) as total_amount
            FROM transactions
            WHERE user_id = ?
            AND transaction_date >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            GROUP BY DATE_FORMAT(transaction_date, '%Y-%m'), type
            ORDER BY month ASC
        ";

        return $this->database->select($query, [$userId, $months]);
    }

    /**
     * Calcular tendencia
     */
    private function calculateTrend(array $data, string $type): array
    {
        $typeData = array_filter($data, fn($item) => $item['type'] === $type);
        $amounts = array_column($typeData, 'total_amount');

        if (count($amounts) < 2) {
            return ['slope' => 0, 'intercept' => 0, 'r_squared' => 0];
        }

        return $this->linearRegression($amounts);
    }

    /**
     * Regresión lineal simple
     */
    private function linearRegression(array $y): array
    {
        $n = count($y);
        $x = range(1, $n);

        $sumX = array_sum($x);
        $sumY = array_sum($y);
        $sumXY = 0;
        $sumXX = 0;

        for ($i = 0; $i < $n; $i++) {
            $sumXY += $x[$i] * $y[$i];
            $sumXX += $x[$i] * $x[$i];
        }

        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumXX - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;

        // Calcular R²
        $meanY = $sumY / $n;
        $ssTotal = 0;
        $ssRes = 0;

        for ($i = 0; $i < $n; $i++) {
            $predicted = $slope * $x[$i] + $intercept;
            $ssTotal += pow($y[$i] - $meanY, 2);
            $ssRes += pow($y[$i] - $predicted, 2);
        }

        $rSquared = $ssTotal > 0 ? 1 - ($ssRes / $ssTotal) : 0;

        return [
            'slope' => $slope,
            'intercept' => $intercept,
            'r_squared' => $rSquared
        ];
    }

    /**
     * Predecir valor futuro
     */
    private function predictValue(array $trend, int $periodsAhead): float
    {
        $baseX = 1; // Punto de referencia
        $predictedX = $baseX + $periodsAhead;
        return max(0, $trend['slope'] * $predictedX + $trend['intercept']);
    }

    /**
     * Calcular confianza de predicción
     */
    private function calculateConfidence(array $data, int $periodsAhead): float
    {
        $baseConfidence = 90;
        $decayRate = 10; // Pérdida de confianza por período

        return max(30, $baseConfidence - ($periodsAhead * $decayRate));
    }

    /**
     * Generar recomendaciones de predicción
     */
    private function generatePredictionRecommendations(array $predictions): array
    {
        $recommendations = [];

        foreach ($predictions as $prediction) {
            $savings = $prediction['predicted_savings'];

            if ($savings < 0) {
                $recommendations[] = "En {$prediction['month']} podrías tener déficit. Considera reducir gastos.";
            } elseif ($savings > 0) {
                $recommendations[] = "En {$prediction['month']} tendrás excedente. Considera invertir o ahorrar.";
            }
        }

        return $recommendations;
    }

    /**
     * Calcular tasa de crecimiento
     */
    private function calculateGrowthRate(array $data): float
    {
        if (count($data) < 2) return 0;

        $first = reset($data);
        $last = end($data);

        if ($first == 0) return $last > 0 ? 100 : 0;

        return (($last - $first) / $first) * 100;
    }

    /**
     * Obtener balance del fondo de emergencia
     */
    private function getEmergencyFundBalance(int $userId): float
    {
        // Buscar cuenta marcada como fondo de emergencia o cuenta de ahorros
        $query = "
            SELECT SUM(balance) as total_balance
            FROM accounts
            WHERE user_id = ?
            AND (type = 'savings' OR name LIKE '%emergencia%' OR name LIKE '%emergency%')
            AND is_active = 1
        ";

        $result = $this->database->selectOne($query, [$userId]);
        return $result['total_balance'] ?? 0;
    }

    /**
     * Obtener estado de meta
     */
    private function getGoalStatus(float $percentage): string
    {
        if ($percentage >= 100) return 'completed';
        if ($percentage >= 75) return 'on_track';
        if ($percentage >= 50) return 'behind';
        return 'critical';
    }
}
