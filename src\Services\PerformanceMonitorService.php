<?php

declare(strict_types=1);

namespace ControlGastos\Services;

use ControlGastos\Core\Logger;
use Exception;

/**
 * Servicio de monitoreo de performance
 * Monitorea el rendimiento de la aplicación y genera métricas
 */
class PerformanceMonitorService
{
    private Logger $logger;
    private CacheService $cache;
    private array $metrics;
    private float $requestStartTime;
    private array $memorySnapshots;

    public function __construct(Logger $logger, CacheService $cache)
    {
        $this->logger = $logger;
        $this->cache = $cache;
        $this->metrics = [];
        $this->requestStartTime = microtime(true);
        $this->memorySnapshots = [];
        
        $this->initializeMonitoring();
    }

    /**
     * Inicializar monitoreo
     */
    private function initializeMonitoring(): void
    {
        // Registrar snapshot inicial de memoria
        $this->takeMemorySnapshot('request_start');
        
        // Registrar shutdown function para métricas finales
        register_shutdown_function([$this, 'recordFinalMetrics']);
    }

    /**
     * Iniciar medición de tiempo
     */
    public function startTimer(string $name): void
    {
        $this->metrics[$name] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'type' => 'timer'
        ];
    }

    /**
     * Finalizar medición de tiempo
     */
    public function endTimer(string $name): float
    {
        if (!isset($this->metrics[$name])) {
            $this->logger->warning("Timer '{$name}' no fue iniciado");
            return 0.0;
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $duration = $endTime - $this->metrics[$name]['start_time'];
        $memoryUsed = $endMemory - $this->metrics[$name]['start_memory'];

        $this->metrics[$name] = array_merge($this->metrics[$name], [
            'end_time' => $endTime,
            'end_memory' => $endMemory,
            'duration' => $duration,
            'memory_used' => $memoryUsed,
            'completed' => true
        ]);

        // Log si es una operación lenta
        if ($duration > 1.0) {
            $this->logger->warning("Operación lenta detectada: {$name}", [
                'duration' => $duration,
                'memory_used' => $memoryUsed
            ]);
        }

        return $duration;
    }

    /**
     * Registrar métrica personalizada
     */
    public function recordMetric(string $name, $value, string $type = 'gauge', array $tags = []): void
    {
        $this->metrics[$name] = [
            'value' => $value,
            'type' => $type,
            'tags' => $tags,
            'timestamp' => microtime(true)
        ];
    }

    /**
     * Incrementar contador
     */
    public function incrementCounter(string $name, int $value = 1, array $tags = []): void
    {
        $currentValue = $this->metrics[$name]['value'] ?? 0;
        $this->recordMetric($name, $currentValue + $value, 'counter', $tags);
    }

    /**
     * Tomar snapshot de memoria
     */
    public function takeMemorySnapshot(string $label): void
    {
        $this->memorySnapshots[$label] = [
            'timestamp' => microtime(true),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit')
        ];
    }

    /**
     * Monitorear consulta de base de datos
     */
    public function monitorDatabaseQuery(string $query, callable $callback, array $params = [])
    {
        $queryHash = md5($query);
        $this->startTimer("db_query_{$queryHash}");
        
        try {
            $result = $callback();
            $this->incrementCounter('db_queries_success');
            return $result;
        } catch (Exception $e) {
            $this->incrementCounter('db_queries_error');
            throw $e;
        } finally {
            $duration = $this->endTimer("db_query_{$queryHash}");
            
            // Registrar métricas de consulta
            $this->recordMetric('db_query_duration', $duration, 'histogram', [
                'query_type' => $this->getQueryType($query)
            ]);
        }
    }

    /**
     * Monitorear operación de cache
     */
    public function monitorCacheOperation(string $operation, string $key, callable $callback)
    {
        $this->startTimer("cache_{$operation}");
        
        try {
            $result = $callback();
            $this->incrementCounter("cache_{$operation}_success");
            return $result;
        } catch (Exception $e) {
            $this->incrementCounter("cache_{$operation}_error");
            throw $e;
        } finally {
            $this->endTimer("cache_{$operation}");
        }
    }

    /**
     * Obtener métricas actuales
     */
    public function getMetrics(): array
    {
        return [
            'request_metrics' => $this->getRequestMetrics(),
            'performance_metrics' => $this->metrics,
            'memory_snapshots' => $this->memorySnapshots,
            'system_metrics' => $this->getSystemMetrics()
        ];
    }

    /**
     * Obtener métricas de request
     */
    public function getRequestMetrics(): array
    {
        $currentTime = microtime(true);
        $currentMemory = memory_get_usage(true);
        
        return [
            'request_duration' => $currentTime - $this->requestStartTime,
            'memory_usage' => $currentMemory,
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => $this->parseMemoryLimit(ini_get('memory_limit')),
            'memory_usage_percentage' => ($currentMemory / $this->parseMemoryLimit(ini_get('memory_limit'))) * 100
        ];
    }

    /**
     * Obtener métricas del sistema
     */
    public function getSystemMetrics(): array
    {
        $metrics = [
            'php_version' => PHP_VERSION,
            'server_load' => null,
            'disk_usage' => null,
            'cpu_usage' => null
        ];

        // Obtener carga del servidor (solo en sistemas Unix)
        if (function_exists('sys_getloadavg')) {
            $metrics['server_load'] = sys_getloadavg();
        }

        // Obtener uso de disco
        $diskTotal = disk_total_space('.');
        $diskFree = disk_free_space('.');
        if ($diskTotal && $diskFree) {
            $metrics['disk_usage'] = [
                'total' => $diskTotal,
                'free' => $diskFree,
                'used' => $diskTotal - $diskFree,
                'usage_percentage' => (($diskTotal - $diskFree) / $diskTotal) * 100
            ];
        }

        return $metrics;
    }

    /**
     * Generar reporte de performance
     */
    public function generatePerformanceReport(): array
    {
        $metrics = $this->getMetrics();
        
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'summary' => $this->generateSummary($metrics),
            'detailed_metrics' => $metrics,
            'recommendations' => $this->generateRecommendations($metrics),
            'alerts' => $this->generateAlerts($metrics)
        ];

        return $report;
    }

    /**
     * Obtener estadísticas de performance históricas
     */
    public function getHistoricalStats(int $hours = 24): array
    {
        try {
            $stats = [];
            $now = time();
            
            for ($i = 0; $i < $hours; $i++) {
                $hour = $now - ($i * 3600);
                $hourKey = date('Y-m-d-H', $hour);
                
                $hourStats = $this->cache->get("performance_stats_{$hourKey}", [
                    'requests' => 0,
                    'avg_response_time' => 0,
                    'errors' => 0,
                    'memory_peak' => 0
                ]);
                
                $stats[$hourKey] = $hourStats;
            }
            
            return array_reverse($stats, true);

        } catch (Exception $e) {
            $this->logger->error('Error obteniendo estadísticas históricas: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Registrar métricas por hora
     */
    public function recordHourlyStats(): void
    {
        try {
            $hourKey = date('Y-m-d-H');
            $currentStats = $this->cache->get("performance_stats_{$hourKey}", [
                'requests' => 0,
                'total_response_time' => 0,
                'errors' => 0,
                'memory_peak' => 0
            ]);

            $requestMetrics = $this->getRequestMetrics();
            
            $currentStats['requests']++;
            $currentStats['total_response_time'] += $requestMetrics['request_duration'];
            $currentStats['avg_response_time'] = $currentStats['total_response_time'] / $currentStats['requests'];
            $currentStats['memory_peak'] = max($currentStats['memory_peak'], $requestMetrics['memory_peak']);

            $this->cache->set("performance_stats_{$hourKey}", $currentStats, 86400); // 24 horas

        } catch (Exception $e) {
            $this->logger->error('Error registrando estadísticas por hora: ' . $e->getMessage());
        }
    }

    /**
     * Registrar métricas finales (llamado en shutdown)
     */
    public function recordFinalMetrics(): void
    {
        try {
            $this->takeMemorySnapshot('request_end');
            $this->recordHourlyStats();
            
            $finalMetrics = $this->getRequestMetrics();
            
            // Log de request completado
            $this->logger->info('Request completado', [
                'duration' => $finalMetrics['request_duration'],
                'memory_peak' => $finalMetrics['memory_peak'],
                'memory_usage_percentage' => $finalMetrics['memory_usage_percentage']
            ]);

        } catch (Exception $e) {
            $this->logger->error('Error registrando métricas finales: ' . $e->getMessage());
        }
    }

    /**
     * Obtener tipo de consulta SQL
     */
    private function getQueryType(string $query): string
    {
        $query = trim(strtoupper($query));
        
        if (strpos($query, 'SELECT') === 0) return 'SELECT';
        if (strpos($query, 'INSERT') === 0) return 'INSERT';
        if (strpos($query, 'UPDATE') === 0) return 'UPDATE';
        if (strpos($query, 'DELETE') === 0) return 'DELETE';
        
        return 'OTHER';
    }

    /**
     * Parsear límite de memoria
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }

    /**
     * Generar resumen de métricas
     */
    private function generateSummary(array $metrics): array
    {
        $requestMetrics = $metrics['request_metrics'];
        
        return [
            'request_duration' => round($requestMetrics['request_duration'], 3),
            'memory_usage_mb' => round($requestMetrics['memory_usage'] / 1024 / 1024, 2),
            'memory_peak_mb' => round($requestMetrics['memory_peak'] / 1024 / 1024, 2),
            'memory_usage_percentage' => round($requestMetrics['memory_usage_percentage'], 1),
            'total_timers' => count(array_filter($metrics['performance_metrics'], fn($m) => ($m['type'] ?? '') === 'timer')),
            'completed_timers' => count(array_filter($metrics['performance_metrics'], fn($m) => ($m['type'] ?? '') === 'timer' && ($m['completed'] ?? false)))
        ];
    }

    /**
     * Generar recomendaciones
     */
    private function generateRecommendations(array $metrics): array
    {
        $recommendations = [];
        $requestMetrics = $metrics['request_metrics'];
        
        // Recomendaciones de memoria
        if ($requestMetrics['memory_usage_percentage'] > 80) {
            $recommendations[] = [
                'type' => 'memory',
                'priority' => 'high',
                'message' => 'Alto uso de memoria detectado. Considerar optimización de código o aumento de límite.'
            ];
        }

        // Recomendaciones de tiempo de respuesta
        if ($requestMetrics['request_duration'] > 2.0) {
            $recommendations[] = [
                'type' => 'performance',
                'priority' => 'medium',
                'message' => 'Tiempo de respuesta alto. Revisar consultas de base de datos y operaciones costosas.'
            ];
        }

        // Recomendaciones de disco
        if (isset($metrics['system_metrics']['disk_usage']['usage_percentage']) && 
            $metrics['system_metrics']['disk_usage']['usage_percentage'] > 90) {
            $recommendations[] = [
                'type' => 'storage',
                'priority' => 'high',
                'message' => 'Espacio en disco bajo. Considerar limpieza de archivos temporales y logs.'
            ];
        }

        return $recommendations;
    }

    /**
     * Generar alertas
     */
    private function generateAlerts(array $metrics): array
    {
        $alerts = [];
        $requestMetrics = $metrics['request_metrics'];
        
        // Alertas críticas
        if ($requestMetrics['memory_usage_percentage'] > 95) {
            $alerts[] = [
                'level' => 'critical',
                'message' => 'Uso de memoria crítico: ' . round($requestMetrics['memory_usage_percentage'], 1) . '%'
            ];
        }

        if ($requestMetrics['request_duration'] > 5.0) {
            $alerts[] = [
                'level' => 'critical',
                'message' => 'Tiempo de respuesta crítico: ' . round($requestMetrics['request_duration'], 2) . 's'
            ];
        }

        // Alertas de advertencia
        if ($requestMetrics['memory_usage_percentage'] > 80) {
            $alerts[] = [
                'level' => 'warning',
                'message' => 'Alto uso de memoria: ' . round($requestMetrics['memory_usage_percentage'], 1) . '%'
            ];
        }

        return $alerts;
    }
}
