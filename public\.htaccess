# =====================================================
# CONFIGURACIÓN APACHE - CONTROL DE GASTOS
# =====================================================

# Habilitar reescritura de URLs
RewriteEngine On

# Redirigir todo el tráfico HTTPS (opcional, descomentar si se usa SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Bloquear acceso a archivos sensibles
<FilesMatch "\.(env|log|sql|md|json|lock|yml|yaml|xml)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Bloquear acceso a directorios del sistema
RedirectMatch 404 /\.git
RedirectMatch 404 /\.svn
RedirectMatch 404 /vendor
RedirectMatch 404 /config
RedirectMatch 404 /src
RedirectMatch 404 /storage
RedirectMatch 404 /database
RedirectMatch 404 /tests

# Configuración de seguridad
<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevenir MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # Habilitar XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Configurar Content Security Policy básico
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';"
    
    # Configurar Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Configurar Feature Policy
    Header set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Configuración de compresión
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Configuración de caché
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Imágenes
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    
    # CSS y JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # Fuentes
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # JSON y XML
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
</IfModule>

# Configuración de límites
<IfModule mod_limitreq.c>
    # Limitar requests por IP (requiere mod_evasive o similar)
    # LimitRequestBody 10485760  # 10MB
</IfModule>

# Reescritura de URLs para el router
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Configuración de PHP (si no se puede configurar en php.ini)
<IfModule mod_php.c>
    php_value upload_max_filesize 5M
    php_value post_max_size 5M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log /var/log/php_errors.log
    php_flag expose_php Off
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
</IfModule>

# Configuración de tipos MIME
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
</IfModule>
