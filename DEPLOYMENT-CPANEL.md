# 🚀 **G<PERSON>ía Completa: Deployment en cPanel**

## 📋 **PASO 1: Preparación Local**

### **1.1 Ejecutar Script de Preparación**
```bash
# Dar permisos de ejecución
chmod +x scripts/prepare-production.sh

# Ejecutar preparación
./scripts/prepare-production.sh
```

Esto creará:
- 📁 `build-production/` - Archivos listos para subir
- 📦 `control-gastos-produccion.zip` - Archivo comprimido

### **1.2 Verificar Archivos Generados**
- ✅ `.env.production` - Configuración de producción
- ✅ `install-cpanel.php` - Script de instalación
- ✅ `.htaccess` - Configuración de seguridad
- ✅ `README-PRODUCCION.md` - Instrucciones

---

## 📋 **PASO 2: Configuración en cPanel**

### **2.1 Acceder a cPanel**
1. Ingresa a tu panel de control de hosting
2. Bus<PERSON> la sección **"Archivos"** o **"File Manager"**

### **2.2 Crear Base de Datos**
1. Ve a **"MySQL Databases"**
2. Crea una nueva base de datos:
   - Nombre: `tu_usuario_control_gastos`
3. Crea un usuario de BD:
   - Usuario: `tu_usuario_admin`
   - Contraseña: **Genera una segura**
4. Asigna el usuario a la base de datos con **TODOS** los privilegios

### **2.3 Configurar Email (Opcional)**
1. Ve a **"Email Accounts"**
2. Crea cuenta: `<EMAIL>`
3. Anota las configuraciones SMTP

---

## 📋 **PASO 3: Subir Archivos**

### **3.1 Estructura de Directorios en cPanel**
```
/home/<USER>/
├── public_html/          # Contenido de public/
│   ├── index.php
│   ├── assets/
│   └── .htaccess
├── control-gastos/       # Resto de la aplicación
│   ├── src/
│   ├── templates/
│   ├── database/
│   ├── config/
│   ├── logs/
│   └── storage/
└── .env                  # Configuración
```

### **3.2 Subir Archivos**

#### **Opción A: File Manager de cPanel**
1. Abre **File Manager**
2. Sube `control-gastos-produccion.zip` al directorio raíz
3. Extrae el archivo ZIP
4. Mueve el contenido de `public/` a `public_html/`
5. Mueve el resto a `control-gastos/`

#### **Opción B: FTP/SFTP**
```bash
# Usando FileZilla o similar
# Subir archivos según la estructura mostrada arriba
```

### **3.3 Configurar Permisos**
```bash
# En File Manager, seleccionar directorios y cambiar permisos:
logs/           → 755
storage/        → 755
storage/cache/  → 755
storage/sessions/ → 755
.env            → 600 (solo lectura del propietario)
```

---

## 📋 **PASO 4: Configuración de la Aplicación**

### **4.1 Configurar Variables de Entorno**
1. Renombra `.env.production` a `.env`
2. Edita `.env` con tus datos reales:

```env
# Entorno
APP_ENV=production
APP_DEBUG=false
APP_URL=https://tudominio.com

# Base de datos (datos de cPanel)
DB_HOST=localhost
DB_NAME=tu_usuario_control_gastos
DB_USER=tu_usuario_admin
DB_PASS=tu_password_seguro

# Seguridad (generar claves únicas)
APP_KEY=tu_clave_32_caracteres_unica_aqui
ENCRYPTION_KEY=otra_clave_32_caracteres_diferente

# Email SMTP (configuración de cPanel)
SMTP_HOST=mail.tudominio.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=password_del_email
SMTP_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Control de Gastos"
```

### **4.2 Generar Claves Seguras**
```php
// Ejecutar en PHP para generar claves:
echo bin2hex(random_bytes(16)); // Para APP_KEY
echo bin2hex(random_bytes(16)); // Para ENCRYPTION_KEY
```

---

## 📋 **PASO 5: Instalación y Configuración**

### **5.1 Ejecutar Instalación**
1. Visita: `https://tudominio.com/install-cpanel.php`
2. Verifica que todos los checks estén en ✅
3. Sigue las instrucciones mostradas

### **5.2 Ejecutar Migraciones**
1. Visita: `https://tudominio.com/database/run-migrations.php`
2. Espera a que todas las migraciones se ejecuten
3. Verifica que no haya errores

### **5.3 Crear Usuario Administrador**
1. Visita: `https://tudominio.com/scripts/create-admin.php`
2. Completa el formulario:
   - **Nombre**: Tu nombre
   - **Apellido**: Tu apellido
   - **Email**: <EMAIL>
   - **Contraseña**: Mínimo 8 caracteres
3. Haz clic en **"Crear Administrador"**

---

## 📋 **PASO 6: Seguridad Post-Instalación**

### **6.1 Eliminar Archivos de Instalación**
```bash
# Eliminar estos archivos por seguridad:
rm install-cpanel.php
rm database/run-migrations.php
rm scripts/create-admin.php
rm README-PRODUCCION.md
```

### **6.2 Verificar Configuración de Seguridad**
1. Verifica que `.htaccess` esté funcionando
2. Intenta acceder a: `tudominio.com/src/` (debe dar error 403)
3. Intenta acceder a: `tudominio.com/.env` (debe dar error 403)

### **6.3 Configurar SSL (Recomendado)**
1. En cPanel, ve a **"SSL/TLS"**
2. Activa **"Let's Encrypt"** o sube tu certificado
3. Actualiza `APP_URL` en `.env` a `https://`

---

## 📋 **PASO 7: Configuración de Cron Jobs (Opcional)**

### **7.1 Configurar Tareas Automáticas**
En cPanel → **"Cron Jobs"**:

```bash
# Procesar emails cada 5 minutos
*/5 * * * * /usr/local/bin/php /home/<USER>/control-gastos/src/Commands/ProcessEmailQueueCommand.php

# Backup diario a las 2:00 AM
0 2 * * * /usr/local/bin/php /home/<USER>/control-gastos/src/Commands/BackupCommand.php

# Limpiar logs semanalmente
0 3 * * 0 /usr/local/bin/php /home/<USER>/control-gastos/src/Commands/CleanupCommand.php
```

---

## 📋 **PASO 8: Verificación Final**

### **8.1 Pruebas Funcionales**
1. ✅ Accede a `https://tudominio.com`
2. ✅ Inicia sesión con el usuario admin creado
3. ✅ Crea una cuenta de prueba
4. ✅ Registra una transacción
5. ✅ Verifica que los reportes funcionen
6. ✅ Prueba el envío de emails (si configuraste SMTP)

### **8.2 Verificar Logs**
1. Revisa `/home/<USER>/control-gastos/logs/`
2. Verifica que no haya errores críticos
3. Configura monitoreo de logs si es necesario

---

## 🆘 **Solución de Problemas Comunes**

### **Error 500 - Internal Server Error**
```bash
# Verificar:
1. Permisos de archivos y directorios
2. Configuración en .env
3. Logs de PHP en cPanel
4. Versión de PHP (debe ser 8.0+)
```

### **Error de Base de Datos**
```bash
# Verificar:
1. Credenciales en .env
2. Que la base de datos existe
3. Permisos del usuario de BD
4. Que las migraciones se ejecutaron
```

### **Emails no se envían**
```bash
# Verificar:
1. Configuración SMTP en .env
2. Que la cuenta de email existe
3. Firewall del servidor (puerto 587)
4. Logs de email en /logs/
```

### **Archivos no se cargan**
```bash
# Verificar:
1. Rutas en .htaccess
2. Permisos de public_html/
3. Configuración de PHP
4. Límites de memoria y tiempo
```

---

## 📞 **Soporte y Mantenimiento**

### **Monitoreo Regular**
- 📊 Revisar logs semanalmente
- 🔄 Hacer backup mensual manual
- 🔒 Actualizar contraseñas trimestralmente
- 📈 Monitorear uso de recursos

### **Actualizaciones**
- 🔄 Siempre hacer backup antes de actualizar
- 🧪 Probar en staging si es posible
- 📝 Documentar cambios realizados

### **Contacto**
- 📧 Logs de error para diagnóstico
- 🔍 Información del hosting y PHP
- 📋 Pasos reproducir el problema

---

## ✅ **¡Felicitaciones!**

Tu aplicación **Control de Gastos** está ahora funcionando en producción con:

- 🔒 **Seguridad empresarial**
- 📊 **Analytics avanzados**
- 📧 **Sistema de emails**
- 💾 **Backup automático**
- 📱 **Diseño responsive**
- ⚡ **Performance optimizado**

¡Disfruta de tu nueva plataforma financiera! 🎉
