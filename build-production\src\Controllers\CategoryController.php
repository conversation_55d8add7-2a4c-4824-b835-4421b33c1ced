<?php

declare(strict_types=1);

namespace ControlGastos\Controllers;

use ControlGastos\Core\Container;
use ControlGastos\Services\CategoryService;
use ControlGastos\Core\Session;

/**
 * Controlador de categorías y subcategorías
 * Maneja todas las rutas relacionadas con gestión de categorías
 */
class CategoryController
{
    private Container $container;
    private CategoryService $categoryService;
    private Session $session;
    private int $userId;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->categoryService = $container->get('categoryService');
        $this->session = $container->get('session');
        $this->userId = $this->session->get('user_id');
    }

    /**
     * Mostrar lista de categorías
     */
    public function index(): string
    {
        $categoriesResult = $this->categoryService->getUserCategories($this->userId);
        $statsResult = $this->categoryService->getCategoryStats($this->userId);

        $data = [
            'title' => 'Categorías y Subcategorías',
            'categories' => $categoriesResult['success'] ? $categoriesResult['categories'] : [],
            'stats' => $statsResult['success'] ? $statsResult['stats'] : [],
            'colors' => $this->categoryService->getAvailableColors(),
            'icons' => $this->categoryService->getAvailableIcons(),
            'csrf_token' => $this->session->getCsrfToken(),
            'success' => $this->session->getFlash('success'),
            'error' => $this->session->getFlash('error')
        ];

        return $this->render('categories/index', $data);
    }

    /**
     * Crear nueva categoría (AJAX)
     */
    public function store(): void
    {
        $data = [
            'name' => $_POST['name'] ?? '',
            'description' => $_POST['description'] ?? '',
            'color' => $_POST['color'] ?? '#007bff',
            'icon' => $_POST['icon'] ?? 'fas fa-folder'
        ];

        $result = $this->categoryService->createCategory($data, $this->userId);

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /categories');
        exit;
    }

    /**
     * Actualizar categoría (AJAX)
     */
    public function update(int $id): void
    {
        $data = [
            'name' => $_POST['name'] ?? '',
            'description' => $_POST['description'] ?? '',
            'color' => $_POST['color'] ?? '',
            'icon' => $_POST['icon'] ?? ''
        ];

        $result = $this->categoryService->updateCategory($id, $data, $this->userId);

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /categories');
        exit;
    }

    /**
     * Eliminar categoría
     */
    public function delete(int $id): void
    {
        $result = $this->categoryService->deleteCategory($id, $this->userId);

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /categories');
        exit;
    }

    /**
     * Activar/Desactivar categoría
     */
    public function toggleStatus(int $id): void
    {
        $result = $this->categoryService->toggleCategoryStatus($id, $this->userId);

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
        }

        if ($result['success']) {
            $this->session->flash('success', $result['message']);
        } else {
            $this->session->flash('error', $result['message']);
        }

        header('Location: /categories');
        exit;
    }

    /**
     * Obtener subcategorías por categoría (AJAX)
     */
    public function getSubcategories(int $categoryId): void
    {
        $result = $this->categoryService->getSubcategoriesByCategory($categoryId, $this->userId);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Crear nueva subcategoría (AJAX)
     */
    public function storeSubcategory(): void
    {
        $data = [
            'category_id' => (int) ($_POST['category_id'] ?? 0),
            'name' => $_POST['name'] ?? '',
            'description' => $_POST['description'] ?? ''
        ];

        $result = $this->categoryService->createSubcategory($data, $this->userId);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Actualizar subcategoría (AJAX)
     */
    public function updateSubcategory(int $id): void
    {
        $data = [
            'category_id' => (int) ($_POST['category_id'] ?? 0),
            'name' => $_POST['name'] ?? '',
            'description' => $_POST['description'] ?? ''
        ];

        $result = $this->categoryService->updateSubcategory($id, $data, $this->userId);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Eliminar subcategoría (AJAX)
     */
    public function deleteSubcategory(int $id): void
    {
        $result = $this->categoryService->deleteSubcategory($id, $this->userId);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    /**
     * Obtener datos para formularios (colores e iconos)
     */
    public function getFormData(): void
    {
        $data = [
            'success' => true,
            'colors' => $this->categoryService->getAvailableColors(),
            'icons' => $this->categoryService->getAvailableIcons()
        ];

        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Buscar categorías (AJAX)
     */
    public function search(): void
    {
        $search = $_GET['q'] ?? '';
        $activeOnly = isset($_GET['active_only']) ? 
            filter_var($_GET['active_only'], FILTER_VALIDATE_BOOLEAN) : true;

        if (strlen($search) < 2) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'El término de búsqueda debe tener al menos 2 caracteres'
            ]);
            exit;
        }

        // Por ahora, obtenemos todas las categorías y filtramos
        $result = $this->categoryService->getUserCategories($this->userId, $activeOnly);
        
        if ($result['success']) {
            $filtered = array_filter($result['categories'], function($category) use ($search) {
                return stripos($category['name'], $search) !== false ||
                       stripos($category['description'] ?? '', $search) !== false;
            });

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'categories' => array_values($filtered)
            ]);
        } else {
            header('Content-Type: application/json');
            echo json_encode($result);
        }
        exit;
    }

    /**
     * Exportar categorías (JSON)
     */
    public function export(): void
    {
        $result = $this->categoryService->getUserCategories($this->userId, false);
        
        if ($result['success']) {
            $filename = 'categorias_' . date('Y-m-d_H-i-s') . '.json';
            
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            echo json_encode([
                'export_date' => date('Y-m-d H:i:s'),
                'user_id' => $this->userId,
                'categories' => $result['categories']
            ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        } else {
            $this->session->flash('error', 'Error al exportar categorías');
            header('Location: /categories');
        }
        exit;
    }

    /**
     * Renderizar vista
     */
    private function render(string $view, array $data = []): string
    {
        $viewPath = __DIR__ . '/../../templates/pages/' . $view . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("Vista no encontrada: {$view}");
        }

        extract($data);
        ob_start();
        include $viewPath;
        return ob_get_clean();
    }

    /**
     * Verificar si es una request AJAX
     */
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
