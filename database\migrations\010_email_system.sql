-- Migración 010: Sistema de Emails y Notificaciones
-- Fecha: 2024-01-01
-- Descripción: Tablas para gestión de emails, cola de envío y notificaciones

-- Tabla para cola de emails
CREATE TABLE IF NOT EXISTS email_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    to_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    template VARCHAR(100) NOT NULL,
    data JSON,
    priority TINYINT DEFAULT 2 COMMENT '1=high, 2=medium, 3=low',
    status ENUM('pending', 'processing', 'sent', 'failed') DEFAULT 'pending',
    attempts TINYINT DEFAULT 0,
    max_attempts TINYINT DEFAULT 3,
    scheduled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_error TEXT,
    
    INDEX idx_status_priority (status, priority),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_to_email (to_email),
    INDEX idx_template (template),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla para log de emails enviados
CREATE TABLE IF NOT EXISTS email_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    to_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    template VARCHAR(100) NOT NULL,
    status ENUM('sent', 'failed', 'bounced') NOT NULL,
    error_message TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_to_email (to_email),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    INDEX idx_template (template)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla para configuración de notificaciones por usuario
CREATE TABLE IF NOT EXISTS user_notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    
    -- Notificaciones por email
    email_enabled BOOLEAN DEFAULT TRUE,
    email_reminders BOOLEAN DEFAULT TRUE,
    email_transactions BOOLEAN DEFAULT FALSE,
    email_monthly_reports BOOLEAN DEFAULT TRUE,
    email_security_alerts BOOLEAN DEFAULT TRUE,
    email_marketing BOOLEAN DEFAULT FALSE,
    
    -- Notificaciones push (para futuro)
    push_enabled BOOLEAN DEFAULT FALSE,
    push_reminders BOOLEAN DEFAULT FALSE,
    push_transactions BOOLEAN DEFAULT FALSE,
    
    -- Configuración de recordatorios
    reminder_advance_days JSON DEFAULT '[1, 3, 7]' COMMENT 'Días de anticipación para recordatorios',
    reminder_time TIME DEFAULT '09:00:00' COMMENT 'Hora preferida para recordatorios',
    
    -- Configuración de reportes
    monthly_report_day TINYINT DEFAULT 1 COMMENT 'Día del mes para enviar reporte (1-28)',
    weekly_summary BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_settings (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla para plantillas de email personalizables
CREATE TABLE IF NOT EXISTS email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    subject VARCHAR(500) NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT,
    variables JSON COMMENT 'Variables disponibles en la plantilla',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla para notificaciones in-app
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL COMMENT 'reminder, transaction, security, system, etc.',
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSON COMMENT 'Datos adicionales específicos del tipo',
    is_read BOOLEAN DEFAULT FALSE,
    is_important BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500),
    action_text VARCHAR(100),
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_unread (user_id, is_read),
    INDEX idx_user_created (user_id, created_at),
    INDEX idx_type (type),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla para tokens de verificación y reset
CREATE TABLE IF NOT EXISTS verification_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    type ENUM('email_verification', 'password_reset', 'email_change') NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_email_type (email, type),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertar configuraciones por defecto para usuarios existentes
INSERT INTO user_notification_settings (user_id)
SELECT id FROM users 
WHERE id NOT IN (SELECT user_id FROM user_notification_settings);

-- Insertar plantillas de email por defecto
INSERT INTO email_templates (name, subject, html_content, text_content, variables) VALUES
('verification', 
 'Verificación de Cuenta - {{app_name}}',
 '<h2>Verificación de Cuenta</h2><p>Hola {{user_name}},</p><p>Haz clic en el siguiente enlace para verificar tu cuenta:</p><a href="{{verification_url}}">Verificar Cuenta</a>',
 'Verificación de Cuenta\n\nHola {{user_name}},\n\nHaz clic en el siguiente enlace para verificar tu cuenta:\n{{verification_url}}',
 '["user_name", "verification_url", "app_name"]'),

('password-reset',
 'Restablecer Contraseña - {{app_name}}',
 '<h2>Restablecer Contraseña</h2><p>Hola {{user_name}},</p><p>Haz clic en el siguiente enlace para restablecer tu contraseña:</p><a href="{{reset_url}}">Restablecer Contraseña</a>',
 'Restablecer Contraseña\n\nHola {{user_name}},\n\nHaz clic en el siguiente enlace para restablecer tu contraseña:\n{{reset_url}}',
 '["user_name", "reset_url", "app_name"]'),

('payment-reminder',
 'Recordatorio de Pago: {{reminder_title}}',
 '<h2>Recordatorio de Pago</h2><p>Hola {{user_name}},</p><p>Te recordamos que tienes un pago próximo:</p><p><strong>{{reminder_title}}</strong></p><p>Fecha de vencimiento: {{due_date}}</p>',
 'Recordatorio de Pago\n\nHola {{user_name}},\n\nTe recordamos que tienes un pago próximo:\n{{reminder_title}}\nFecha de vencimiento: {{due_date}}',
 '["user_name", "reminder_title", "due_date", "amount"]'),

('monthly-report',
 'Reporte Mensual - {{month_year}}',
 '<h2>Tu Reporte Mensual</h2><p>Hola {{user_name}},</p><p>Aquí tienes tu resumen financiero del mes:</p><p>Ingresos: ${{total_income}}</p><p>Gastos: ${{total_expenses}}</p>',
 'Tu Reporte Mensual\n\nHola {{user_name}},\n\nAquí tienes tu resumen financiero del mes:\nIngresos: ${{total_income}}\nGastos: ${{total_expenses}}',
 '["user_name", "month_year", "total_income", "total_expenses"]'),

('security-alert',
 'Alerta de Seguridad - {{app_name}}',
 '<h2>Alerta de Seguridad</h2><p>Hola {{user_name}},</p><p>Hemos detectado actividad sospechosa en tu cuenta:</p><p>{{alert_message}}</p>',
 'Alerta de Seguridad\n\nHola {{user_name}},\n\nHemos detectado actividad sospechosa en tu cuenta:\n{{alert_message}}',
 '["user_name", "alert_message", "ip_address", "timestamp"]');

-- Procedimiento para limpiar emails antiguos
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS CleanupEmailData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Limpiar cola de emails procesados (más de 7 días)
    DELETE FROM email_queue 
    WHERE status IN ('sent', 'failed') 
    AND processed_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

    -- Limpiar log de emails antiguos (más de 90 días)
    DELETE FROM email_log 
    WHERE sent_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

    -- Limpiar notificaciones leídas antiguas (más de 30 días)
    DELETE FROM notifications 
    WHERE is_read = TRUE 
    AND read_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

    -- Limpiar notificaciones expiradas
    DELETE FROM notifications 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW();

    -- Limpiar tokens expirados
    DELETE FROM verification_tokens 
    WHERE expires_at < NOW();

    COMMIT;
END //

DELIMITER ;

-- Función para obtener configuración de notificaciones de usuario
DELIMITER //

CREATE FUNCTION IF NOT EXISTS GetUserNotificationSetting(user_id INT, setting_name VARCHAR(50))
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE setting_value BOOLEAN DEFAULT TRUE;
    
    SELECT 
        CASE setting_name
            WHEN 'email_enabled' THEN email_enabled
            WHEN 'email_reminders' THEN email_reminders
            WHEN 'email_transactions' THEN email_transactions
            WHEN 'email_monthly_reports' THEN email_monthly_reports
            WHEN 'email_security_alerts' THEN email_security_alerts
            WHEN 'email_marketing' THEN email_marketing
            WHEN 'push_enabled' THEN push_enabled
            WHEN 'push_reminders' THEN push_reminders
            WHEN 'push_transactions' THEN push_transactions
            ELSE TRUE
        END INTO setting_value
    FROM user_notification_settings 
    WHERE user_id = user_id
    LIMIT 1;
    
    RETURN COALESCE(setting_value, TRUE);
END //

DELIMITER ;

-- Vista para estadísticas de emails
CREATE OR REPLACE VIEW email_stats AS
SELECT 
    DATE(sent_at) as date,
    template,
    status,
    COUNT(*) as count,
    COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
    ROUND(
        (COUNT(CASE WHEN status = 'sent' THEN 1 END) / COUNT(*)) * 100, 
        2
    ) as success_rate
FROM email_log 
WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(sent_at), template, status
ORDER BY date DESC, template;

-- Vista para notificaciones no leídas por usuario
CREATE OR REPLACE VIEW user_unread_notifications AS
SELECT 
    n.*,
    u.name as user_name,
    u.email as user_email
FROM notifications n
INNER JOIN users u ON n.user_id = u.id
WHERE n.is_read = FALSE 
AND (n.expires_at IS NULL OR n.expires_at > NOW())
ORDER BY n.is_important DESC, n.created_at DESC;

-- Índices adicionales para optimización
CREATE INDEX IF NOT EXISTS idx_email_queue_processing ON email_queue(status, scheduled_at, priority);
CREATE INDEX IF NOT EXISTS idx_notifications_user_type ON notifications(user_id, type, created_at);
CREATE INDEX IF NOT EXISTS idx_verification_tokens_cleanup ON verification_tokens(expires_at, used_at);

-- Trigger para crear configuración de notificaciones para nuevos usuarios
DELIMITER //

CREATE TRIGGER IF NOT EXISTS after_user_insert_notification_settings
AFTER INSERT ON users
FOR EACH ROW
BEGIN
    INSERT INTO user_notification_settings (user_id) 
    VALUES (NEW.id);
END //

DELIMITER ;

-- Comentarios de documentación
/*
Sistema de Emails y Notificaciones:

1. Cola de Emails (email_queue):
   - Gestiona el envío asíncrono de emails
   - Prioridades: 1=alta, 2=media, 3=baja
   - Reintentos automáticos en caso de fallo
   - Programación de envíos

2. Log de Emails (email_log):
   - Registro histórico de todos los emails enviados
   - Estadísticas de entrega y fallos
   - Auditoría completa

3. Configuración de Notificaciones (user_notification_settings):
   - Preferencias personalizables por usuario
   - Control granular de tipos de notificación
   - Configuración de horarios y frecuencias

4. Plantillas de Email (email_templates):
   - Plantillas reutilizables y personalizables
   - Soporte para variables dinámicas
   - Versionado y activación/desactivación

5. Notificaciones In-App (notifications):
   - Sistema de notificaciones dentro de la aplicación
   - Soporte para acciones y expiración
   - Marcado de leído/no leído

6. Tokens de Verificación (verification_tokens):
   - Gestión segura de tokens para verificación
   - Soporte para múltiples tipos de verificación
   - Expiración automática

Funcionalidades:
- Envío de emails con cola y reintentos
- Notificaciones personalizables por usuario
- Plantillas responsivas y profesionales
- Estadísticas y monitoreo de entrega
- Limpieza automática de datos antiguos
- Integración completa con el sistema de usuarios
*/
